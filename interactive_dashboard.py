"""
Dashboard interativo do BitcoinAnalytics com seleção de tokens.
Interface moderna para análise de múltiplas criptomoedas.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import webbrowser
import tempfile
from datetime import datetime

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg


class CryptoAnalyzerGUI:
    """Interface gráfica para análise de criptomoedas."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🪙 BitcoinAnalytics - Analisador Interativo")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1e1e1e')
        
        # Configurar estilo
        self.setup_styles()
        
        # Inicializar gerenciador de dados
        self.data_manager = CryptoDataManager()
        
        # Variáveis
        self.current_symbol = tk.StringVar(value="BTC")
        self.current_period = tk.StringVar(value="3m")
        self.current_data = None
        self.current_analysis = None
        
        # Criar interface
        self.create_widgets()
        
        # Carregar dados iniciais
        self.load_initial_data()
    
    def setup_styles(self):
        """Configura estilos da interface."""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Cores do tema Bitcoin
        bg_color = '#1e1e1e'
        fg_color = '#ffffff'
        accent_color = '#F7931A'
        
        # Configurar estilos
        style.configure('Title.TLabel', 
                       background=bg_color, 
                       foreground=accent_color, 
                       font=('Arial', 16, 'bold'))
        
        style.configure('Subtitle.TLabel', 
                       background=bg_color, 
                       foreground=fg_color, 
                       font=('Arial', 12, 'bold'))
        
        style.configure('Info.TLabel', 
                       background=bg_color, 
                       foreground=fg_color, 
                       font=('Arial', 10))
        
        style.configure('Bitcoin.TButton',
                       background=accent_color,
                       foreground='white',
                       font=('Arial', 10, 'bold'))
    
    def create_widgets(self):
        """Cria os widgets da interface."""
        # Frame principal
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Título
        title_label = ttk.Label(main_frame, 
                               text="🪙 BitcoinAnalytics - Analisador Interativo",
                               style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # Frame de controles
        controls_frame = ttk.LabelFrame(main_frame, text="Configurações", padding=10)
        controls_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Seleção de símbolo
        symbol_frame = ttk.Frame(controls_frame)
        symbol_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(symbol_frame, text="Criptomoeda:", style='Info.TLabel').pack(side=tk.LEFT)
        
        self.symbol_entry = ttk.Entry(symbol_frame, textvariable=self.current_symbol, width=10)
        self.symbol_entry.pack(side=tk.LEFT, padx=(10, 5))
        
        ttk.Button(symbol_frame, text="🔍 Buscar", 
                  command=self.search_symbols).pack(side=tk.LEFT, padx=5)
        
        # Combobox para símbolos populares
        popular_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOT', 'MATIC', 'LINK', 'AVAX', 'UNI']
        self.symbol_combo = ttk.Combobox(symbol_frame, values=popular_symbols, width=8)
        self.symbol_combo.pack(side=tk.LEFT, padx=5)
        self.symbol_combo.bind('<<ComboboxSelected>>', self.on_symbol_selected)
        
        # Seleção de período
        period_frame = ttk.Frame(controls_frame)
        period_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(period_frame, text="Período:", style='Info.TLabel').pack(side=tk.LEFT)
        
        periods = [('1 Dia', '1d'), ('1 Semana', '1w'), ('1 Mês', '1m'), 
                  ('3 Meses', '3m'), ('6 Meses', '6m'), ('1 Ano', '1y'), ('2 Anos', '2y')]
        
        for text, value in periods:
            ttk.Radiobutton(period_frame, text=text, variable=self.current_period, 
                           value=value).pack(side=tk.LEFT, padx=5)
        
        # Botões de ação
        action_frame = ttk.Frame(controls_frame)
        action_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(action_frame, text="📊 Analisar", 
                  command=self.analyze_crypto, style='Bitcoin.TButton').pack(side=tk.LEFT, padx=5)
        
        ttk.Button(action_frame, text="💾 Salvar Análise", 
                  command=self.save_analysis).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(action_frame, text="📋 Ver Histórico", 
                  command=self.show_history).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(action_frame, text="🌐 Dashboard Web", 
                  command=self.open_web_dashboard).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(action_frame, text="🗑️ Limpar Cache", 
                  command=self.cleanup_cache).pack(side=tk.LEFT, padx=5)
        
        # Frame de informações
        self.info_frame = ttk.LabelFrame(main_frame, text="Informações", padding=10)
        self.info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.info_text = tk.Text(self.info_frame, height=8, bg='#2d2d2d', fg='white', 
                                font=('Consolas', 10))
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar para o texto
        scrollbar = ttk.Scrollbar(self.info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_text.config(yscrollcommand=scrollbar.set)
        
        # Frame para gráfico
        self.chart_frame = ttk.LabelFrame(main_frame, text="Gráfico", padding=10)
        self.chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # Status bar
        self.status_var = tk.StringVar(value="Pronto")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM)
    
    def log_message(self, message: str):
        """Adiciona mensagem ao log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.info_text.see(tk.END)
        self.root.update()
    
    def search_symbols(self):
        """Busca símbolos baseado na entrada do usuário."""
        query = self.current_symbol.get().strip()
        if not query:
            messagebox.showwarning("Aviso", "Digite um símbolo para buscar")
            return
        
        self.log_message(f"🔍 Buscando símbolos para: {query}")
        
        try:
            results = self.data_manager.search_symbols(query)
            
            if not results:
                self.log_message("❌ Nenhum símbolo encontrado")
                return
            
            # Mostrar resultados
            result_window = tk.Toplevel(self.root)
            result_window.title("Resultados da Busca")
            result_window.geometry("500x400")
            result_window.configure(bg='#1e1e1e')
            
            # Lista de resultados
            listbox = tk.Listbox(result_window, bg='#2d2d2d', fg='white', 
                               font=('Arial', 10))
            listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            for result in results:
                if 'variants' in result:
                    text = f"{result['symbol']} - {', '.join(result['variants'])}"
                else:
                    text = f"{result['symbol']} - {result.get('name', 'N/A')}"
                listbox.insert(tk.END, text)
            
            def select_symbol():
                selection = listbox.curselection()
                if selection:
                    selected = results[selection[0]]
                    self.current_symbol.set(selected['symbol'])
                    result_window.destroy()
                    self.log_message(f"✅ Símbolo selecionado: {selected['symbol']}")
            
            ttk.Button(result_window, text="Selecionar", 
                      command=select_symbol).pack(pady=10)
            
        except Exception as e:
            self.log_message(f"❌ Erro na busca: {e}")
    
    def on_symbol_selected(self, event):
        """Callback quando símbolo é selecionado no combobox."""
        selected = self.symbol_combo.get()
        if selected:
            self.current_symbol.set(selected)
            self.log_message(f"✅ Símbolo selecionado: {selected}")
    
    def analyze_crypto(self):
        """Executa análise da criptomoeda selecionada."""
        symbol = self.current_symbol.get().strip().upper()
        period = self.current_period.get()
        
        if not symbol:
            messagebox.showwarning("Aviso", "Digite um símbolo para analisar")
            return
        
        # Executar em thread separada para não travar a interface
        thread = threading.Thread(target=self._analyze_crypto_thread, 
                                 args=(symbol, period))
        thread.daemon = True
        thread.start()
    
    def _analyze_crypto_thread(self, symbol: str, period: str):
        """Thread para análise da criptomoeda."""
        try:
            self.status_var.set(f"Analisando {symbol}...")
            self.log_message(f"📊 Iniciando análise de {symbol} ({period})")
            
            # Buscar dados
            self.log_message("📥 Buscando dados...")
            data = self.data_manager.fetch_crypto_data(symbol, period)
            
            if data is None or len(data) == 0:
                self.log_message("❌ Não foi possível obter dados")
                self.status_var.set("Erro")
                return
            
            self.current_data = data
            self.log_message(f"✅ Dados obtidos: {len(data)} registros")
            
            # Análise técnica
            self.log_message("🔍 Executando análise técnica...")
            analyzer = TechnicalAnalyzer(data)
            indicators = analyzer.calculate_all_indicators()
            summary = analyzer.get_market_summary()
            
            self.current_analysis = {
                'symbol': symbol,
                'period': period,
                'data': indicators,
                'summary': summary,
                'timestamp': datetime.now()
            }
            
            # Atualizar interface na thread principal
            self.root.after(0, self._update_analysis_display)
            
        except Exception as e:
            self.log_message(f"❌ Erro na análise: {e}")
            self.status_var.set("Erro")
    
    def _update_analysis_display(self):
        """Atualiza a exibição da análise na interface."""
        if not self.current_analysis:
            return
        
        summary = self.current_analysis['summary']
        symbol = self.current_analysis['symbol']
        
        # Log dos resultados
        self.log_message(f"📈 Análise de {symbol} concluída!")
        self.log_message(f"💰 Preço atual: ${summary['current_price']:,.2f}")
        self.log_message(f"📊 Tendência: {summary['overall_trend']}")
        self.log_message(f"📉 RSI: {summary.get('rsi', 0):.2f}")
        
        self.log_message("🎯 Sinais de Trading:")
        for indicator, signal in summary['signals'].items():
            emoji = "🟢" if signal == "COMPRA" else "🔴" if signal == "VENDA" else "🟡"
            self.log_message(f"   {emoji} {indicator}: {signal}")
        
        # Criar gráfico
        self._create_chart()
        
        self.status_var.set(f"Análise de {symbol} concluída")
    
    def _create_chart(self):
        """Cria gráfico da análise."""
        if not self.current_analysis:
            return
        
        # Limpar frame anterior
        for widget in self.chart_frame.winfo_children():
            widget.destroy()
        
        data = self.current_analysis['data'].tail(200)  # Últimos 200 pontos
        summary = self.current_analysis['summary']
        symbol = self.current_analysis['symbol']
        
        # Criar figura
        plt.style.use('dark_background')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), 
                                      gridspec_kw={'height_ratios': [3, 1]})
        
        # Gráfico de preços
        ax1.plot(data.index, data['Close'], color='#F7931A', linewidth=2, label=f'{symbol}/USDT')
        
        if 'SMA_20' in data.columns:
            ax1.plot(data.index, data['SMA_20'], color='cyan', alpha=0.7, label='SMA 20')
        
        if 'SMA_50' in data.columns:
            ax1.plot(data.index, data['SMA_50'], color='yellow', alpha=0.7, label='SMA 50')
        
        ax1.set_title(f'{symbol} - ${summary["current_price"]:,.2f} | {summary["overall_trend"]}', 
                     fontsize=14, fontweight='bold', color='#F7931A')
        ax1.set_ylabel('Preço (USDT)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Gráfico de volume
        ax2.bar(data.index, data['Volume'], color='#F7931A', alpha=0.6)
        ax2.set_title('Volume')
        ax2.set_ylabel('Volume')
        ax2.grid(True, alpha=0.3)
        
        # Formatação das datas
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            ax.xaxis.set_major_locator(mdates.HourLocator(interval=max(1, len(data)//10)))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # Adicionar à interface
        canvas = FigureCanvasTkAgg(fig, self.chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def save_analysis(self):
        """Salva a análise atual."""
        if not self.current_analysis:
            messagebox.showwarning("Aviso", "Nenhuma análise para salvar")
            return
        
        try:
            self.data_manager.save_analysis(
                self.current_analysis['symbol'],
                'technical_analysis',
                self.current_analysis['summary']
            )
            
            self.log_message(f"💾 Análise de {self.current_analysis['symbol']} salva!")
            messagebox.showinfo("Sucesso", "Análise salva com sucesso!")
            
        except Exception as e:
            self.log_message(f"❌ Erro ao salvar: {e}")
            messagebox.showerror("Erro", f"Erro ao salvar análise: {e}")
    
    def show_history(self):
        """Mostra histórico de análises."""
        try:
            analyses = self.data_manager.get_saved_analyses()
            
            if not analyses:
                messagebox.showinfo("Histórico", "Nenhuma análise salva encontrada")
                return
            
            # Janela de histórico
            history_window = tk.Toplevel(self.root)
            history_window.title("Histórico de Análises")
            history_window.geometry("800x600")
            history_window.configure(bg='#1e1e1e')
            
            # Treeview para mostrar análises
            columns = ('Símbolo', 'Tipo', 'Preço', 'Tendência', 'Data')
            tree = ttk.Treeview(history_window, columns=columns, show='headings')
            
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=150)
            
            # Adicionar dados
            for analysis in analyses:
                data = analysis['analysis_data']
                tree.insert('', tk.END, values=(
                    analysis['symbol'],
                    analysis['analysis_type'],
                    f"${data.get('current_price', 0):,.2f}",
                    data.get('overall_trend', 'N/A'),
                    analysis['created_at'][:19]
                ))
            
            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            self.log_message(f"📋 Histórico carregado: {len(analyses)} análises")
            
        except Exception as e:
            self.log_message(f"❌ Erro ao carregar histórico: {e}")
            messagebox.showerror("Erro", f"Erro ao carregar histórico: {e}")
    
    def open_web_dashboard(self):
        """Abre dashboard web com a análise atual."""
        if not self.current_analysis:
            messagebox.showwarning("Aviso", "Execute uma análise primeiro")
            return
        
        try:
            # Criar dashboard web temporário
            self._create_web_dashboard()
            
        except Exception as e:
            self.log_message(f"❌ Erro ao criar dashboard web: {e}")
            messagebox.showerror("Erro", f"Erro ao criar dashboard: {e}")
    
    def _create_web_dashboard(self):
        """Cria dashboard web temporário."""
        analysis = self.current_analysis
        summary = analysis['summary']
        symbol = analysis['symbol']
        
        # Criar HTML
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{symbol} - BitcoinAnalytics</title>
            <meta charset="UTF-8">
            <style>
                body {{ background: #1e1e1e; color: white; font-family: Arial; margin: 20px; }}
                .header {{ text-align: center; margin-bottom: 30px; }}
                .card {{ background: #2d2d2d; padding: 20px; margin: 10px 0; border-radius: 10px; }}
                .price {{ font-size: 2em; color: #F7931A; font-weight: bold; }}
                .trend {{ font-size: 1.5em; margin: 10px 0; }}
                .signals {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }}
                .signal {{ background: #3d3d3d; padding: 15px; border-radius: 5px; text-align: center; }}
                .buy {{ border-left: 5px solid #28a745; }}
                .sell {{ border-left: 5px solid #dc3545; }}
                .neutral {{ border-left: 5px solid #ffc107; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🪙 {symbol} - Análise Técnica</h1>
                <div class="price">${summary['current_price']:,.2f} USDT</div>
                <div class="trend">Tendência: {summary['overall_trend']}</div>
            </div>
            
            <div class="card">
                <h3>📊 Indicadores Principais</h3>
                <p><strong>RSI (14):</strong> {summary.get('rsi', 0):.2f}</p>
                <p><strong>Sinais Ativos:</strong> {summary['signal_count']['buy']} Compra, 
                   {summary['signal_count']['sell']} Venda, {summary['signal_count']['neutral']} Neutro</p>
            </div>
            
            <div class="card">
                <h3>🎯 Sinais de Trading</h3>
                <div class="signals">
        """
        
        for indicator, signal in summary['signals'].items():
            css_class = 'buy' if signal == 'COMPRA' else 'sell' if signal == 'VENDA' else 'neutral'
            emoji = '🟢' if signal == 'COMPRA' else '🔴' if signal == 'VENDA' else '🟡'
            
            html_content += f"""
                    <div class="signal {css_class}">
                        <div>{emoji}</div>
                        <div><strong>{indicator}</strong></div>
                        <div>{signal}</div>
                    </div>
            """
        
        html_content += f"""
                </div>
            </div>
            
            <div class="card">
                <h3>ℹ️ Informações</h3>
                <p><strong>Análise gerada em:</strong> {analysis['timestamp'].strftime('%d/%m/%Y %H:%M:%S')}</p>
                <p><strong>Período analisado:</strong> {analysis['period']}</p>
                <p><strong>Total de dados:</strong> {len(analysis['data']):,} registros</p>
            </div>
            
            <script>
                setTimeout(() => location.reload(), 300000); // Refresh a cada 5 min
            </script>
        </body>
        </html>
        """
        
        # Salvar e abrir
        temp_file = os.path.join(tempfile.gettempdir(), f'{symbol}_analysis.html')
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        webbrowser.open(f'file:///{temp_file}')
        self.log_message(f"🌐 Dashboard web aberto para {symbol}")
    
    def cleanup_cache(self):
        """Limpa cache antigo."""
        try:
            self.data_manager.cleanup_old_data(days_to_keep=7)
            
            stats = self.data_manager.get_database_stats()
            self.log_message(f"🗑️ Cache limpo! Tamanho atual: {stats['file_size_mb']:.2f} MB")
            
            messagebox.showinfo("Limpeza", "Cache limpo com sucesso!")
            
        except Exception as e:
            self.log_message(f"❌ Erro na limpeza: {e}")
            messagebox.showerror("Erro", f"Erro na limpeza: {e}")
    
    def load_initial_data(self):
        """Carrega dados iniciais."""
        self.log_message("🚀 BitcoinAnalytics iniciado!")
        
        # Mostrar estatísticas do banco
        stats = self.data_manager.get_database_stats()
        self.log_message(f"📊 Banco de dados: {stats['symbols']} símbolos, "
                        f"{stats['data_records']} registros, "
                        f"{stats['file_size_mb']:.2f} MB")
        
        self.log_message("💡 Dica: Digite um símbolo (ex: BTC, ETH) e clique em Analisar")
    
    def run(self):
        """Executa a aplicação."""
        self.root.mainloop()


if __name__ == "__main__":
    try:
        app = CryptoAnalyzerGUI()
        app.run()
    except Exception as e:
        print(f"Erro ao iniciar aplicação: {e}")
        import traceback
        traceback.print_exc()
