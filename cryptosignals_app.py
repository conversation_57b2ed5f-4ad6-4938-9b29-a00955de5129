"""
CryptoSignals - Aplicação SaaS Completa
Landing Page + Dashboard + Sistema de Pagamentos
"""

import sys
import os
import json
import sqlite3
from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for, send_file
import threading
import webbrowser
from datetime import datetime, timedelta
from functools import wraps
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.utils

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer
from visualization import BitcoinVisualizer

# Importar sistema SaaS
from saas_auth import SaaSAuthManager, PlanType
from saas_payments import (
    TetherPaymentProcessor, TANOSIntegration, MultiNetworkPaymentProcessor,
    PaymentMethod, PaymentNetwork, PaymentStatus
)

# Importar novos sistemas
from notification_system import notification_system, NotificationType, AlertLevel
from sentiment_analysis import sentiment_analyzer, SentimentScore
from backtesting_engine import backtesting_engine, StrategyParameters, StrategyType

# Importar sistemas avançados
from cache_manager import cache_manager, CryptoCache, UserCache
from rate_limiter import rate_limiter, rate_limited
from backup_manager import backup_manager
from monitoring import monitor, track_operation

# Importar sistemas de elite
from ai_prediction_engine import ai_engine, PredictionModel, PredictionTimeframe
from realtime_websocket import realtime_manager, MessageType, SubscriptionType
from push_notification_system import push_system, NotificationChannel, NotificationPriority
from user_analytics import user_analytics, EventType

# Importar sistemas finais
from affiliate_system import affiliate_system, AffiliateStatus, CommissionStatus, PayoutMethod
from exchange_integrations import exchange_manager, ExchangeType, MarketData
from advanced_security import (
    captcha_validator, two_factor_manager, oauth_manager, security_logger,
    SecurityEventType, TwoFactorType, OAuthProvider
)

# Inicializar Flask
app = Flask(__name__)
app.secret_key = "cryptosignals_premium_2024"

# Inicializar sistemas
data_manager = CryptoDataManager()
auth_manager = SaaSAuthManager()

# Configurar wallets para múltiplas redes
wallet_addresses = {
    'ethereum': '******************************************',
    'bsc': '******************************************',  # Mesmo endereço para BSC
    'polygon': '******************************************'  # Mesmo endereço para Polygon
}

# Inicializar processadores de pagamento
multi_payment_processor = MultiNetworkPaymentProcessor(wallet_addresses)
payment_processor = TetherPaymentProcessor("******************************************")  # Compatibilidade
tanos_integration = TANOSIntegration()

# Cache global
cache = {
    'current_symbol': 'BTC',
    'current_period': '3m',
    'current_data': None,
    'current_analysis': None,
    'last_update': None
}

# Decoradores
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': 'Login required', 'redirect': '/login'})
        return f(*args, **kwargs)
    return decorated_function

def check_plan_limits(action):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return jsonify({'success': False, 'error': 'Login required'})

            user = auth_manager.get_user_by_id(session['user_id'])
            if not user:
                return jsonify({'success': False, 'error': 'User not found'})

            if not auth_manager.check_user_limits(user, action):
                return jsonify({
                    'success': False,
                    'error': 'Plan limit exceeded',
                    'upgrade_required': True
                })

            auth_manager.log_api_usage(user.id, request.endpoint, request.remote_addr)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Rotas principais
@app.route('/')
def landing_page():
    """Landing Page Principal"""
    # Se usuário já está logado, redirecionar para dashboard
    if 'user_id' in session:
        return redirect('/dashboard')

    # Carregar template da landing page
    with open('templates/landing.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template)

@app.route('/dashboard')
@login_required
def dashboard():
    """Dashboard Principal"""
    user = auth_manager.get_user_by_id(session['user_id'])
    if not user:
        return redirect('/')

    # Carregar template do dashboard
    with open('templates/dashboard.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template, user=user)

@app.route('/admin')
@login_required
def admin_dashboard():
    """Dashboard Administrativo"""
    user = auth_manager.get_user_by_id(session['user_id'])
    if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
        return redirect('/dashboard')

    # Carregar template do dashboard administrativo
    with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template, user=user)

@app.route('/login')
def login_page():
    """Página de Login"""
    if 'user_id' in session:
        return redirect('/dashboard')

    with open('templates/login.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template)

@app.route('/register')
def register_page():
    """Página de Registro"""
    if 'user_id' in session:
        return redirect('/dashboard')

    with open('templates/register.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template)

# Rotas de autenticação
@app.route('/auth/status')
def auth_status():
    """Verifica o status de autenticação do usuário"""
    if 'user_id' in session:
        user = auth_manager.get_user_by_id(session['user_id'])
        if user:
            return jsonify({
                'authenticated': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'plan': user.plan.value,
                    'subscription_end': user.subscription_end.isoformat()
                }
            })

    return jsonify({'authenticated': False})

@app.route('/auth/login', methods=['POST'])
def login():
    """Login do usuário"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')

        user = auth_manager.authenticate_user(email, password)
        if user:
            session['user_id'] = user.id
            return jsonify({
                'success': True,
                'redirect': '/dashboard',
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'plan': user.plan.value
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Credenciais inválidas'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth/register', methods=['POST'])
def register():
    """Registro de novo usuário"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        plan = data.get('plan', 'free')

        plan_enum = PlanType(plan)
        user = auth_manager.create_user(email, password, plan_enum)

        if user:
            session['user_id'] = user.id
            return jsonify({
                'success': True,
                'redirect': '/dashboard',
                'plan': plan,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'plan': user.plan.value
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Erro ao criar usuário'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth/logout', methods=['POST'])
def logout():
    """Logout do usuário"""
    session.pop('user_id', None)
    return jsonify({'success': True, 'redirect': '/'})

# Rotas de análise
@app.route('/analyze', methods=['POST'])
@check_plan_limits('api_call')
@track_operation('crypto_analysis')
def analyze():
    """Analisa uma criptomoeda com cache inteligente"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'BTC').upper()
        period = data.get('period', '3m')

        # Verificar cache primeiro
        cached_analysis = CryptoCache.get_analysis(symbol, period)
        if cached_analysis:
            monitor.record_operation('cache_hit', 5.0, True)
            return jsonify({
                'success': True,
                'analysis': cached_analysis,
                'cached': True
            })

        # Verificar cache de dados de preço
        crypto_data = CryptoCache.get_price_data(symbol, period)
        if not crypto_data:
            crypto_data = data_manager.fetch_crypto_data(symbol, period)
            if crypto_data is not None and len(crypto_data) > 0:
                CryptoCache.set_price_data(symbol, period, crypto_data.to_dict())

        if crypto_data is None or len(crypto_data) == 0:
            return jsonify({'success': False, 'error': 'Não foi possível obter dados'})

        analyzer = TechnicalAnalyzer(crypto_data)
        indicators_df = analyzer.calculate_all_indicators()
        summary = analyzer.get_market_summary()

        visualizer = BitcoinVisualizer(indicators_df)

        charts = {}
        try:
            candlestick_fig = visualizer.create_candlestick_chart(analyzer.indicators, height=500)
            charts['candlestick'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(candlestick_fig))

            volume_fig = visualizer.create_volume_chart(height=200)
            charts['volume'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(volume_fig))

            indicators_fig = visualizer.create_indicators_subplot(analyzer.indicators)
            charts['indicators'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(indicators_fig))

            volatility_fig = visualizer.create_volatility_chart()
            charts['volatility'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(volatility_fig))

        except Exception as chart_error:
            print(f"Erro ao gerar gráficos: {chart_error}")
            charts = {}

        def convert_to_serializable(obj):
            import numpy as np
            import pandas as pd

            if obj is None:
                return None
            elif isinstance(obj, (np.integer, np.floating)):
                return obj.item()
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, pd.Series):
                return obj.tolist()
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict('records')
            elif hasattr(obj, 'isoformat'):
                return obj.isoformat()
            elif isinstance(obj, (int, float, str, bool, list, dict)):
                return obj
            else:
                try:
                    return float(obj)
                except (ValueError, TypeError):
                    return str(obj)

        serializable_summary = {}
        for key, value in summary.items():
            if isinstance(value, dict):
                serializable_summary[key] = {k: convert_to_serializable(v) for k, v in value.items()}
            else:
                serializable_summary[key] = convert_to_serializable(value)

        # Criar análise completa
        analysis_result = {
            'symbol': symbol,
            'period': period,
            'summary': serializable_summary,
            'data_count': len(crypto_data),
            'timestamp': datetime.now().isoformat(),
            'charts': charts,
            'cached': False
        }

        # Salvar no cache
        CryptoCache.set_analysis(symbol, period, analysis_result)

        # Atualizar cache global (manter compatibilidade)
        cache['current_symbol'] = symbol
        cache['current_period'] = period
        cache['current_data'] = crypto_data
        cache['current_analysis'] = analysis_result
        cache['last_update'] = datetime.now()

        return jsonify({
            'success': True,
            'analysis': analysis_result
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de análise avançada
@app.route('/sentiment/<symbol>')
@check_plan_limits('sentiment_analysis')
def get_sentiment_analysis(symbol):
    """Análise de sentimento para um símbolo"""
    try:
        # Verificar cache primeiro
        cached_sentiment = sentiment_analyzer.get_cached_sentiment(symbol.upper())

        if cached_sentiment:
            return jsonify({
                'success': True,
                'sentiment': cached_sentiment,
                'cached': True
            })

        # Realizar nova análise
        analysis = sentiment_analyzer.analyze_symbol_sentiment(symbol.upper())

        return jsonify({
            'success': True,
            'sentiment': {
                'overall_sentiment': analysis.overall_sentiment.name,
                'sentiment_score': analysis.sentiment_score,
                'confidence': analysis.confidence,
                'news_count': analysis.news_count,
                'positive_news': analysis.positive_news,
                'negative_news': analysis.negative_news,
                'neutral_news': analysis.neutral_news,
                'trending_keywords': analysis.trending_keywords,
                'market_mood': analysis.market_mood,
                'recommendation': analysis.recommendation,
                'last_updated': analysis.analysis_timestamp.isoformat()
            },
            'cached': False
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/backtest', methods=['POST'])
@check_plan_limits('backtesting')
def run_backtest():
    """Executa um backtest de estratégia"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'BTC').upper()
        strategy_type = data.get('strategy_type', 'sma_crossover')
        strategy_params = data.get('parameters', {})
        initial_capital = data.get('initial_capital', 10000.0)

        # Buscar dados históricos
        crypto_data = data_manager.fetch_crypto_data(symbol, '1h', limit=1000)

        if crypto_data is None or len(crypto_data) == 0:
            return jsonify({'success': False, 'error': 'Dados insuficientes para backtest'})

        # Configurar estratégia
        strategy_config = StrategyParameters(
            strategy_type=StrategyType(strategy_type),
            parameters=strategy_params,
            stop_loss_percent=data.get('stop_loss_percent'),
            take_profit_percent=data.get('take_profit_percent'),
            position_size_percent=data.get('position_size_percent', 100.0)
        )

        # Executar backtest
        result = backtesting_engine.run_backtest(
            crypto_data, strategy_config, initial_capital, symbol
        )

        return jsonify({
            'success': True,
            'backtest': {
                'strategy_name': result.strategy_name,
                'symbol': result.symbol,
                'total_return_percent': result.total_return_percent,
                'total_trades': result.total_trades,
                'winning_trades': result.winning_trades,
                'losing_trades': result.losing_trades,
                'win_rate': result.win_rate,
                'max_drawdown': result.max_drawdown,
                'sharpe_ratio': result.sharpe_ratio,
                'profit_factor': result.profit_factor,
                'equity_curve': result.equity_curve[-50:],  # Últimos 50 pontos
                'start_date': result.start_date.isoformat(),
                'end_date': result.end_date.isoformat()
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/alerts', methods=['GET', 'POST'])
@login_required
def manage_alerts():
    """Gerenciar alertas do usuário"""
    user = auth_manager.get_user_by_id(session['user_id'])

    if request.method == 'POST':
        try:
            data = request.get_json()
            symbol = data.get('symbol', '').upper()
            alert_type = data.get('alert_type', 'price')
            condition = data.get('condition', 'above')
            target_value = float(data.get('target_value', 0))

            # Buscar preço atual
            current_data = data_manager.fetch_crypto_data(symbol, '1m', limit=1)
            current_price = current_data.iloc[-1]['close'] if current_data is not None and len(current_data) > 0 else 0

            # Criar alerta
            alert_id = notification_system.create_alert(
                user.id, symbol, alert_type, condition, target_value, current_price
            )

            if alert_id:
                return jsonify({
                    'success': True,
                    'alert_id': alert_id,
                    'message': 'Alerta criado com sucesso'
                })
            else:
                return jsonify({'success': False, 'error': 'Erro ao criar alerta'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})

    else:
        # GET - Listar alertas do usuário
        # Implementar busca de alertas do usuário
        return jsonify({
            'success': True,
            'alerts': []  # Implementar busca real
        })

# Rotas de pagamento
@app.route('/plans')
def get_plans():
    """Retorna informações dos planos"""
    plans_info = {}
    for plan_type, plan in auth_manager.plans.items():
        plans_info[plan_type.value] = {
            'name': plan.name,
            'price_monthly': plan.price_monthly,
            'price_annual': plan.price_annual,
            'features': plan.features,
            'limits': plan.limits
        }

    return jsonify(plans_info)

# Rotas de pagamento multi-rede
@app.route('/payment/methods')
def get_payment_methods():
    """Retorna métodos de pagamento suportados"""
    try:
        methods = multi_payment_processor.get_supported_payment_methods()
        return jsonify({
            'success': True,
            'payment_methods': methods
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/payment/create', methods=['POST'])
@login_required
def create_payment():
    """Cria uma solicitação de pagamento multi-rede"""
    try:
        data = request.get_json()
        plan = data.get('plan')
        annual = data.get('annual', False)
        payment_method = data.get('payment_method', 'usdt_eth')

        user = auth_manager.get_user_by_id(session['user_id'])
        if not user:
            return jsonify({'success': False, 'error': 'Usuário não encontrado'})

        plan_enum = PlanType(plan)
        payment_info = auth_manager.get_payment_info(plan_enum, annual)

        # Usar novo processador multi-rede
        try:
            payment_method_enum = PaymentMethod(payment_method)
            payment = multi_payment_processor.create_payment_request(
                user.id, plan, payment_info['amount'], payment_method_enum, annual
            )
        except ValueError:
            # Fallback para processador antigo se método não suportado
            payment = payment_processor.create_payment_request(
                user.id, plan, payment_info['amount'], annual
            )

        # Gerar QR code para pagamento
        qr_data = multi_payment_processor.generate_payment_qr_code(payment)

        return jsonify({
            'success': True,
            'payment': {
                'id': payment.id,
                'amount': payment.amount,
                'currency': payment.currency,
                'network': payment.network.value,
                'payment_method': payment.payment_method.value,
                'wallet_address': payment.wallet_address,
                'expires_at': payment.expires_at.isoformat(),
                'qr_data': qr_data
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/payment/verify', methods=['POST'])
@login_required
def verify_payment():
    """Verifica um pagamento multi-rede"""
    try:
        data = request.get_json()
        payment_id = data.get('payment_id')
        tx_hash = data.get('tx_hash')

        # Tentar verificar com processador multi-rede primeiro
        verified = False
        try:
            verified = multi_payment_processor.verify_payment(payment_id, tx_hash)
        except:
            # Fallback para processador antigo
            verified = payment_processor.verify_payment(payment_id, tx_hash)

        if verified:
            # Processar upgrade da assinatura
            try:
                payment = multi_payment_processor.get_payment(payment_id)
            except:
                payment = payment_processor.get_payment(payment_id)

            if payment:
                # Atualizar plano do usuário
                conn = sqlite3.connect("saas_users.db")
                cursor = conn.cursor()

                # Calcular nova data de expiração
                months = 12 if payment.metadata and payment.metadata.get('annual') else 1
                new_expiry = datetime.now() + timedelta(days=30 * months)

                cursor.execute("""
                    UPDATE users
                    SET plan = ?, subscription_end = ?
                    WHERE id = ?
                """, (payment.plan, new_expiry.isoformat(), payment.user_id))

                conn.commit()
                conn.close()

                return jsonify({'success': True, 'message': 'Pagamento confirmado!'})

        return jsonify({'success': False, 'error': 'Pagamento não confirmado'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/payment/methods-page')
def payment_methods_page():
    """Página de demonstração dos métodos de pagamento"""
    try:
        with open('templates/payment_methods.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "Página de métodos de pagamento não encontrada", 404

# Rotas de IA e Predições
@app.route('/ai/predict', methods=['POST'])
@login_required
@track_operation('ai_prediction')
def ai_predict():
    """Predição com IA"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan == PlanType.FREE:
            return jsonify({'error': 'Predições IA disponíveis apenas para planos pagos'}), 403

        data = request.get_json()
        symbol = data.get('symbol', 'BTC').upper()
        model_type = PredictionModel(data.get('model', 'ensemble'))
        timeframe = PredictionTimeframe(data.get('timeframe', '1h'))

        # Obter dados históricos
        crypto_data = data_manager.fetch_crypto_data(symbol, '1h', limit=500)
        if crypto_data is None or len(crypto_data) < 100:
            return jsonify({'success': False, 'error': 'Dados insuficientes para predição'})

        # Adicionar dados para treinamento
        ai_engine.add_training_data(symbol, crypto_data)

        # Fazer predição
        prediction = ai_engine.predict(symbol, crypto_data, model_type, timeframe)

        if prediction:
            # Registrar evento
            user_analytics.track_event(
                user.id, EventType.PREDICTION_VIEW, session.get('session_id', 'unknown'),
                request.remote_addr, request.headers.get('User-Agent', ''),
                {'symbol': symbol, 'model': model_type.value, 'timeframe': timeframe.value}
            )

            # Broadcast predição em tempo real
            realtime_manager.add_prediction(symbol, {
                'predicted_price': prediction.predicted_price,
                'confidence': prediction.confidence,
                'direction': prediction.direction,
                'percentage_change': prediction.percentage_change,
                'model_type': prediction.model_type.value,
                'timeframe': prediction.timeframe.value
            })

            return jsonify({
                'success': True,
                'prediction': {
                    'symbol': prediction.symbol,
                    'predicted_price': prediction.predicted_price,
                    'confidence': prediction.confidence,
                    'direction': prediction.direction,
                    'percentage_change': prediction.percentage_change,
                    'model_type': prediction.model_type.value,
                    'timeframe': prediction.timeframe.value,
                    'timestamp': prediction.timestamp.isoformat(),
                    'support_levels': prediction.support_levels,
                    'resistance_levels': prediction.resistance_levels
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Falha na predição'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/ai/train', methods=['POST'])
@login_required
def ai_train():
    """Treina modelos de IA"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Treinamento disponível apenas para planos Professional/Enterprise'}), 403

        data = request.get_json()
        symbol = data.get('symbol')

        # Treinar modelos
        results = ai_engine.train_models(symbol)

        return jsonify({
            'success': True,
            'training_results': {
                model_type.value: {
                    'accuracy': metrics.accuracy,
                    'last_trained': metrics.last_trained.isoformat(),
                    'training_samples': metrics.training_samples
                }
                for model_type, metrics in results.items()
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/ai/status')
@login_required
def ai_status():
    """Status dos modelos de IA"""
    try:
        status = ai_engine.get_model_status()
        return jsonify({
            'success': True,
            'ai_status': status
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de Analytics de Usuário
@app.route('/analytics/user')
@login_required
def user_analytics_data():
    """Analytics do usuário atual"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        period_days = int(request.args.get('period', 30))

        metrics = user_analytics.get_user_metrics(user.id, period_days)
        insights = user_analytics.generate_user_insights(user.id)

        return jsonify({
            'success': True,
            'metrics': metrics,
            'insights': [
                {
                    'type': insight.insight_type,
                    'title': insight.title,
                    'description': insight.description,
                    'confidence': insight.confidence,
                    'data': insight.data
                }
                for insight in insights
            ]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/analytics/platform')
@login_required
def platform_analytics():
    """Analytics da plataforma (admin)"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        period_days = int(request.args.get('period', 7))
        metrics = user_analytics.get_platform_metrics(period_days)

        return jsonify({
            'success': True,
            'platform_metrics': metrics
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de Notificações
@app.route('/notifications/send', methods=['POST'])
@login_required
def send_notification():
    """Envia notificação personalizada"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        data = request.get_json()

        channel = NotificationChannel(data.get('channel', 'in_app'))
        priority = NotificationPriority(data.get('priority', 'normal'))
        subject = data.get('subject', 'Notificação CryptoSignals')
        message = data.get('message', '')
        recipient = data.get('recipient', user.email)

        notification_id = push_system.send_notification(
            user_id=user.id,
            channel=channel,
            subject=subject,
            message=message,
            recipient=recipient,
            priority=priority
        )

        return jsonify({
            'success': True,
            'notification_id': notification_id
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/notifications/status/<notification_id>')
@login_required
def notification_status(notification_id):
    """Status de uma notificação"""
    try:
        result = push_system.get_notification_status(notification_id)

        if result:
            return jsonify({
                'success': True,
                'status': {
                    'notification_id': result.notification_id,
                    'status': result.status.value,
                    'sent_at': result.sent_at.isoformat() if result.sent_at else None,
                    'delivered_at': result.delivered_at.isoformat() if result.delivered_at else None,
                    'error_message': result.error_message
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Notificação não encontrada'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de Sistema de Afiliados
@app.route('/affiliate/join', methods=['POST'])
@login_required
def join_affiliate_program():
    """Ingressar no programa de afiliados"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])

        # Verificar se já é afiliado
        existing_affiliate = affiliate_system.get_affiliate_by_user(user.id)
        if existing_affiliate:
            return jsonify({'success': False, 'error': 'Usuário já é afiliado'})

        # Criar afiliado
        affiliate = affiliate_system.create_affiliate(user.id)

        return jsonify({
            'success': True,
            'affiliate': {
                'code': affiliate.affiliate_code,
                'commission_rate': affiliate.commission_rate,
                'status': affiliate.status.value
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/affiliate/stats')
@login_required
def affiliate_stats():
    """Estatísticas do afiliado"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        affiliate = affiliate_system.get_affiliate_by_user(user.id)

        if not affiliate:
            return jsonify({'success': False, 'error': 'Usuário não é afiliado'})

        stats = affiliate_system.get_affiliate_stats(affiliate.id)

        return jsonify({
            'success': True,
            'affiliate_info': {
                'code': affiliate.affiliate_code,
                'commission_rate': affiliate.commission_rate,
                'total_earnings': float(affiliate.total_earnings),
                'pending_earnings': float(affiliate.pending_earnings),
                'paid_earnings': float(affiliate.paid_earnings)
            },
            'stats': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de Exchange Integrations
@app.route('/market/overview')
def market_overview():
    """Visão geral do mercado"""
    try:
        market_data = exchange_manager.get_market_overview()

        return jsonify({
            'success': True,
            'market_data': [
                {
                    'symbol': data.symbol,
                    'price': data.price,
                    'change_24h': data.change_24h,
                    'volume_24h': data.volume_24h,
                    'exchange': data.exchange.value
                }
                for data in market_data
            ]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/market/price/<symbol>')
def get_market_price(symbol):
    """Obtém preço de um símbolo"""
    try:
        # Primeiro tentar cache
        cached_data = exchange_manager.get_cached_data(symbol.upper())
        if cached_data:
            return jsonify({
                'success': True,
                'data': {
                    'symbol': cached_data.symbol,
                    'price': cached_data.price,
                    'change_24h': cached_data.change_24h,
                    'volume_24h': cached_data.volume_24h,
                    'timestamp': cached_data.timestamp.isoformat(),
                    'cached': True
                }
            })

        # Buscar dados atualizados
        market_data = exchange_manager.get_best_price(symbol.upper())
        if market_data:
            return jsonify({
                'success': True,
                'data': {
                    'symbol': market_data.symbol,
                    'price': market_data.price,
                    'change_24h': market_data.change_24h,
                    'volume_24h': market_data.volume_24h,
                    'timestamp': market_data.timestamp.isoformat(),
                    'cached': False
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Dados não encontrados'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de Segurança Avançada
@app.route('/security/2fa/setup', methods=['POST'])
@login_required
def setup_2fa():
    """Configurar 2FA"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])

        # Verificar se já tem 2FA habilitado
        if two_factor_manager.is_2fa_enabled(user.id):
            return jsonify({'success': False, 'error': '2FA já está habilitado'})

        # Configurar TOTP
        qr_code, backup_codes = two_factor_manager.setup_totp(user.id, user.email)

        return jsonify({
            'success': True,
            'qr_code': qr_code,
            'backup_codes': backup_codes,
            'message': 'Escaneie o QR code com seu app autenticador'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/security/2fa/verify', methods=['POST'])
@login_required
def verify_2fa_setup():
    """Verificar configuração 2FA"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        data = request.get_json()
        token = data.get('token', '')

        if two_factor_manager.verify_totp_setup(user.id, token):
            # Log evento de segurança
            security_logger.log_event(
                user.id, SecurityEventType.LOGIN_SUCCESS,
                request.remote_addr, request.headers.get('User-Agent', ''),
                {'action': '2fa_enabled'}
            )

            return jsonify({
                'success': True,
                'message': '2FA habilitado com sucesso!'
            })
        else:
            return jsonify({'success': False, 'error': 'Token inválido'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/security/2fa/disable', methods=['POST'])
@login_required
def disable_2fa():
    """Desabilitar 2FA"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        data = request.get_json()
        token = data.get('token', '')

        # Verificar token antes de desabilitar
        if two_factor_manager.verify_totp(user.id, token):
            two_factor_manager.disable_2fa(user.id)

            # Log evento de segurança
            security_logger.log_event(
                user.id, SecurityEventType.PASSWORD_CHANGED,
                request.remote_addr, request.headers.get('User-Agent', ''),
                {'action': '2fa_disabled'}
            )

            return jsonify({
                'success': True,
                'message': '2FA desabilitado'
            })
        else:
            return jsonify({'success': False, 'error': 'Token inválido'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/security/summary')
@login_required
def security_summary():
    """Resumo de segurança do usuário"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])

        # Verificar status 2FA
        has_2fa = two_factor_manager.is_2fa_enabled(user.id)

        # Obter resumo de segurança
        security_summary = security_logger.get_user_security_summary(user.id)

        return jsonify({
            'success': True,
            'security_status': {
                'two_factor_enabled': has_2fa,
                'security_score': security_summary['security_score'],
                'recent_events': security_summary['events_by_type'],
                'unique_ips': security_summary['unique_ips'],
                'high_risk_events': security_summary['high_risk_events']
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas Administrativas Avançadas
@app.route('/admin/users/stats')
@login_required
def admin_users_stats():
    """Estatísticas de usuários para admin"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        # Obter estatísticas de usuários
        total_users = len(auth_manager.users)
        active_users = len([u for u in auth_manager.users.values() if u.is_active])
        premium_users = len([u for u in auth_manager.users.values() if u.plan != PlanType.FREE])

        # Usuários novos hoje
        today = datetime.now().date()
        new_users_today = len([
            u for u in auth_manager.users.values()
            if u.created_at.date() == today
        ])

        return jsonify({
            'success': True,
            'user_stats': {
                'total_users': total_users,
                'active_users': active_users,
                'premium_users': premium_users,
                'new_users_today': new_users_today
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/affiliates/stats')
@login_required
def admin_affiliates_stats():
    """Estatísticas de afiliados para admin"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        # Obter estatísticas da plataforma
        platform_stats = affiliate_system.get_platform_stats()

        return jsonify({
            'success': True,
            'affiliate_stats': platform_stats
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/security/events')
@login_required
def admin_security_events():
    """Eventos de segurança para admin"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        # Obter eventos de segurança recentes
        import sqlite3
        conn = sqlite3.connect('security.db')
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, user_id, event_type, ip_address, risk_score, timestamp
            FROM security_events
            ORDER BY timestamp DESC
            LIMIT 50
        ''')

        events = []
        for row in cursor.fetchall():
            events.append({
                'id': row[0],
                'user_id': row[1],
                'event_type': row[2],
                'ip_address': row[3],
                'risk_score': row[4],
                'timestamp': row[5]
            })

        conn.close()

        return jsonify({
            'success': True,
            'security_events': events
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/exchanges/status')
@login_required
def admin_exchanges_status():
    """Status das exchanges para admin"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        # Obter status das exchanges
        status = exchange_manager.get_exchange_status()

        return jsonify({
            'success': True,
            'exchanges_status': status
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/exchanges/test', methods=['POST'])
@login_required
def admin_test_exchanges():
    """Testa conexões com exchanges"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        # Testar todas as exchanges
        results = {}
        for exchange_type, connector in exchange_manager.connectors.items():
            try:
                test_data = connector.get_ticker('BTCUSDT')
                results[exchange_type.value] = {
                    'success': test_data is not None,
                    'response_time': 'OK' if test_data else 'FAIL'
                }
            except Exception as e:
                results[exchange_type.value] = {
                    'success': False,
                    'error': str(e)
                }

        return jsonify({
            'success': True,
            'test_results': results
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/security/block-ips', methods=['POST'])
@login_required
def admin_block_suspicious_ips():
    """Bloqueia IPs suspeitos automaticamente"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        # Encontrar IPs com alto risk score
        import sqlite3
        conn = sqlite3.connect('security.db')
        cursor = conn.cursor()

        cursor.execute('''
            SELECT ip_address, COUNT(*) as events, AVG(risk_score) as avg_risk
            FROM security_events
            WHERE timestamp > datetime('now', '-24 hours')
            GROUP BY ip_address
            HAVING avg_risk > 70 OR events > 10
        ''')

        suspicious_ips = cursor.fetchall()
        blocked_count = 0

        # Aqui você implementaria o bloqueio real (firewall, etc.)
        # Por enquanto, apenas simular
        for ip_row in suspicious_ips:
            ip_address = ip_row[0]
            # Simular bloqueio
            print(f"🚫 Bloqueando IP suspeito: {ip_address}")
            blocked_count += 1

        conn.close()

        return jsonify({
            'success': True,
            'blocked_count': blocked_count,
            'message': f'{blocked_count} IPs suspeitos foram bloqueados'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de administração e monitoramento
@app.route('/admin/cache/status')
@login_required
def cache_status():
    """Status do sistema de cache"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        stats = cache_manager.get_stats()
        return jsonify({
            'success': True,
            'cache_stats': stats
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/cache/clear', methods=['POST'])
@login_required
def clear_cache():
    """Limpa cache específico"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        data = request.get_json()
        namespace = data.get('namespace', 'all')

        if namespace == 'all':
            # Limpar todos os namespaces principais
            cache_manager.clear_namespace('price_data')
            cache_manager.clear_namespace('analysis')
            cache_manager.clear_namespace('sentiment')
        else:
            cache_manager.clear_namespace(namespace)

        return jsonify({
            'success': True,
            'message': f'Cache {namespace} limpo com sucesso'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/rate-limits/status')
@login_required
def rate_limits_status():
    """Status do rate limiting"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        analytics = rate_limiter.get_analytics()
        return jsonify({
            'success': True,
            'rate_limit_analytics': analytics
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/backup/status')
@login_required
def backup_status():
    """Status do sistema de backup"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        status = backup_manager.get_backup_status()
        recent_backups = backup_manager.list_backups(5)

        return jsonify({
            'success': True,
            'backup_status': status,
            'recent_backups': recent_backups
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/backup/create', methods=['POST'])
@login_required
def create_backup():
    """Cria backup manual"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        data = request.get_json()
        backup_type = data.get('type', 'full')
        description = data.get('description', 'Backup manual')

        backup_info = backup_manager.create_backup(backup_type, description)

        if backup_info:
            return jsonify({
                'success': True,
                'backup': {
                    'id': backup_info.backup_id,
                    'type': backup_info.backup_type,
                    'size_mb': backup_info.file_size / 1024 / 1024,
                    'timestamp': backup_info.timestamp.isoformat()
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Falha ao criar backup'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/admin/monitoring/metrics')
@login_required
def monitoring_metrics():
    """Métricas de monitoramento"""
    try:
        user = auth_manager.get_user_by_id(session['user_id'])
        if not user or user.plan not in [PlanType.PROFESSIONAL, PlanType.ENTERPRISE]:
            return jsonify({'error': 'Acesso negado'}), 403

        current_metrics = monitor.get_current_metrics()
        health_status = monitor.get_health_status()

        return jsonify({
            'success': True,
            'metrics': current_metrics,
            'health': health_status
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Rotas de arquivos estáticos
@app.route('/logo.png')
def serve_logo():
    """Serve o logo"""
    try:
        return send_file('logo.png', mimetype='image/png')
    except FileNotFoundError:
        return "", 404

@app.route('/static/css/custom.css')
def serve_custom_css():
    """Serve o CSS customizado"""
    try:
        with open('dashboard/assets/custom.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        return css_content, 200, {'Content-Type': 'text/css'}
    except FileNotFoundError:
        return "", 404

if __name__ == "__main__":
    print("🚀 Iniciando CryptoSignals - Plataforma SaaS Completa...")
    print("🌐 Acesse: http://localhost:5000")
    print("🎨 Landing page de alta conversão")
    print("💎 Dashboard premium com modo escuro")
    print("💳 Sistema de pagamentos USDT integrado")

    # Criar diretório de templates se não existir
    os.makedirs('templates', exist_ok=True)

    # Abrir navegador automaticamente
    threading.Timer(1.5, lambda: webbrowser.open('http://localhost:5000')).start()

    app.run(debug=True, host='0.0.0.0', port=5000)
