#!/usr/bin/env python3
"""
Demonstração do Sistema de Pagamentos Multi-Rede
Mostra como usar BEP20, Polygon, Ethereum e métodos tradicionais
"""

import sys
import os
import json
from datetime import datetime

# Adicionar diretório atual ao path
sys.path.append(os.path.dirname(__file__))

from saas_payments import (
    MultiNetworkPaymentProcessor, PaymentMethod, PaymentNetwork, 
    PaymentStatus, Payment
)

def print_header(title):
    """Imprime cabeçalho formatado"""
    print("\n" + "="*60)
    print(f"🚀 {title}")
    print("="*60)

def print_payment_info(payment):
    """Imprime informações do pagamento"""
    print(f"📋 ID do Pagamento: {payment.id}")
    print(f"💰 Valor: {payment.amount} {payment.currency}")
    print(f"🌐 Rede: {payment.network.value}")
    print(f"💳 Método: {payment.payment_method.value}")
    print(f"📍 Endereço: {payment.wallet_address}")
    print(f"⏰ Status: {payment.status.value}")
    print(f"📅 Criado em: {payment.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"⏳ Expira em: {payment.expires_at.strftime('%Y-%m-%d %H:%M:%S')}")

def demo_payment_creation():
    """Demonstra criação de pagamentos em diferentes redes"""
    print_header("DEMONSTRAÇÃO - CRIAÇÃO DE PAGAMENTOS MULTI-REDE")
    
    # Configurar wallets de demonstração
    wallet_addresses = {
        'ethereum': '******************************************',
        'bsc': '******************************************',
        'polygon': '******************************************'
    }
    
    # Inicializar processador
    processor = MultiNetworkPaymentProcessor(wallet_addresses, "demo_payments.db")
    
    # Dados de teste
    user_id = "demo_user_123"
    plan = "starter"
    amount = 29.0
    
    print(f"👤 Usuário: {user_id}")
    print(f"📦 Plano: {plan}")
    print(f"💵 Valor base: ${amount}")
    
    # Demonstrar diferentes métodos de pagamento
    payment_methods = [
        (PaymentMethod.USDT_ETH, "USDT na rede Ethereum"),
        (PaymentMethod.USDT_BSC, "USDT na rede Binance Smart Chain (BEP20)"),
        (PaymentMethod.USDT_POLYGON, "USDT na rede Polygon"),
        (PaymentMethod.BUSD_BSC, "BUSD na rede Binance Smart Chain"),
        (PaymentMethod.BNB_BSC, "BNB (token nativo da BSC)"),
        (PaymentMethod.MATIC_POLYGON, "MATIC (token nativo do Polygon)"),
        (PaymentMethod.CREDIT_CARD, "Cartão de Crédito"),
        (PaymentMethod.PAYPAL, "PayPal")
    ]
    
    created_payments = []
    
    for method, description in payment_methods:
        print(f"\n🔄 Criando pagamento: {description}")
        try:
            payment = processor.create_payment_request(
                user_id=user_id,
                plan=plan,
                amount=amount,
                payment_method=method,
                annual=False
            )
            created_payments.append(payment)
            print(f"✅ Pagamento criado com sucesso!")
            print_payment_info(payment)
            
            # Gerar QR code
            qr_data = processor.generate_payment_qr_code(payment)
            qr_dict = json.loads(qr_data)
            print(f"📱 QR Code: {json.dumps(qr_dict, indent=2)}")
            
        except Exception as e:
            print(f"❌ Erro ao criar pagamento: {e}")
    
    return processor, created_payments

def demo_payment_methods():
    """Demonstra obtenção de métodos de pagamento suportados"""
    print_header("MÉTODOS DE PAGAMENTO SUPORTADOS")
    
    processor = MultiNetworkPaymentProcessor({
        'ethereum': '******************************************',
        'bsc': '******************************************',
        'polygon': '******************************************'
    })
    
    methods = processor.get_supported_payment_methods()
    
    print("🚀 CRIPTOMOEDAS:")
    for method in methods['crypto']:
        print(f"  💎 {method['name']}")
        print(f"     🌐 Rede: {method['network']}")
        print(f"     💰 Moeda: {method['currency']}")
        print(f"     💸 Taxas: {method['fees']}")
        print(f"     ⏱️  Confirmação: {method['confirmation_time']}")
        print()
    
    print("💳 MÉTODOS TRADICIONAIS:")
    for method in methods['traditional']:
        print(f"  🏦 {method['name']}")
        print(f"     💸 Taxas: {method['fees']}")
        print(f"     ⏱️  Confirmação: {method['confirmation_time']}")
        print()

def demo_payment_verification():
    """Demonstra verificação de pagamentos"""
    print_header("VERIFICAÇÃO DE PAGAMENTOS")
    
    processor = MultiNetworkPaymentProcessor({
        'ethereum': '******************************************',
        'bsc': '******************************************',
        'polygon': '******************************************'
    })
    
    # Criar um pagamento de teste
    payment = processor.create_payment_request(
        user_id="test_user",
        plan="starter",
        amount=29.0,
        payment_method=PaymentMethod.USDT_BSC,
        annual=False
    )
    
    print("📋 Pagamento criado para verificação:")
    print_payment_info(payment)
    
    # Simular verificação (sem transação real)
    print(f"\n🔍 Verificando pagamento {payment.id}...")
    print("⚠️  Nota: Esta é uma demonstração. Em produção, seria verificada uma transação real na blockchain.")
    
    # Simular confirmação
    processor.update_payment_status(payment.id, PaymentStatus.CONFIRMED, "0xdemo_tx_hash")
    
    # Buscar pagamento atualizado
    updated_payment = processor.get_payment(payment.id)
    print("\n✅ Pagamento após verificação:")
    print_payment_info(updated_payment)

def main():
    """Função principal da demonstração"""
    print("🎉 SISTEMA DE PAGAMENTOS MULTI-REDE - CRYPTOSIGNALS")
    print("Suporte para Ethereum, BEP20 (Binance Smart Chain), Polygon e métodos tradicionais")
    
    try:
        # Demonstrar métodos suportados
        demo_payment_methods()
        
        # Demonstrar criação de pagamentos
        processor, payments = demo_payment_creation()
        
        # Demonstrar verificação
        demo_payment_verification()
        
        print_header("RESUMO DA DEMONSTRAÇÃO")
        print(f"✅ {len(payments)} pagamentos criados com sucesso")
        print("✅ Suporte para 6 redes/métodos de criptomoedas")
        print("✅ Suporte para 2 métodos tradicionais")
        print("✅ Sistema de verificação implementado")
        print("✅ Geração de QR codes funcionando")
        
        print("\n🎯 RECURSOS IMPLEMENTADOS:")
        print("  🌐 Ethereum (USDT, USDC, DAI)")
        print("  🟡 Binance Smart Chain - BEP20 (USDT, BUSD, BNB)")
        print("  🟣 Polygon (USDT, USDC, MATIC)")
        print("  💳 Cartão de Crédito")
        print("  💰 PayPal")
        print("  📱 QR Codes para pagamentos crypto")
        print("  🔍 Verificação automática na blockchain")
        print("  💾 Banco de dados SQLite integrado")
        
        print("\n🚀 PRONTO PARA PRODUÇÃO!")
        
    except Exception as e:
        print(f"❌ Erro durante a demonstração: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Limpar arquivo de demonstração
        try:
            os.remove("demo_payments.db")
            print("\n🧹 Arquivo de demonstração removido")
        except:
            pass

if __name__ == "__main__":
    main()
