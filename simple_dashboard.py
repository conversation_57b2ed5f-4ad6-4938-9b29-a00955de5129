"""
Dashboard simplificado do BitcoinAnalytics usando apenas matplotlib.
"""

import sys
import os
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import webbrowser
import tempfile

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_processing import BitcoinDataProcessor
from technical_analysis import TechnicalAnalyzer

def create_bitcoin_dashboard():
    """Cria dashboard do Bitcoin usando matplotlib."""
    
    print("🪙 BitcoinAnalytics - Dashboard Simplificado")
    print("=" * 50)
    
    # Carregar dados
    print("📊 Carregando dados do Bitcoin...")
    processor = BitcoinDataProcessor()
    df = processor.add_technical_features()
    
    # Análise técnica
    print("🔍 Executando análise técnica...")
    analyzer = TechnicalAnalyzer(df)
    indicators = analyzer.calculate_all_indicators()
    summary = analyzer.get_market_summary()
    
    # Usar apenas os últimos 1000 pontos para visualização
    df_plot = indicators.tail(1000).copy()
    
    # Configurar estilo
    plt.style.use('dark_background')
    fig = plt.figure(figsize=(16, 12))
    fig.suptitle('🪙 BitcoinAnalytics Dashboard', fontsize=20, fontweight='bold', color='#F7931A')
    
    # Layout dos subplots
    gs = fig.add_gridspec(4, 2, height_ratios=[3, 1, 1, 1], hspace=0.3, wspace=0.2)
    
    # 1. Gráfico principal - Preço e Médias Móveis
    ax1 = fig.add_subplot(gs[0, :])
    ax1.plot(df_plot.index, df_plot['Close'], label='BTC/USDT', color='#F7931A', linewidth=2)
    
    if 'SMA_20' in df_plot.columns:
        ax1.plot(df_plot.index, df_plot['SMA_20'], label='SMA 20', color='cyan', alpha=0.7)
    if 'SMA_50' in df_plot.columns:
        ax1.plot(df_plot.index, df_plot['SMA_50'], label='SMA 50', color='yellow', alpha=0.7)
    
    # Bandas de Bollinger
    if all(col in df_plot.columns for col in ['BB_Upper', 'BB_Lower']):
        ax1.fill_between(df_plot.index, df_plot['BB_Upper'], df_plot['BB_Lower'], 
                        alpha=0.1, color='gray', label='Bollinger Bands')
        ax1.plot(df_plot.index, df_plot['BB_Upper'], color='gray', alpha=0.5, linestyle='--')
        ax1.plot(df_plot.index, df_plot['BB_Lower'], color='gray', alpha=0.5, linestyle='--')
    
    ax1.set_title(f'Preço do Bitcoin - ${summary["current_price"]:.2f} | Tendência: {summary["overall_trend"]}', 
                  fontsize=14, fontweight='bold')
    ax1.set_ylabel('Preço (USDT)', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. Volume
    ax2 = fig.add_subplot(gs[1, :])
    ax2.bar(df_plot.index, df_plot['Volume'], color='#F7931A', alpha=0.6, width=0.8)
    ax2.set_title('Volume de Negociação', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Volume', fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 3. RSI
    ax3 = fig.add_subplot(gs[2, 0])
    if 'RSI_14' in df_plot.columns:
        ax3.plot(df_plot.index, df_plot['RSI_14'], color='purple', linewidth=2)
        ax3.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='Sobrecomprado')
        ax3.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='Sobrevendido')
        ax3.fill_between(df_plot.index, 30, 70, alpha=0.1, color='gray')
    
    ax3.set_title(f'RSI (14) - {summary.get("rsi", 0):.1f}', fontsize=12, fontweight='bold')
    ax3.set_ylabel('RSI', fontsize=10)
    ax3.set_ylim(0, 100)
    ax3.grid(True, alpha=0.3)
    ax3.legend(fontsize=8)
    
    # 4. MACD
    ax4 = fig.add_subplot(gs[2, 1])
    if all(col in df_plot.columns for col in ['MACD', 'MACD_Signal']):
        ax4.plot(df_plot.index, df_plot['MACD'], label='MACD', color='blue', linewidth=2)
        ax4.plot(df_plot.index, df_plot['MACD_Signal'], label='Signal', color='red', linewidth=2)
        
        if 'MACD_Histogram' in df_plot.columns:
            colors = ['green' if x >= 0 else 'red' for x in df_plot['MACD_Histogram']]
            ax4.bar(df_plot.index, df_plot['MACD_Histogram'], color=colors, alpha=0.6, width=0.8)
    
    ax4.set_title('MACD', fontsize=12, fontweight='bold')
    ax4.set_ylabel('MACD', fontsize=10)
    ax4.legend(fontsize=8)
    ax4.grid(True, alpha=0.3)
    
    # 5. Sinais de Trading
    ax5 = fig.add_subplot(gs[3, :])
    ax5.axis('off')
    
    # Criar tabela de sinais
    signals_text = "🎯 SINAIS DE TRADING:\n\n"
    for indicator, signal in summary['signals'].items():
        color = '🟢' if signal == 'COMPRA' else '🔴' if signal == 'VENDA' else '🟡'
        signals_text += f"{color} {indicator}: {signal}\n"
    
    # Adicionar estatísticas
    stats_text = f"\n📊 ESTATÍSTICAS:\n"
    stats_text += f"• Total de registros: {len(df):,}\n"
    stats_text += f"• Período: {df.index.min().strftime('%Y-%m-%d')} até {df.index.max().strftime('%Y-%m-%d')}\n"
    stats_text += f"• Volatilidade (24h): {df['Volatility_24h'].iloc[-1]*100:.2f}%\n"
    
    full_text = signals_text + stats_text
    
    ax5.text(0.02, 0.98, full_text, transform=ax5.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='black', alpha=0.8))
    
    # Formatação das datas nos eixos
    for ax in [ax1, ax2, ax3, ax4]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(df_plot)//10)))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    # Ajustar layout
    plt.tight_layout()
    
    # Salvar como HTML
    temp_dir = tempfile.gettempdir()
    html_file = os.path.join(temp_dir, 'bitcoin_dashboard.html')
    
    # Salvar gráfico como imagem temporária
    img_file = os.path.join(temp_dir, 'bitcoin_chart.png')
    plt.savefig(img_file, dpi=150, bbox_inches='tight', facecolor='black')
    
    # Criar HTML
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>BitcoinAnalytics Dashboard</title>
        <meta charset="UTF-8">
        <style>
            body {{
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
            }}
            .header {{
                text-align: center;
                margin-bottom: 20px;
            }}
            .chart {{
                text-align: center;
                margin: 20px 0;
            }}
            .info {{
                background-color: #2d2d2d;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
            }}
            .refresh-btn {{
                background-color: #F7931A;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            }}
            .refresh-btn:hover {{
                background-color: #e6830f;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🪙 BitcoinAnalytics Dashboard</h1>
            <p>Análise em tempo real do Bitcoin (BTC/USDT)</p>
            <button class="refresh-btn" onclick="location.reload()">🔄 Atualizar</button>
        </div>
        
        <div class="chart">
            <img src="file:///{img_file.replace(os.sep, '/')}" alt="Bitcoin Chart" style="max-width: 100%; height: auto;">
        </div>
        
        <div class="info">
            <h3>📊 Resumo do Mercado</h3>
            <p><strong>Preço Atual:</strong> ${summary['current_price']:,.2f} USDT</p>
            <p><strong>Tendência Geral:</strong> {summary['overall_trend']}</p>
            <p><strong>RSI (14):</strong> {summary.get('rsi', 0):.2f}</p>
            <p><strong>Total de Dados:</strong> {len(df):,} registros</p>
        </div>
        
        <div class="info">
            <h3>🎯 Sinais de Trading</h3>
            <ul>
    """
    
    for indicator, signal in summary['signals'].items():
        color = '#28a745' if signal == 'COMPRA' else '#dc3545' if signal == 'VENDA' else '#ffc107'
        html_content += f'<li style="color: {color};"><strong>{indicator}:</strong> {signal}</li>'
    
    html_content += """
            </ul>
        </div>
        
        <div class="info">
            <h3>ℹ️ Sobre</h3>
            <p>Este dashboard foi gerado automaticamente pelo BitcoinAnalytics.</p>
            <p>Os dados são processados usando análise técnica avançada com indicadores como RSI, MACD e Bandas de Bollinger.</p>
            <p><strong>Aviso:</strong> Este dashboard é apenas para fins educacionais. Não constitui aconselhamento financeiro.</p>
        </div>
        
        <script>
            // Auto-refresh a cada 5 minutos
            setTimeout(function() {
                location.reload();
            }, 300000);
        </script>
    </body>
    </html>
    """
    
    # Salvar HTML
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Dashboard criado com sucesso!")
    print(f"📊 Preço atual: ${summary['current_price']:.2f}")
    print(f"📈 Tendência: {summary['overall_trend']}")
    print(f"📉 RSI: {summary.get('rsi', 0):.2f}")
    print(f"📁 Arquivo salvo em: {html_file}")
    
    # Abrir no navegador
    print("🌐 Abrindo dashboard no navegador...")
    webbrowser.open(f'file:///{html_file}')
    
    # Mostrar gráfico
    plt.show()
    
    return html_file

if __name__ == "__main__":
    try:
        dashboard_file = create_bitcoin_dashboard()
        print(f"\n🎉 Dashboard disponível em: {dashboard_file}")
        print("💡 O dashboard será atualizado automaticamente a cada 5 minutos.")
        
    except Exception as e:
        print(f"❌ Erro ao criar dashboard: {e}")
        import traceback
        traceback.print_exc()
