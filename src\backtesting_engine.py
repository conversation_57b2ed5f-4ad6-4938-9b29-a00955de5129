"""
Sistema de Backtesting
Validação de estratégias de trading com dados históricos
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
import sqlite3
import logging
import json
from technical_analysis import TechnicalAnalyzer

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    EXECUTED = "executed"
    CANCELLED = "cancelled"

class StrategyType(Enum):
    SMA_CROSSOVER = "sma_crossover"
    RSI_OVERSOLD = "rsi_oversold"
    MACD_SIGNAL = "macd_signal"
    BOLLINGER_BANDS = "bollinger_bands"
    CUSTOM = "custom"

@dataclass
class Trade:
    id: str
    entry_time: datetime
    exit_time: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    quantity: float
    order_type: OrderType
    profit_loss: Optional[float] = None
    profit_loss_percent: Optional[float] = None
    duration: Optional[timedelta] = None
    is_open: bool = True

@dataclass
class BacktestResult:
    strategy_name: str
    symbol: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_percent: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    profit_factor: float
    trades: List[Trade] = field(default_factory=list)
    equity_curve: List[float] = field(default_factory=list)
    drawdown_curve: List[float] = field(default_factory=list)
    monthly_returns: Dict[str, float] = field(default_factory=dict)

@dataclass
class StrategyParameters:
    strategy_type: StrategyType
    parameters: Dict
    stop_loss_percent: Optional[float] = None
    take_profit_percent: Optional[float] = None
    max_positions: int = 1
    position_size_percent: float = 100.0  # % do capital por trade

class BacktestingEngine:
    def __init__(self, db_path: str = "backtest_results.db"):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """Inicializa banco de dados de backtesting"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabela de resultados de backtest
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backtest_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_name TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    start_date TIMESTAMP NOT NULL,
                    end_date TIMESTAMP NOT NULL,
                    initial_capital REAL NOT NULL,
                    final_capital REAL NOT NULL,
                    total_return REAL NOT NULL,
                    total_return_percent REAL NOT NULL,
                    total_trades INTEGER NOT NULL,
                    winning_trades INTEGER NOT NULL,
                    losing_trades INTEGER NOT NULL,
                    win_rate REAL NOT NULL,
                    avg_win REAL NOT NULL,
                    avg_loss REAL NOT NULL,
                    max_drawdown REAL NOT NULL,
                    sharpe_ratio REAL NOT NULL,
                    sortino_ratio REAL NOT NULL,
                    profit_factor REAL NOT NULL,
                    strategy_parameters TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de trades individuais
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backtest_trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    backtest_id INTEGER NOT NULL,
                    trade_id TEXT NOT NULL,
                    entry_time TIMESTAMP NOT NULL,
                    exit_time TIMESTAMP,
                    entry_price REAL NOT NULL,
                    exit_price REAL,
                    quantity REAL NOT NULL,
                    order_type TEXT NOT NULL,
                    profit_loss REAL,
                    profit_loss_percent REAL,
                    duration_minutes INTEGER,
                    FOREIGN KEY (backtest_id) REFERENCES backtest_results (id)
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Banco de dados de backtesting inicializado")
            
        except Exception as e:
            logger.error(f"Erro ao inicializar banco de backtesting: {e}")
    
    def run_backtest(self, data: pd.DataFrame, strategy_params: StrategyParameters, 
                    initial_capital: float = 10000.0, symbol: str = "BTC") -> BacktestResult:
        """Executa um backtest completo"""
        try:
            # Preparar dados
            data = data.copy()
            data.reset_index(drop=True, inplace=True)
            
            # Calcular indicadores técnicos
            analyzer = TechnicalAnalyzer(data)
            indicators = analyzer.calculate_all_indicators()
            
            # Gerar sinais de trading
            signals = self.generate_signals(indicators, strategy_params)
            
            # Simular trades
            trades = self.simulate_trades(data, signals, strategy_params, initial_capital)
            
            # Calcular métricas
            result = self.calculate_metrics(
                trades, initial_capital, data, strategy_params.strategy_type.value, symbol
            )
            
            # Salvar resultado
            self.save_backtest_result(result, strategy_params)
            
            logger.info(f"Backtest concluído: {result.total_return_percent:.2f}% retorno")
            return result
            
        except Exception as e:
            logger.error(f"Erro no backtest: {e}")
            raise
    
    def generate_signals(self, data: pd.DataFrame, strategy_params: StrategyParameters) -> pd.DataFrame:
        """Gera sinais de compra/venda baseados na estratégia"""
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0  # 0 = hold, 1 = buy, -1 = sell
        
        if strategy_params.strategy_type == StrategyType.SMA_CROSSOVER:
            signals = self.sma_crossover_strategy(data, signals, strategy_params.parameters)
            
        elif strategy_params.strategy_type == StrategyType.RSI_OVERSOLD:
            signals = self.rsi_oversold_strategy(data, signals, strategy_params.parameters)
            
        elif strategy_params.strategy_type == StrategyType.MACD_SIGNAL:
            signals = self.macd_signal_strategy(data, signals, strategy_params.parameters)
            
        elif strategy_params.strategy_type == StrategyType.BOLLINGER_BANDS:
            signals = self.bollinger_bands_strategy(data, signals, strategy_params.parameters)
        
        return signals
    
    def sma_crossover_strategy(self, data: pd.DataFrame, signals: pd.DataFrame, params: Dict) -> pd.DataFrame:
        """Estratégia de cruzamento de médias móveis"""
        short_window = params.get('short_window', 10)
        long_window = params.get('long_window', 30)
        
        # Calcular médias
        data[f'SMA_{short_window}'] = data['close'].rolling(window=short_window).mean()
        data[f'SMA_{long_window}'] = data['close'].rolling(window=long_window).mean()
        
        # Sinais de cruzamento
        signals['signal'][short_window:] = np.where(
            data[f'SMA_{short_window}'][short_window:] > data[f'SMA_{long_window}'][short_window:], 1, 0
        )
        
        # Detectar mudanças de sinal
        signals['positions'] = signals['signal'].diff()
        
        return signals
    
    def rsi_oversold_strategy(self, data: pd.DataFrame, signals: pd.DataFrame, params: Dict) -> pd.DataFrame:
        """Estratégia baseada em RSI oversold/overbought"""
        oversold_threshold = params.get('oversold_threshold', 30)
        overbought_threshold = params.get('overbought_threshold', 70)
        
        # Usar RSI já calculado
        rsi = data.get('rsi', pd.Series())
        
        if not rsi.empty:
            # Sinais de compra (RSI oversold)
            buy_signals = (rsi < oversold_threshold) & (rsi.shift(1) >= oversold_threshold)
            
            # Sinais de venda (RSI overbought)
            sell_signals = (rsi > overbought_threshold) & (rsi.shift(1) <= overbought_threshold)
            
            signals.loc[buy_signals, 'signal'] = 1
            signals.loc[sell_signals, 'signal'] = -1
            
            signals['positions'] = signals['signal']
        
        return signals
    
    def macd_signal_strategy(self, data: pd.DataFrame, signals: pd.DataFrame, params: Dict) -> pd.DataFrame:
        """Estratégia baseada em sinais MACD"""
        # Usar MACD já calculado
        macd = data.get('macd', pd.Series())
        macd_signal = data.get('macd_signal', pd.Series())
        
        if not macd.empty and not macd_signal.empty:
            # Cruzamento MACD acima da linha de sinal (compra)
            buy_signals = (macd > macd_signal) & (macd.shift(1) <= macd_signal.shift(1))
            
            # Cruzamento MACD abaixo da linha de sinal (venda)
            sell_signals = (macd < macd_signal) & (macd.shift(1) >= macd_signal.shift(1))
            
            signals.loc[buy_signals, 'signal'] = 1
            signals.loc[sell_signals, 'signal'] = -1
            
            signals['positions'] = signals['signal']
        
        return signals
    
    def bollinger_bands_strategy(self, data: pd.DataFrame, signals: pd.DataFrame, params: Dict) -> pd.DataFrame:
        """Estratégia baseada em Bollinger Bands"""
        # Usar Bollinger Bands já calculadas
        bb_upper = data.get('bb_upper', pd.Series())
        bb_lower = data.get('bb_lower', pd.Series())
        close = data['close']
        
        if not bb_upper.empty and not bb_lower.empty:
            # Compra quando preço toca banda inferior
            buy_signals = (close <= bb_lower) & (close.shift(1) > bb_lower.shift(1))
            
            # Venda quando preço toca banda superior
            sell_signals = (close >= bb_upper) & (close.shift(1) < bb_upper.shift(1))
            
            signals.loc[buy_signals, 'signal'] = 1
            signals.loc[sell_signals, 'signal'] = -1
            
            signals['positions'] = signals['signal']
        
        return signals
    
    def simulate_trades(self, data: pd.DataFrame, signals: pd.DataFrame, 
                       strategy_params: StrategyParameters, initial_capital: float) -> List[Trade]:
        """Simula execução de trades baseado nos sinais"""
        trades = []
        current_capital = initial_capital
        current_position = None
        trade_counter = 0
        
        for i in range(len(signals)):
            signal = signals.iloc[i]['signal']
            current_price = data.iloc[i]['close']
            current_time = data.iloc[i]['timestamp'] if 'timestamp' in data.columns else datetime.now()
            
            # Sinal de compra
            if signal == 1 and current_position is None:
                position_size = (current_capital * strategy_params.position_size_percent / 100) / current_price
                
                trade_counter += 1
                current_position = Trade(
                    id=f"trade_{trade_counter}",
                    entry_time=current_time,
                    exit_time=None,
                    entry_price=current_price,
                    exit_price=None,
                    quantity=position_size,
                    order_type=OrderType.BUY
                )
                
            # Sinal de venda
            elif signal == -1 and current_position is not None:
                # Fechar posição
                current_position.exit_time = current_time
                current_position.exit_price = current_price
                current_position.is_open = False
                
                # Calcular P&L
                profit_loss = (current_price - current_position.entry_price) * current_position.quantity
                current_position.profit_loss = profit_loss
                current_position.profit_loss_percent = (profit_loss / (current_position.entry_price * current_position.quantity)) * 100
                current_position.duration = current_time - current_position.entry_time
                
                # Atualizar capital
                current_capital += profit_loss
                
                trades.append(current_position)
                current_position = None
            
            # Verificar stop loss e take profit
            if current_position is not None:
                entry_price = current_position.entry_price
                
                # Stop Loss
                if strategy_params.stop_loss_percent:
                    stop_loss_price = entry_price * (1 - strategy_params.stop_loss_percent / 100)
                    if current_price <= stop_loss_price:
                        current_position.exit_time = current_time
                        current_position.exit_price = stop_loss_price
                        current_position.is_open = False
                        
                        profit_loss = (stop_loss_price - entry_price) * current_position.quantity
                        current_position.profit_loss = profit_loss
                        current_position.profit_loss_percent = (profit_loss / (entry_price * current_position.quantity)) * 100
                        current_position.duration = current_time - current_position.entry_time
                        
                        current_capital += profit_loss
                        trades.append(current_position)
                        current_position = None
                        continue
                
                # Take Profit
                if strategy_params.take_profit_percent:
                    take_profit_price = entry_price * (1 + strategy_params.take_profit_percent / 100)
                    if current_price >= take_profit_price:
                        current_position.exit_time = current_time
                        current_position.exit_price = take_profit_price
                        current_position.is_open = False
                        
                        profit_loss = (take_profit_price - entry_price) * current_position.quantity
                        current_position.profit_loss = profit_loss
                        current_position.profit_loss_percent = (profit_loss / (entry_price * current_position.quantity)) * 100
                        current_position.duration = current_time - current_position.entry_time
                        
                        current_capital += profit_loss
                        trades.append(current_position)
                        current_position = None
        
        # Fechar posição aberta no final
        if current_position is not None:
            final_price = data.iloc[-1]['close']
            final_time = data.iloc[-1]['timestamp'] if 'timestamp' in data.columns else datetime.now()
            
            current_position.exit_time = final_time
            current_position.exit_price = final_price
            current_position.is_open = False
            
            profit_loss = (final_price - current_position.entry_price) * current_position.quantity
            current_position.profit_loss = profit_loss
            current_position.profit_loss_percent = (profit_loss / (current_position.entry_price * current_position.quantity)) * 100
            current_position.duration = final_time - current_position.entry_time
            
            trades.append(current_position)
        
        return trades
    
    def calculate_metrics(self, trades: List[Trade], initial_capital: float, 
                         data: pd.DataFrame, strategy_name: str, symbol: str) -> BacktestResult:
        """Calcula métricas de performance do backtest"""
        if not trades:
            return BacktestResult(
                strategy_name=strategy_name,
                symbol=symbol,
                start_date=data.iloc[0]['timestamp'] if 'timestamp' in data.columns else datetime.now(),
                end_date=data.iloc[-1]['timestamp'] if 'timestamp' in data.columns else datetime.now(),
                initial_capital=initial_capital,
                final_capital=initial_capital,
                total_return=0.0,
                total_return_percent=0.0,
                total_trades=0,
                winning_trades=0,
                losing_trades=0,
                win_rate=0.0,
                avg_win=0.0,
                avg_loss=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                sortino_ratio=0.0,
                profit_factor=0.0
            )
        
        # Métricas básicas
        total_profit_loss = sum(trade.profit_loss for trade in trades if trade.profit_loss is not None)
        final_capital = initial_capital + total_profit_loss
        total_return_percent = (total_profit_loss / initial_capital) * 100
        
        # Trades vencedores e perdedores
        winning_trades = [t for t in trades if t.profit_loss and t.profit_loss > 0]
        losing_trades = [t for t in trades if t.profit_loss and t.profit_loss < 0]
        
        win_rate = len(winning_trades) / len(trades) * 100 if trades else 0
        avg_win = np.mean([t.profit_loss for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t.profit_loss for t in losing_trades]) if losing_trades else 0
        
        # Profit Factor
        total_wins = sum(t.profit_loss for t in winning_trades) if winning_trades else 0
        total_losses = abs(sum(t.profit_loss for t in losing_trades)) if losing_trades else 0
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        # Curva de equity e drawdown
        equity_curve = [initial_capital]
        running_capital = initial_capital
        
        for trade in trades:
            if trade.profit_loss is not None:
                running_capital += trade.profit_loss
                equity_curve.append(running_capital)
        
        # Calcular drawdown
        peak = equity_curve[0]
        drawdown_curve = []
        max_drawdown = 0
        
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            
            drawdown = (peak - equity) / peak * 100
            drawdown_curve.append(drawdown)
            
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        # Sharpe Ratio (simplificado)
        returns = [t.profit_loss_percent for t in trades if t.profit_loss_percent is not None]
        if returns:
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Sortino Ratio (simplificado)
        negative_returns = [r for r in returns if r < 0]
        if negative_returns:
            downside_std = np.std(negative_returns)
            sortino_ratio = avg_return / downside_std if downside_std > 0 else 0
        else:
            sortino_ratio = sharpe_ratio
        
        # Retornos mensais (simplificado)
        monthly_returns = {}
        
        return BacktestResult(
            strategy_name=strategy_name,
            symbol=symbol,
            start_date=data.iloc[0]['timestamp'] if 'timestamp' in data.columns else datetime.now(),
            end_date=data.iloc[-1]['timestamp'] if 'timestamp' in data.columns else datetime.now(),
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return=total_profit_loss,
            total_return_percent=total_return_percent,
            total_trades=len(trades),
            winning_trades=len(winning_trades),
            losing_trades=len(losing_trades),
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            profit_factor=profit_factor,
            trades=trades,
            equity_curve=equity_curve,
            drawdown_curve=drawdown_curve,
            monthly_returns=monthly_returns
        )
    
    def save_backtest_result(self, result: BacktestResult, strategy_params: StrategyParameters):
        """Salva resultado do backtest no banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Salvar resultado principal
            cursor.execute('''
                INSERT INTO backtest_results 
                (strategy_name, symbol, start_date, end_date, initial_capital, final_capital,
                 total_return, total_return_percent, total_trades, winning_trades, losing_trades,
                 win_rate, avg_win, avg_loss, max_drawdown, sharpe_ratio, sortino_ratio,
                 profit_factor, strategy_parameters)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                result.strategy_name, result.symbol, result.start_date, result.end_date,
                result.initial_capital, result.final_capital, result.total_return,
                result.total_return_percent, result.total_trades, result.winning_trades,
                result.losing_trades, result.win_rate, result.avg_win, result.avg_loss,
                result.max_drawdown, result.sharpe_ratio, result.sortino_ratio,
                result.profit_factor, json.dumps(strategy_params.parameters)
            ))
            
            backtest_id = cursor.lastrowid
            
            # Salvar trades individuais
            for trade in result.trades:
                duration_minutes = trade.duration.total_seconds() / 60 if trade.duration else None
                
                cursor.execute('''
                    INSERT INTO backtest_trades 
                    (backtest_id, trade_id, entry_time, exit_time, entry_price, exit_price,
                     quantity, order_type, profit_loss, profit_loss_percent, duration_minutes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    backtest_id, trade.id, trade.entry_time, trade.exit_time,
                    trade.entry_price, trade.exit_price, trade.quantity,
                    trade.order_type.value, trade.profit_loss, trade.profit_loss_percent,
                    duration_minutes
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Resultado de backtest salvo com ID: {backtest_id}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar resultado de backtest: {e}")
    
    def get_backtest_results(self, symbol: str = None, strategy_name: str = None, 
                           limit: int = 10) -> List[Dict]:
        """Busca resultados de backtests salvos"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            query = "SELECT * FROM backtest_results WHERE 1=1"
            params = []
            
            if symbol:
                query += " AND symbol = ?"
                params.append(symbol)
            
            if strategy_name:
                query += " AND strategy_name = ?"
                params.append(strategy_name)
            
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # Converter para dicionários
            columns = [desc[0] for desc in cursor.description]
            backtest_results = []
            
            for result in results:
                result_dict = dict(zip(columns, result))
                result_dict['strategy_parameters'] = json.loads(result_dict['strategy_parameters'])
                backtest_results.append(result_dict)
            
            conn.close()
            return backtest_results
            
        except Exception as e:
            logger.error(f"Erro ao buscar resultados de backtest: {e}")
            return []

# Instância global
backtesting_engine = BacktestingEngine()
