"""
Aplicação de teste simplificada para BitcoinAnalytics.
"""

import sys
import os

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    import pandas as pd
    import numpy as np
    print("✅ Pandas e NumPy importados com sucesso!")
    
    # Testar módulos customizados
    from data_processing import BitcoinDataProcessor
    print("✅ Módulo data_processing importado!")
    
    from technical_analysis import TechnicalAnalyzer
    print("✅ Módulo technical_analysis importado!")
    
    # Testar carregamento de dados
    processor = BitcoinDataProcessor()
    df = processor.add_technical_features()
    print(f"✅ Dados carregados: {len(df)} registros")
    
    # Testar análise técnica
    analyzer = TechnicalAnalyzer(df)
    indicators = analyzer.calculate_all_indicators()
    summary = analyzer.get_market_summary()
    
    print(f"✅ Análise técnica concluída!")
    print(f"📊 Preço atual: ${summary['current_price']:.2f}")
    print(f"📈 Tendência: {summary['overall_trend']}")
    print(f"📉 RSI: {summary.get('rsi', 'N/A')}")
    
    print("\n🎯 Sinais de Trading:")
    for indicator, signal in summary['signals'].items():
        print(f"   {indicator}: {signal}")
    
    # Testar visualização
    try:
        import plotly.graph_objects as go
        from visualization import BitcoinVisualizer
        
        visualizer = BitcoinVisualizer(indicators)
        fig = visualizer.create_candlestick_chart(analyzer.indicators)
        print("✅ Visualização criada com sucesso!")
        
        # Testar Dash
        try:
            import dash
            import dash_bootstrap_components as dbc
            print("✅ Dash importado com sucesso!")
            print("🚀 Todos os componentes estão funcionando!")
            
        except ImportError as e:
            print(f"❌ Erro ao importar Dash: {e}")
            
    except ImportError as e:
        print(f"❌ Erro ao importar Plotly: {e}")
    
except ImportError as e:
    print(f"❌ Erro ao importar módulos: {e}")
except Exception as e:
    print(f"❌ Erro geral: {e}")

print("\n" + "="*50)
print("Teste concluído!")
