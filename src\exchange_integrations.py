"""
Integrações com Exchanges - CryptoSignals
Integração com Binance, Coinbase Pro, Kraken e outras exchanges
"""

import requests
import websocket
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import hmac
import hashlib
import base64
from collections import deque

class ExchangeType(Enum):
    """Tipos de exchanges suportadas"""
    BINANCE = "binance"
    COINBASE_PRO = "coinbase_pro"
    KRAKEN = "kraken"
    COINGECKO = "coingecko"  # Para dados gratuitos

class DataType(Enum):
    """Tipos de dados"""
    TICKER = "ticker"
    ORDERBOOK = "orderbook"
    TRADES = "trades"
    KLINES = "klines"
    NEWS = "news"

@dataclass
class ExchangeConfig:
    """Configuração da exchange"""
    exchange: ExchangeType
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    sandbox: bool = True
    rate_limit: int = 1200  # requests por minuto
    timeout: int = 30

@dataclass
class MarketData:
    """Dados de mercado"""
    symbol: str
    exchange: ExchangeType
    price: float
    volume_24h: float
    change_24h: float
    high_24h: float
    low_24h: float
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    spread: Optional[float] = None

@dataclass
class OrderBookData:
    """Dados do order book"""
    symbol: str
    exchange: ExchangeType
    bids: List[Tuple[float, float]]  # [(price, quantity), ...]
    asks: List[Tuple[float, float]]
    timestamp: datetime

@dataclass
class TradeData:
    """Dados de trade"""
    symbol: str
    exchange: ExchangeType
    price: float
    quantity: float
    side: str  # 'buy' or 'sell'
    timestamp: datetime
    trade_id: str

class BinanceConnector:
    """Conector para Binance"""
    
    def __init__(self, config: ExchangeConfig):
        self.config = config
        self.base_url = "https://api.binance.com" if not config.sandbox else "https://testnet.binance.vision"
        self.ws_url = "wss://stream.binance.com:9443/ws/" if not config.sandbox else "wss://testnet.binance.vision/ws/"
        self.session = requests.Session()
        self.ws_connections = {}
        
    def get_ticker(self, symbol: str) -> Optional[MarketData]:
        """Obtém ticker de um símbolo"""
        try:
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {'symbol': symbol.upper()}
            
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            return MarketData(
                symbol=symbol,
                exchange=ExchangeType.BINANCE,
                price=float(data['lastPrice']),
                volume_24h=float(data['volume']),
                change_24h=float(data['priceChangePercent']),
                high_24h=float(data['highPrice']),
                low_24h=float(data['lowPrice']),
                timestamp=datetime.now(),
                bid=float(data['bidPrice']),
                ask=float(data['askPrice']),
                spread=float(data['askPrice']) - float(data['bidPrice'])
            )
            
        except Exception as e:
            print(f"❌ Erro ao obter ticker Binance {symbol}: {e}")
            return None
    
    def get_klines(self, symbol: str, interval: str = "1h", limit: int = 100) -> List[Dict[str, Any]]:
        """Obtém dados de candlestick"""
        try:
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': symbol.upper(),
                'interval': interval,
                'limit': limit
            }
            
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            klines = []
            for kline in data:
                klines.append({
                    'timestamp': datetime.fromtimestamp(kline[0] / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                })
            
            return klines
            
        except Exception as e:
            print(f"❌ Erro ao obter klines Binance {symbol}: {e}")
            return []
    
    def subscribe_ticker(self, symbol: str, callback: Callable[[MarketData], None]):
        """Subscreve a atualizações de ticker via WebSocket"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                
                market_data = MarketData(
                    symbol=symbol,
                    exchange=ExchangeType.BINANCE,
                    price=float(data['c']),
                    volume_24h=float(data['v']),
                    change_24h=float(data['P']),
                    high_24h=float(data['h']),
                    low_24h=float(data['l']),
                    timestamp=datetime.now()
                )
                
                callback(market_data)
                
            except Exception as e:
                print(f"❌ Erro no WebSocket Binance: {e}")
        
        def on_error(ws, error):
            print(f"❌ Erro WebSocket Binance: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"🔌 WebSocket Binance fechado: {symbol}")
        
        stream = f"{symbol.lower()}@ticker"
        ws_url = f"{self.ws_url}{stream}"
        
        ws = websocket.WebSocketApp(
            ws_url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        # Executar em thread separada
        def run_ws():
            ws.run_forever()
        
        ws_thread = threading.Thread(target=run_ws, daemon=True)
        ws_thread.start()
        
        self.ws_connections[symbol] = ws
        return ws

class CoinbaseProConnector:
    """Conector para Coinbase Pro"""
    
    def __init__(self, config: ExchangeConfig):
        self.config = config
        self.base_url = "https://api.pro.coinbase.com" if not config.sandbox else "https://api-public.sandbox.pro.coinbase.com"
        self.session = requests.Session()
    
    def get_ticker(self, symbol: str) -> Optional[MarketData]:
        """Obtém ticker de um símbolo"""
        try:
            # Coinbase usa formato BTC-USD
            symbol_formatted = symbol.replace('USDT', 'USD').replace('BTC', 'BTC-USD')
            if '-' not in symbol_formatted:
                symbol_formatted = f"{symbol_formatted}-USD"
            
            url = f"{self.base_url}/products/{symbol_formatted}/ticker"
            
            response = self.session.get(url, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # Obter estatísticas 24h
            stats_url = f"{self.base_url}/products/{symbol_formatted}/stats"
            stats_response = self.session.get(stats_url, timeout=self.config.timeout)
            stats_data = stats_response.json() if stats_response.status_code == 200 else {}
            
            return MarketData(
                symbol=symbol,
                exchange=ExchangeType.COINBASE_PRO,
                price=float(data['price']),
                volume_24h=float(stats_data.get('volume', 0)),
                change_24h=0,  # Calcular se necessário
                high_24h=float(stats_data.get('high', data['price'])),
                low_24h=float(stats_data.get('low', data['price'])),
                timestamp=datetime.now(),
                bid=float(data['bid']),
                ask=float(data['ask']),
                spread=float(data['ask']) - float(data['bid'])
            )
            
        except Exception as e:
            print(f"❌ Erro ao obter ticker Coinbase Pro {symbol}: {e}")
            return None

class CoinGeckoConnector:
    """Conector para CoinGecko (dados gratuitos)"""
    
    def __init__(self, config: ExchangeConfig):
        self.config = config
        self.base_url = "https://api.coingecko.com/api/v3"
        self.session = requests.Session()
        
        # Mapeamento de símbolos
        self.symbol_map = {
            'BTCUSDT': 'bitcoin',
            'ETHUSDT': 'ethereum',
            'ADAUSDT': 'cardano',
            'DOTUSDT': 'polkadot',
            'LINKUSDT': 'chainlink',
            'LTCUSDT': 'litecoin',
            'BCHUSDT': 'bitcoin-cash',
            'XLMUSDT': 'stellar',
            'XRPUSDT': 'ripple',
            'EOSUSDT': 'eos'
        }
    
    def get_ticker(self, symbol: str) -> Optional[MarketData]:
        """Obtém ticker de um símbolo"""
        try:
            coin_id = self.symbol_map.get(symbol.upper())
            if not coin_id:
                return None
            
            url = f"{self.base_url}/simple/price"
            params = {
                'ids': coin_id,
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true'
            }
            
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            coin_data = data.get(coin_id, {})
            
            return MarketData(
                symbol=symbol,
                exchange=ExchangeType.COINGECKO,
                price=float(coin_data.get('usd', 0)),
                volume_24h=float(coin_data.get('usd_24h_vol', 0)),
                change_24h=float(coin_data.get('usd_24h_change', 0)),
                high_24h=0,  # CoinGecko não fornece
                low_24h=0,   # CoinGecko não fornece
                timestamp=datetime.now()
            )
            
        except Exception as e:
            print(f"❌ Erro ao obter ticker CoinGecko {symbol}: {e}")
            return None
    
    def get_market_data(self, symbols: List[str]) -> List[MarketData]:
        """Obtém dados de múltiplos símbolos"""
        try:
            coin_ids = []
            symbol_to_id = {}
            
            for symbol in symbols:
                coin_id = self.symbol_map.get(symbol.upper())
                if coin_id:
                    coin_ids.append(coin_id)
                    symbol_to_id[coin_id] = symbol
            
            if not coin_ids:
                return []
            
            url = f"{self.base_url}/simple/price"
            params = {
                'ids': ','.join(coin_ids),
                'vs_currencies': 'usd',
                'include_24hr_change': 'true',
                'include_24hr_vol': 'true'
            }
            
            response = self.session.get(url, params=params, timeout=self.config.timeout)
            response.raise_for_status()
            
            data = response.json()
            market_data_list = []
            
            for coin_id, coin_data in data.items():
                symbol = symbol_to_id.get(coin_id)
                if symbol:
                    market_data = MarketData(
                        symbol=symbol,
                        exchange=ExchangeType.COINGECKO,
                        price=float(coin_data.get('usd', 0)),
                        volume_24h=float(coin_data.get('usd_24h_vol', 0)),
                        change_24h=float(coin_data.get('usd_24h_change', 0)),
                        high_24h=0,
                        low_24h=0,
                        timestamp=datetime.now()
                    )
                    market_data_list.append(market_data)
            
            return market_data_list
            
        except Exception as e:
            print(f"❌ Erro ao obter dados CoinGecko: {e}")
            return []

class ExchangeManager:
    """Gerenciador principal de exchanges"""
    
    def __init__(self):
        self.connectors: Dict[ExchangeType, Any] = {}
        self.market_data_cache: Dict[str, MarketData] = {}
        self.subscribers: Dict[str, List[Callable]] = {}
        self.update_threads: Dict[str, threading.Thread] = {}
        
        # Configurações padrão
        self.default_symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
        self.update_interval = 30  # segundos
        
        # Inicializar conectores padrão
        self._init_default_connectors()
        
        # Iniciar atualizações automáticas
        self._start_auto_updates()
    
    def _init_default_connectors(self):
        """Inicializa conectores padrão"""
        # CoinGecko (gratuito)
        coingecko_config = ExchangeConfig(
            exchange=ExchangeType.COINGECKO,
            rate_limit=50,  # CoinGecko tem limite baixo
            timeout=30
        )
        self.connectors[ExchangeType.COINGECKO] = CoinGeckoConnector(coingecko_config)
        
        # Binance (se configurado)
        binance_config = ExchangeConfig(
            exchange=ExchangeType.BINANCE,
            sandbox=True,  # Usar testnet por padrão
            rate_limit=1200,
            timeout=30
        )
        self.connectors[ExchangeType.BINANCE] = BinanceConnector(binance_config)
    
    def add_exchange(self, config: ExchangeConfig):
        """Adiciona nova exchange"""
        if config.exchange == ExchangeType.BINANCE:
            self.connectors[config.exchange] = BinanceConnector(config)
        elif config.exchange == ExchangeType.COINBASE_PRO:
            self.connectors[config.exchange] = CoinbaseProConnector(config)
        elif config.exchange == ExchangeType.COINGECKO:
            self.connectors[config.exchange] = CoinGeckoConnector(config)
    
    def get_best_price(self, symbol: str) -> Optional[MarketData]:
        """Obtém melhor preço entre todas as exchanges"""
        prices = []
        
        for exchange_type, connector in self.connectors.items():
            try:
                market_data = connector.get_ticker(symbol)
                if market_data:
                    prices.append(market_data)
            except Exception as e:
                print(f"❌ Erro ao obter preço de {exchange_type.value}: {e}")
        
        if not prices:
            return None
        
        # Retornar preço médio ou da exchange preferida
        if len(prices) == 1:
            return prices[0]
        
        # Calcular preço médio
        avg_price = sum(p.price for p in prices) / len(prices)
        best_data = prices[0]  # Usar primeira como base
        best_data.price = avg_price
        best_data.exchange = ExchangeType.COINGECKO  # Marcar como agregado
        
        return best_data
    
    def get_market_overview(self) -> List[MarketData]:
        """Obtém visão geral do mercado"""
        # Usar CoinGecko para obter dados de múltiplos símbolos
        coingecko = self.connectors.get(ExchangeType.COINGECKO)
        if coingecko:
            return coingecko.get_market_data(self.default_symbols)
        
        # Fallback: obter individualmente
        market_data = []
        for symbol in self.default_symbols:
            data = self.get_best_price(symbol)
            if data:
                market_data.append(data)
        
        return market_data
    
    def subscribe_to_updates(self, symbol: str, callback: Callable[[MarketData], None]):
        """Subscreve a atualizações de preço"""
        if symbol not in self.subscribers:
            self.subscribers[symbol] = []
        
        self.subscribers[symbol].append(callback)
        
        # Iniciar WebSocket se disponível
        binance = self.connectors.get(ExchangeType.BINANCE)
        if binance and symbol not in self.update_threads:
            def update_callback(market_data):
                self.market_data_cache[symbol] = market_data
                for cb in self.subscribers.get(symbol, []):
                    try:
                        cb(market_data)
                    except Exception as e:
                        print(f"❌ Erro no callback: {e}")
            
            binance.subscribe_ticker(symbol, update_callback)
    
    def _start_auto_updates(self):
        """Inicia atualizações automáticas"""
        def update_loop():
            while True:
                try:
                    # Atualizar dados de mercado
                    market_data = self.get_market_overview()
                    
                    for data in market_data:
                        self.market_data_cache[data.symbol] = data
                        
                        # Notificar subscribers
                        for callback in self.subscribers.get(data.symbol, []):
                            try:
                                callback(data)
                            except Exception as e:
                                print(f"❌ Erro no callback: {e}")
                    
                    time.sleep(self.update_interval)
                    
                except Exception as e:
                    print(f"❌ Erro na atualização automática: {e}")
                    time.sleep(60)
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def get_cached_data(self, symbol: str) -> Optional[MarketData]:
        """Obtém dados do cache"""
        return self.market_data_cache.get(symbol)
    
    def get_exchange_status(self) -> Dict[str, Any]:
        """Obtém status das exchanges"""
        status = {}
        
        for exchange_type, connector in self.connectors.items():
            try:
                # Testar conectividade
                test_data = connector.get_ticker('BTCUSDT')
                status[exchange_type.value] = {
                    'connected': test_data is not None,
                    'last_update': datetime.now().isoformat(),
                    'error': None
                }
            except Exception as e:
                status[exchange_type.value] = {
                    'connected': False,
                    'last_update': None,
                    'error': str(e)
                }
        
        return status

# Instância global do gerenciador de exchanges
exchange_manager = ExchangeManager()
