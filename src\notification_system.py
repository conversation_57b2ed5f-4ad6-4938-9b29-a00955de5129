"""
Sistema de Notificações em Tempo Real
Alertas por email, push notifications e webhooks
"""

import smtplib
import json
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
from dataclasses import dataclass
from enum import Enum
import sqlite3
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NotificationType(Enum):
    EMAIL = "email"
    PUSH = "push"
    WEBHOOK = "webhook"
    TELEGRAM = "telegram"
    SMS = "sms"

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    SUCCESS = "success"

@dataclass
class NotificationTemplate:
    id: str
    name: str
    subject: str
    html_content: str
    text_content: str
    variables: List[str]

@dataclass
class Alert:
    id: str
    user_id: str
    symbol: str
    alert_type: str
    condition: str
    target_value: float
    current_value: float
    level: AlertLevel
    created_at: datetime
    triggered_at: Optional[datetime] = None
    is_active: bool = True

class NotificationSystem:
    def __init__(self, db_path: str = "notifications.db"):
        self.db_path = db_path
        self.init_database()
        self.email_config = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '',  # Configurar em produção
            'password': '',  # Configurar em produção
            'from_email': '<EMAIL>'
        }
        self.telegram_config = {
            'bot_token': '',  # Configurar em produção
            'api_url': 'https://api.telegram.org/bot'
        }
        
    def init_database(self):
        """Inicializa o banco de dados de notificações"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabela de templates
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_templates (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    html_content TEXT NOT NULL,
                    text_content TEXT NOT NULL,
                    variables TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de alertas
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    condition TEXT NOT NULL,
                    target_value REAL NOT NULL,
                    current_value REAL NOT NULL,
                    level TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    triggered_at TIMESTAMP
                )
            ''')
            
            # Tabela de histórico de notificações
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notification_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    notification_type TEXT NOT NULL,
                    recipient TEXT NOT NULL,
                    subject TEXT,
                    content TEXT NOT NULL,
                    status TEXT NOT NULL,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    error_message TEXT
                )
            ''')
            
            # Tabela de preferências de usuário
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_notification_preferences (
                    user_id TEXT PRIMARY KEY,
                    email_enabled BOOLEAN DEFAULT TRUE,
                    push_enabled BOOLEAN DEFAULT TRUE,
                    telegram_enabled BOOLEAN DEFAULT FALSE,
                    sms_enabled BOOLEAN DEFAULT FALSE,
                    telegram_chat_id TEXT,
                    phone_number TEXT,
                    email_frequency TEXT DEFAULT 'immediate',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            # Criar templates padrão
            self.create_default_templates()
            
            logger.info("Sistema de notificações inicializado com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao inicializar banco de notificações: {e}")
            
    def create_default_templates(self):
        """Cria templates padrão de notificação"""
        templates = [
            NotificationTemplate(
                id="price_alert",
                name="Alerta de Preço",
                subject="🚨 Alerta de Preço - {symbol}",
                html_content="""
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); padding: 20px; text-align: center;">
                        <h1 style="color: #000; margin: 0;">CryptoSignals</h1>
                        <p style="color: #000; margin: 5px 0;">Alerta de Preço Ativado</p>
                    </div>
                    <div style="padding: 30px; background: #1a1a1a; color: #fff;">
                        <h2 style="color: #FFD700;">🚨 {symbol} atingiu o preço alvo!</h2>
                        <p><strong>Preço Atual:</strong> ${current_price}</p>
                        <p><strong>Preço Alvo:</strong> ${target_price}</p>
                        <p><strong>Condição:</strong> {condition}</p>
                        <p><strong>Variação:</strong> {change_percent}%</p>
                        <div style="margin: 30px 0; padding: 20px; background: #0a0a0a; border-radius: 10px;">
                            <h3 style="color: #FFD700;">📊 Análise Técnica</h3>
                            <p>{technical_analysis}</p>
                        </div>
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{dashboard_url}" style="background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); color: #000; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold;">Ver Dashboard</a>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #0a0a0a; text-align: center; color: #888;">
                        <p>CryptoSignals - Sinais Premium de Criptomoedas</p>
                        <p><a href="{unsubscribe_url}" style="color: #FFD700;">Cancelar alertas</a></p>
                    </div>
                </div>
                """,
                text_content="""
                🚨 ALERTA DE PREÇO - {symbol}
                
                {symbol} atingiu o preço alvo!
                
                Preço Atual: ${current_price}
                Preço Alvo: ${target_price}
                Condição: {condition}
                Variação: {change_percent}%
                
                Análise Técnica: {technical_analysis}
                
                Acesse seu dashboard: {dashboard_url}
                
                ---
                CryptoSignals - Sinais Premium de Criptomoedas
                Cancelar alertas: {unsubscribe_url}
                """,
                variables=["symbol", "current_price", "target_price", "condition", "change_percent", "technical_analysis", "dashboard_url", "unsubscribe_url"]
            ),
            
            NotificationTemplate(
                id="welcome_email",
                name="Email de Boas-vindas",
                subject="🎉 Bem-vindo ao CryptoSignals!",
                html_content="""
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); padding: 30px; text-align: center;">
                        <h1 style="color: #000; margin: 0; font-size: 2rem;">CryptoSignals</h1>
                        <p style="color: #000; margin: 10px 0; font-size: 1.2rem;">Bem-vindo à plataforma premium!</p>
                    </div>
                    <div style="padding: 40px; background: #1a1a1a; color: #fff;">
                        <h2 style="color: #FFD700;">🎉 Conta criada com sucesso!</h2>
                        <p>Olá <strong>{user_name}</strong>,</p>
                        <p>Sua conta no CryptoSignals foi criada com sucesso! Você agora tem acesso a:</p>
                        <ul style="color: #ccc; line-height: 1.8;">
                            <li>📊 Análise técnica avançada</li>
                            <li>🚨 Alertas de preço em tempo real</li>
                            <li>📈 Gráficos interativos</li>
                            <li>💎 Sinais premium de trading</li>
                        </ul>
                        <div style="margin: 30px 0; padding: 20px; background: #0a0a0a; border-radius: 10px;">
                            <h3 style="color: #FFD700;">🚀 Próximos Passos</h3>
                            <ol style="color: #ccc; line-height: 1.8;">
                                <li>Acesse seu dashboard</li>
                                <li>Configure seus alertas</li>
                                <li>Explore as análises técnicas</li>
                                <li>Maximize seus lucros!</li>
                            </ol>
                        </div>
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{dashboard_url}" style="background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%); color: #000; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; font-size: 1.1rem;">Acessar Dashboard</a>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #0a0a0a; text-align: center; color: #888;">
                        <p>CryptoSignals - Sinais Premium de Criptomoedas</p>
                        <p>Precisa de ajuda? <a href="mailto:<EMAIL>" style="color: #FFD700;">Entre em contato</a></p>
                    </div>
                </div>
                """,
                text_content="""
                🎉 BEM-VINDO AO CRYPTOSIGNALS!
                
                Olá {user_name},
                
                Sua conta foi criada com sucesso! Você agora tem acesso a:
                
                📊 Análise técnica avançada
                🚨 Alertas de preço em tempo real
                📈 Gráficos interativos
                💎 Sinais premium de trading
                
                Próximos Passos:
                1. Acesse seu dashboard
                2. Configure seus alertas
                3. Explore as análises técnicas
                4. Maximize seus lucros!
                
                Acesse seu dashboard: {dashboard_url}
                
                ---
                CryptoSignals - Sinais Premium de Criptomoedas
                Suporte: <EMAIL>
                """,
                variables=["user_name", "dashboard_url"]
            )
        ]
        
        for template in templates:
            self.save_template(template)
    
    def save_template(self, template: NotificationTemplate):
        """Salva um template no banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO notification_templates 
                (id, name, subject, html_content, text_content, variables)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                template.id,
                template.name,
                template.subject,
                template.html_content,
                template.text_content,
                json.dumps(template.variables)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Erro ao salvar template: {e}")
    
    def send_email(self, to_email: str, subject: str, html_content: str, text_content: str) -> bool:
        """Envia email usando SMTP"""
        try:
            if not self.email_config['username'] or not self.email_config['password']:
                logger.warning("Configuração de email não definida")
                return False
                
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.email_config['from_email']
            msg['To'] = to_email
            
            # Adicionar versões text e HTML
            part1 = MIMEText(text_content, 'plain')
            part2 = MIMEText(html_content, 'html')
            
            msg.attach(part1)
            msg.attach(part2)
            
            # Enviar email
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email enviado com sucesso para {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao enviar email: {e}")
            return False
    
    def send_telegram_message(self, chat_id: str, message: str) -> bool:
        """Envia mensagem via Telegram Bot"""
        try:
            if not self.telegram_config['bot_token']:
                logger.warning("Token do Telegram não configurado")
                return False
                
            url = f"{self.telegram_config['api_url']}{self.telegram_config['bot_token']}/sendMessage"
            
            data = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, data=data)
            
            if response.status_code == 200:
                logger.info(f"Mensagem Telegram enviada para {chat_id}")
                return True
            else:
                logger.error(f"Erro ao enviar Telegram: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao enviar Telegram: {e}")
            return False
    
    def send_webhook(self, webhook_url: str, data: Dict) -> bool:
        """Envia notificação via webhook"""
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(webhook_url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"Webhook enviado com sucesso para {webhook_url}")
                return True
            else:
                logger.error(f"Erro no webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao enviar webhook: {e}")
            return False
    
    def create_alert(self, user_id: str, symbol: str, alert_type: str, 
                    condition: str, target_value: float, current_value: float, 
                    level: AlertLevel = AlertLevel.INFO) -> str:
        """Cria um novo alerta"""
        try:
            alert_id = f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_id}"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alerts 
                (id, user_id, symbol, alert_type, condition, target_value, current_value, level)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert_id, user_id, symbol, alert_type, condition, 
                target_value, current_value, level.value
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Alerta criado: {alert_id}")
            return alert_id
            
        except Exception as e:
            logger.error(f"Erro ao criar alerta: {e}")
            return ""
    
    def check_alerts(self, symbol: str, current_price: float):
        """Verifica alertas ativos para um símbolo"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM alerts 
                WHERE symbol = ? AND is_active = TRUE AND triggered_at IS NULL
            ''', (symbol,))
            
            alerts = cursor.fetchall()
            
            for alert_data in alerts:
                alert = Alert(
                    id=alert_data[0],
                    user_id=alert_data[1],
                    symbol=alert_data[2],
                    alert_type=alert_data[3],
                    condition=alert_data[4],
                    target_value=alert_data[5],
                    current_value=current_price,
                    level=AlertLevel(alert_data[7]),
                    created_at=datetime.fromisoformat(alert_data[9]),
                    triggered_at=None,
                    is_active=bool(alert_data[8])
                )
                
                # Verificar se o alerta deve ser disparado
                should_trigger = False
                
                if alert.condition == "above" and current_price >= alert.target_value:
                    should_trigger = True
                elif alert.condition == "below" and current_price <= alert.target_value:
                    should_trigger = True
                elif alert.condition == "change_percent":
                    change_percent = ((current_price - alert.current_value) / alert.current_value) * 100
                    if abs(change_percent) >= alert.target_value:
                        should_trigger = True
                
                if should_trigger:
                    self.trigger_alert(alert, current_price)
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Erro ao verificar alertas: {e}")
    
    def trigger_alert(self, alert: Alert, current_price: float):
        """Dispara um alerta"""
        try:
            # Marcar alerta como disparado
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE alerts 
                SET triggered_at = CURRENT_TIMESTAMP, current_value = ?
                WHERE id = ?
            ''', (current_price, alert.id))
            
            conn.commit()
            conn.close()
            
            # Enviar notificações
            self.send_alert_notifications(alert, current_price)
            
            logger.info(f"Alerta disparado: {alert.id}")
            
        except Exception as e:
            logger.error(f"Erro ao disparar alerta: {e}")
    
    def send_alert_notifications(self, alert: Alert, current_price: float):
        """Envia notificações para um alerta disparado"""
        try:
            # Buscar preferências do usuário
            prefs = self.get_user_preferences(alert.user_id)
            
            # Preparar dados para o template
            template_data = {
                'symbol': alert.symbol,
                'current_price': f"{current_price:.2f}",
                'target_price': f"{alert.target_value:.2f}",
                'condition': alert.condition,
                'change_percent': f"{((current_price - alert.current_value) / alert.current_value) * 100:.2f}",
                'technical_analysis': "Análise técnica detalhada disponível no dashboard",
                'dashboard_url': "http://localhost:5000/dashboard",
                'unsubscribe_url': f"http://localhost:5000/unsubscribe/{alert.user_id}"
            }
            
            # Enviar email se habilitado
            if prefs.get('email_enabled', True):
                self.send_template_email(alert.user_id, "price_alert", template_data)
            
            # Enviar Telegram se habilitado
            if prefs.get('telegram_enabled', False) and prefs.get('telegram_chat_id'):
                telegram_message = f"""
🚨 <b>ALERTA DE PREÇO</b>

<b>{alert.symbol}</b> atingiu o preço alvo!

💰 <b>Preço Atual:</b> ${current_price:.2f}
🎯 <b>Preço Alvo:</b> ${alert.target_value:.2f}
📊 <b>Condição:</b> {alert.condition}
📈 <b>Variação:</b> {template_data['change_percent']}%

<a href="{template_data['dashboard_url']}">Ver Dashboard</a>
                """
                self.send_telegram_message(prefs['telegram_chat_id'], telegram_message)
            
        except Exception as e:
            logger.error(f"Erro ao enviar notificações do alerta: {e}")
    
    def send_template_email(self, user_id: str, template_id: str, data: Dict) -> bool:
        """Envia email usando um template"""
        try:
            # Buscar template
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM notification_templates WHERE id = ?', (template_id,))
            template_data = cursor.fetchone()
            
            if not template_data:
                logger.error(f"Template não encontrado: {template_id}")
                return False
            
            # Buscar email do usuário (assumindo que temos acesso ao sistema de auth)
            user_email = self.get_user_email(user_id)
            if not user_email:
                logger.error(f"Email do usuário não encontrado: {user_id}")
                return False
            
            # Processar template
            subject = template_data[2].format(**data)
            html_content = template_data[3].format(**data)
            text_content = template_data[4].format(**data)
            
            # Enviar email
            success = self.send_email(user_email, subject, html_content, text_content)
            
            # Registrar no histórico
            self.log_notification(user_id, NotificationType.EMAIL, user_email, 
                                subject, html_content, "sent" if success else "failed")
            
            conn.close()
            return success
            
        except Exception as e:
            logger.error(f"Erro ao enviar email template: {e}")
            return False
    
    def get_user_preferences(self, user_id: str) -> Dict:
        """Busca preferências de notificação do usuário"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT * FROM user_notification_preferences WHERE user_id = ?', (user_id,))
            prefs_data = cursor.fetchone()
            
            if prefs_data:
                return {
                    'email_enabled': bool(prefs_data[1]),
                    'push_enabled': bool(prefs_data[2]),
                    'telegram_enabled': bool(prefs_data[3]),
                    'sms_enabled': bool(prefs_data[4]),
                    'telegram_chat_id': prefs_data[5],
                    'phone_number': prefs_data[6],
                    'email_frequency': prefs_data[7]
                }
            else:
                # Criar preferências padrão
                cursor.execute('''
                    INSERT INTO user_notification_preferences (user_id)
                    VALUES (?)
                ''', (user_id,))
                conn.commit()
                
                return {
                    'email_enabled': True,
                    'push_enabled': True,
                    'telegram_enabled': False,
                    'sms_enabled': False,
                    'telegram_chat_id': None,
                    'phone_number': None,
                    'email_frequency': 'immediate'
                }
            
            conn.close()
            
        except Exception as e:
            logger.error(f"Erro ao buscar preferências: {e}")
            return {}
    
    def get_user_email(self, user_id: str) -> str:
        """Busca email do usuário (integração com sistema de auth)"""
        # Esta função deve ser integrada com o sistema de autenticação
        # Por enquanto, retorna um placeholder
        return f"user_{user_id}@example.com"
    
    def log_notification(self, user_id: str, notification_type: NotificationType, 
                        recipient: str, subject: str, content: str, status: str, 
                        error_message: str = None):
        """Registra notificação no histórico"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO notification_history 
                (user_id, notification_type, recipient, subject, content, status, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, notification_type.value, recipient, subject, content, status, error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Erro ao registrar notificação: {e}")

# Instância global
notification_system = NotificationSystem()
