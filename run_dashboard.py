#!/usr/bin/env python3
"""
Script para inicializar o dashboard BitcoinAnalytics.
Execute este arquivo para iniciar a aplicação web.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """Verifica se as dependências estão instaladas."""
    try:
        import pandas
        import numpy
        import plotly
        import dash
        import dash_bootstrap_components
        import ta
        import sklearn
        import statsmodels
        print("✅ Todas as dependências estão instaladas!")
        return True
    except ImportError as e:
        print(f"❌ Dependência faltando: {e}")
        print("Execute: pip install -r requirements.txt")
        return False

def create_sample_data():
    """Cria dados de exemplo se o arquivo BTCUSDT.csv não existir."""
    if not os.path.exists("BTCUSDT.csv"):
        print("📊 Arquivo BTCUSDT.csv não encontrado. Criando dados de exemplo...")
        
        # Importar módulo de processamento
        sys.path.append("src")
        from data_processing import BitcoinDataProcessor
        
        # Criar dados de exemplo
        processor = BitcoinDataProcessor("BTCUSDT.csv")
        df = processor._create_sample_data()
        
        # Salvar dados
        df.to_csv("BTCUSDT.csv", index=False)
        print("✅ Dados de exemplo criados em BTCUSDT.csv")

def main():
    """Função principal."""
    print("🪙 BitcoinAnalytics Dashboard")
    print("=" * 50)
    
    # Verificar dependências
    if not check_dependencies():
        return
    
    # Criar dados de exemplo se necessário
    create_sample_data()
    
    # Verificar se o diretório dashboard existe
    if not os.path.exists("dashboard"):
        print("❌ Diretório 'dashboard' não encontrado!")
        return
    
    # Iniciar aplicação
    print("🚀 Iniciando dashboard...")
    print("📱 Acesse: http://localhost:8050")
    print("⏹️  Pressione Ctrl+C para parar")
    print("-" * 50)
    
    try:
        # Executar aplicação
        os.chdir("dashboard")
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Dashboard encerrado!")
    except Exception as e:
        print(f"❌ Erro ao iniciar dashboard: {e}")

if __name__ == "__main__":
    main()
