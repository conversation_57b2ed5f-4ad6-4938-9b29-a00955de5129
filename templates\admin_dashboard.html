<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals - Dashboard Administrativo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0B1B33 0%, #1a2332 100%);
            color: #F8F7F2;
            min-height: 100vh;
        }

        .header {
            background: rgba(212, 160, 23, 0.1);
            padding: 1rem 2rem;
            border-bottom: 2px solid #D4A017;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #D4A017;
            font-size: 1.8rem;
            font-weight: 700;
        }

        .nav-tabs {
            display: flex;
            gap: 1rem;
            margin: 2rem;
            border-bottom: 1px solid rgba(212, 160, 23, 0.3);
        }

        .nav-tab {
            padding: 0.8rem 1.5rem;
            background: transparent;
            border: none;
            color: #E0E0E0;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            color: #D4A017;
            border-bottom-color: #D4A017;
        }

        .nav-tab:hover {
            color: #D4A017;
        }

        .dashboard-content {
            padding: 2rem;
            display: none;
        }

        .dashboard-content.active {
            display: block;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: rgba(248, 247, 242, 0.05);
            border: 1px solid rgba(212, 160, 23, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
        }

        .metric-card h3 {
            color: #D4A017;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #F8F7F2;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #E0E0E0;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-healthy { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-critical { background: #F44336; }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #D4A017;
            color: #0B1B33;
        }

        .btn-primary:hover {
            background: #E5B028;
        }

        .btn-danger {
            background: #F44336;
            color: white;
        }

        .btn-danger:hover {
            background: #D32F2F;
        }

        .btn-secondary {
            background: rgba(224, 224, 224, 0.1);
            color: #E0E0E0;
            border: 1px solid rgba(224, 224, 224, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(224, 224, 224, 0.2);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .data-table th,
        .data-table td {
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid rgba(212, 160, 23, 0.2);
        }

        .data-table th {
            background: rgba(212, 160, 23, 0.1);
            color: #D4A017;
            font-weight: 600;
        }

        .data-table td {
            color: #E0E0E0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(224, 224, 224, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #D4A017, #E5B028);
            transition: width 0.3s ease;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(76, 175, 80, 0.1);
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .alert-warning {
            background: rgba(255, 152, 0, 0.1);
            border-color: #FF9800;
            color: #FF9800;
        }

        .alert-error {
            background: rgba(244, 67, 54, 0.1);
            border-color: #F44336;
            color: #F44336;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(212, 160, 23, 0.3);
            border-radius: 50%;
            border-top-color: #D4A017;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .timestamp {
            font-size: 0.8rem;
            color: #E0E0E0;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ Dashboard Administrativo</h1>
        <div>
            <span id="last-update" class="timestamp">Carregando...</span>
            <button class="btn btn-secondary" onclick="refreshAllData()">🔄 Atualizar</button>
        </div>
    </div>

    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('overview')">📊 Visão Geral</button>
        <button class="nav-tab" onclick="showTab('users')">👥 Usuários</button>
        <button class="nav-tab" onclick="showTab('affiliates')">💰 Afiliados</button>
        <button class="nav-tab" onclick="showTab('security')">🔐 Segurança</button>
        <button class="nav-tab" onclick="showTab('exchanges')">🏦 Exchanges</button>
        <button class="nav-tab" onclick="showTab('ai')">🤖 IA/ML</button>
        <button class="nav-tab" onclick="showTab('cache')">💾 Cache</button>
        <button class="nav-tab" onclick="showTab('backup')">💿 Backup</button>
        <button class="nav-tab" onclick="showTab('monitoring')">📈 Monitoramento</button>
    </div>

    <!-- Overview Tab -->
    <div id="overview" class="dashboard-content active">
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>🎯 Performance Geral</h3>
                <div class="metric-value" id="overall-performance">-</div>
                <div class="metric-label">Taxa de sucesso das operações</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="performance-progress" style="width: 0%"></div>
                </div>
            </div>

            <div class="metric-card">
                <h3>💾 Status do Cache</h3>
                <div class="metric-value" id="cache-hit-rate">-</div>
                <div class="metric-label">Taxa de acerto do cache</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cache-progress" style="width: 0%"></div>
                </div>
            </div>

            <div class="metric-card">
                <h3>⚡ Rate Limiting</h3>
                <div class="metric-value" id="rate-limit-status">-</div>
                <div class="metric-label">Requisições na última hora</div>
            </div>

            <div class="metric-card">
                <h3>💿 Backup</h3>
                <div class="metric-value" id="backup-status">-</div>
                <div class="metric-label">Status do último backup</div>
            </div>
        </div>

        <div id="alerts-container">
            <!-- Alertas serão inseridos aqui -->
        </div>
    </div>

    <!-- Users Tab -->
    <div id="users" class="dashboard-content">
        <div class="metric-card">
            <h3>👥 Gestão de Usuários</h3>
            <div id="users-stats">
                <div class="loading"></div> Carregando estatísticas...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshUsersStats()">🔄 Atualizar</button>
                <button class="btn btn-secondary" onclick="exportUsers()">📊 Exportar</button>
            </div>
        </div>

        <div class="metric-card">
            <h3>📋 Usuários Recentes</h3>
            <div id="recent-users">
                <div class="loading"></div> Carregando lista...
            </div>
        </div>
    </div>

    <!-- Affiliates Tab -->
    <div id="affiliates" class="dashboard-content">
        <div class="metric-card">
            <h3>💰 Programa de Afiliados</h3>
            <div id="affiliates-stats">
                <div class="loading"></div> Carregando estatísticas...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshAffiliatesStats()">🔄 Atualizar</button>
                <button class="btn btn-secondary" onclick="approveCommissions()">✅ Aprovar Comissões</button>
            </div>
        </div>

        <div class="metric-card">
            <h3>🏆 Top Afiliados</h3>
            <div id="top-affiliates">
                <div class="loading"></div> Carregando ranking...
            </div>
        </div>
    </div>

    <!-- Security Tab -->
    <div id="security" class="dashboard-content">
        <div class="metric-card">
            <h3>🔐 Eventos de Segurança</h3>
            <div id="security-events">
                <div class="loading"></div> Carregando eventos...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshSecurityEvents()">🔄 Atualizar</button>
                <button class="btn btn-danger" onclick="blockSuspiciousIPs()">🚫 Bloquear IPs Suspeitos</button>
            </div>
        </div>

        <div class="metric-card">
            <h3>📊 Estatísticas de Segurança</h3>
            <div id="security-stats">
                <div class="loading"></div> Carregando estatísticas...
            </div>
        </div>
    </div>

    <!-- Exchanges Tab -->
    <div id="exchanges" class="dashboard-content">
        <div class="metric-card">
            <h3>🏦 Status das Exchanges</h3>
            <div id="exchanges-status">
                <div class="loading"></div> Carregando status...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshExchangesStatus()">🔄 Atualizar</button>
                <button class="btn btn-secondary" onclick="testExchangeConnections()">🔗 Testar Conexões</button>
            </div>
        </div>

        <div class="metric-card">
            <h3>📈 Dados de Mercado</h3>
            <div id="market-overview">
                <div class="loading"></div> Carregando dados...
            </div>
        </div>
    </div>

    <!-- AI Tab -->
    <div id="ai" class="dashboard-content">
        <div class="metric-card">
            <h3>🤖 Status dos Modelos de IA</h3>
            <div id="ai-models-status">
                <div class="loading"></div> Carregando status...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshAIStatus()">🔄 Atualizar</button>
                <button class="btn btn-secondary" onclick="trainModels()">🎯 Treinar Modelos</button>
            </div>
        </div>

        <div class="metric-card">
            <h3>📊 Performance dos Modelos</h3>
            <div id="ai-performance">
                <div class="loading"></div> Carregando performance...
            </div>
        </div>
    </div>

    <!-- Cache Tab -->
    <div id="cache" class="dashboard-content">
        <div class="metric-card">
            <h3>💾 Estatísticas do Cache</h3>
            <div id="cache-stats">
                <div class="loading"></div> Carregando estatísticas...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshCacheStats()">🔄 Atualizar</button>
                <button class="btn btn-danger" onclick="clearCache('all')">🗑️ Limpar Tudo</button>
                <button class="btn btn-secondary" onclick="clearCache('price_data')">📊 Limpar Preços</button>
                <button class="btn btn-secondary" onclick="clearCache('analysis')">📈 Limpar Análises</button>
            </div>
        </div>
    </div>

    <!-- Rate Limits Tab -->
    <div id="rate-limits" class="dashboard-content">
        <div class="metric-card">
            <h3>⚡ Analytics de Rate Limiting</h3>
            <div id="rate-limit-analytics">
                <div class="loading"></div> Carregando analytics...
            </div>
        </div>
    </div>

    <!-- Backup Tab -->
    <div id="backup" class="dashboard-content">
        <div class="metric-card">
            <h3>💿 Status dos Backups</h3>
            <div id="backup-info">
                <div class="loading"></div> Carregando informações...
            </div>
            <div class="action-buttons">
                <button class="btn btn-primary" onclick="createBackup('full')">📦 Backup Completo</button>
                <button class="btn btn-secondary" onclick="createBackup('incremental')">📋 Backup Incremental</button>
                <button class="btn btn-secondary" onclick="refreshBackupInfo()">🔄 Atualizar</button>
            </div>
        </div>

        <div class="metric-card">
            <h3>📋 Backups Recentes</h3>
            <div id="recent-backups">
                <div class="loading"></div> Carregando lista...
            </div>
        </div>
    </div>

    <!-- Monitoring Tab -->
    <div id="monitoring" class="dashboard-content">
        <div class="metric-card">
            <h3>📈 Métricas de Monitoramento</h3>
            <div id="monitoring-metrics">
                <div class="loading"></div> Carregando métricas...
            </div>
        </div>
    </div>

    <script>
        // Estado global
        let currentTab = 'overview';
        let refreshInterval;

        // Navegação entre tabs
        function showTab(tabName) {
            // Esconder todas as tabs
            document.querySelectorAll('.dashboard-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remover classe active de todos os botões
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Mostrar tab selecionada
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            currentTab = tabName;

            // Carregar dados da tab
            loadTabData(tabName);
        }

        // Carregar dados específicos da tab
        function loadTabData(tabName) {
            switch(tabName) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'users':
                    refreshUsersStats();
                    break;
                case 'affiliates':
                    refreshAffiliatesStats();
                    break;
                case 'security':
                    refreshSecurityEvents();
                    break;
                case 'exchanges':
                    refreshExchangesStatus();
                    break;
                case 'ai':
                    refreshAIStatus();
                    break;
                case 'cache':
                    refreshCacheStats();
                    break;
                case 'backup':
                    refreshBackupInfo();
                    break;
                case 'monitoring':
                    loadMonitoringMetrics();
                    break;
            }
        }

        // Carregar dados da visão geral
        async function loadOverviewData() {
            try {
                // Carregar múltiplas APIs em paralelo
                const [cacheResponse, rateLimitResponse, backupResponse, monitoringResponse] = await Promise.all([
                    fetch('/admin/cache/status'),
                    fetch('/admin/rate-limits/status'),
                    fetch('/admin/backup/status'),
                    fetch('/admin/monitoring/metrics')
                ]);

                const cacheData = await cacheResponse.json();
                const rateLimitData = await rateLimitResponse.json();
                const backupData = await backupResponse.json();
                const monitoringData = await monitoringResponse.json();

                // Atualizar métricas
                if (cacheData.success) {
                    const hitRate = cacheData.cache_stats.hit_rate;
                    document.getElementById('cache-hit-rate').textContent = `${hitRate.toFixed(1)}%`;
                    document.getElementById('cache-progress').style.width = `${hitRate}%`;
                }

                if (rateLimitData.success) {
                    const totalRequests = rateLimitData.rate_limit_analytics.total_requests;
                    document.getElementById('rate-limit-status').textContent = totalRequests;
                }

                if (backupData.success) {
                    const status = backupData.backup_status.status;
                    const statusText = status === 'healthy' ? '✅ Saudável' :
                                     status === 'warning' ? '⚠️ Atenção' : '❌ Crítico';
                    document.getElementById('backup-status').textContent = statusText;
                }

                if (monitoringData.success) {
                    const successRate = monitoringData.metrics.success_rate || 0;
                    document.getElementById('overall-performance').textContent = `${successRate.toFixed(1)}%`;
                    document.getElementById('performance-progress').style.width = `${successRate}%`;
                }

                updateTimestamp();

            } catch (error) {
                console.error('Erro ao carregar dados da visão geral:', error);
                showAlert('Erro ao carregar dados da visão geral', 'error');
            }
        }

        // Atualizar estatísticas do cache
        async function refreshCacheStats() {
            try {
                const response = await fetch('/admin/cache/status');
                const data = await response.json();

                if (data.success) {
                    const stats = data.cache_stats;
                    document.getElementById('cache-stats').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">${stats.hit_rate.toFixed(1)}%</div>
                                <div class="metric-label">Taxa de Acerto</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.total_requests}</div>
                                <div class="metric-label">Total de Requisições</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.memory_entries}</div>
                                <div class="metric-label">Entradas em Memória</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.redis_available ? '✅' : '❌'}</div>
                                <div class="metric-label">Redis Disponível</div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar stats do cache:', error);
                showAlert('Erro ao carregar estatísticas do cache', 'error');
            }
        }

        // Limpar cache
        async function clearCache(namespace) {
            if (!confirm(`Tem certeza que deseja limpar o cache ${namespace}?`)) {
                return;
            }

            try {
                const response = await fetch('/admin/cache/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ namespace })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(data.message, 'success');
                    refreshCacheStats();
                } else {
                    showAlert(data.error || 'Erro ao limpar cache', 'error');
                }
            } catch (error) {
                console.error('Erro ao limpar cache:', error);
                showAlert('Erro ao limpar cache', 'error');
            }
        }

        // Carregar analytics de rate limiting
        async function loadRateLimitAnalytics() {
            try {
                const response = await fetch('/admin/rate-limits/status');
                const data = await response.json();

                if (data.success) {
                    const analytics = data.rate_limit_analytics;
                    document.getElementById('rate-limit-analytics').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">${analytics.total_requests}</div>
                                <div class="metric-label">Total de Requisições</div>
                            </div>
                            <div>
                                <div class="metric-value">${analytics.successful_requests}</div>
                                <div class="metric-label">Requisições Bem-sucedidas</div>
                            </div>
                            <div>
                                <div class="metric-value">${analytics.failed_requests}</div>
                                <div class="metric-label">Requisições Falharam</div>
                            </div>
                            <div>
                                <div class="metric-value">${analytics.success_rate.toFixed(1)}%</div>
                                <div class="metric-label">Taxa de Sucesso</div>
                            </div>
                            <div>
                                <div class="metric-value">${analytics.blocked_ips}</div>
                                <div class="metric-label">IPs Bloqueados</div>
                            </div>
                            <div>
                                <div class="metric-value">${analytics.active_users}</div>
                                <div class="metric-label">Usuários Ativos</div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar analytics de rate limiting:', error);
                showAlert('Erro ao carregar analytics de rate limiting', 'error');
            }
        }

        // Atualizar informações de backup
        async function refreshBackupInfo() {
            try {
                const response = await fetch('/admin/backup/status');
                const data = await response.json();

                if (data.success) {
                    const status = data.backup_status;
                    const backups = data.recent_backups;

                    document.getElementById('backup-info').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">${status.total_backups}</div>
                                <div class="metric-label">Total de Backups</div>
                            </div>
                            <div>
                                <div class="metric-value">${status.total_size_mb.toFixed(1)} MB</div>
                                <div class="metric-label">Tamanho Total</div>
                            </div>
                            <div>
                                <div class="metric-value">${status.hours_since_last.toFixed(1)}h</div>
                                <div class="metric-label">Último Backup</div>
                            </div>
                            <div>
                                <div class="metric-value">
                                    <span class="status-indicator status-${status.status}"></span>
                                    ${status.status}
                                </div>
                                <div class="metric-label">Status</div>
                            </div>
                        </div>
                    `;

                    // Atualizar lista de backups recentes
                    const backupsList = backups.map(backup => `
                        <tr>
                            <td>${backup.id}</td>
                            <td>${backup.type}</td>
                            <td>${backup.size_mb.toFixed(1)} MB</td>
                            <td>${new Date(backup.timestamp).toLocaleString()}</td>
                        </tr>
                    `).join('');

                    document.getElementById('recent-backups').innerHTML = `
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tipo</th>
                                    <th>Tamanho</th>
                                    <th>Data</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${backupsList}
                            </tbody>
                        </table>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar informações de backup:', error);
                showAlert('Erro ao carregar informações de backup', 'error');
            }
        }

        // Criar backup
        async function createBackup(type) {
            try {
                showAlert('Criando backup...', 'warning');

                const response = await fetch('/admin/backup/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        type,
                        description: `Backup ${type} manual via dashboard`
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(`Backup ${type} criado com sucesso!`, 'success');
                    refreshBackupInfo();
                } else {
                    showAlert(data.error || 'Erro ao criar backup', 'error');
                }
            } catch (error) {
                console.error('Erro ao criar backup:', error);
                showAlert('Erro ao criar backup', 'error');
            }
        }

        // Carregar métricas de monitoramento
        async function loadMonitoringMetrics() {
            try {
                const response = await fetch('/admin/monitoring/metrics');
                const data = await response.json();

                if (data.success) {
                    const metrics = data.metrics;
                    const health = data.health;

                    document.getElementById('monitoring-metrics').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                            <div>
                                <div class="metric-value">${metrics.total_operations || 0}</div>
                                <div class="metric-label">Operações Totais</div>
                            </div>
                            <div>
                                <div class="metric-value">${(metrics.success_rate || 0).toFixed(1)}%</div>
                                <div class="metric-label">Taxa de Sucesso</div>
                            </div>
                            <div>
                                <div class="metric-value">${(metrics.avg_response_time || 0).toFixed(1)}ms</div>
                                <div class="metric-label">Tempo Médio</div>
                            </div>
                            <div>
                                <div class="metric-value">${(metrics.operations_per_minute || 0).toFixed(1)}</div>
                                <div class="metric-label">Ops/Minuto</div>
                            </div>
                        </div>

                        <h4 style="color: #D4A017; margin-bottom: 1rem;">Saúde do Sistema</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">
                                    <span class="status-indicator status-${health.status || 'unknown'}"></span>
                                    ${health.status || 'Unknown'}
                                </div>
                                <div class="metric-label">Status Geral</div>
                            </div>
                            <div>
                                <div class="metric-value">${(health.cpu_usage || 0).toFixed(1)}%</div>
                                <div class="metric-label">CPU</div>
                            </div>
                            <div>
                                <div class="metric-value">${(health.memory_usage || 0).toFixed(1)}%</div>
                                <div class="metric-label">Memória</div>
                            </div>
                            <div>
                                <div class="metric-value">${health.active_connections || 0}</div>
                                <div class="metric-label">Conexões Ativas</div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar métricas de monitoramento:', error);
                showAlert('Erro ao carregar métricas de monitoramento', 'error');
            }
        }

        // Atualizar todos os dados
        function refreshAllData() {
            loadTabData(currentTab);
            updateTimestamp();
        }

        // Mostrar alerta
        function showAlert(message, type) {
            const alertsContainer = document.getElementById('alerts-container');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertsContainer.appendChild(alert);

            // Remover alerta após 5 segundos
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // Atualizar timestamp
        function updateTimestamp() {
            document.getElementById('last-update').textContent =
                `Última atualização: ${new Date().toLocaleTimeString()}`;
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            loadOverviewData();

            // Auto-refresh a cada 30 segundos
            refreshInterval = setInterval(refreshAllData, 30000);
        });

        // Funções para Usuários
        async function refreshUsersStats() {
            try {
                const response = await fetch('/admin/users/stats');
                const data = await response.json();

                if (data.success) {
                    const stats = data.user_stats;
                    document.getElementById('users-stats').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">${stats.total_users}</div>
                                <div class="metric-label">Total de Usuários</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.active_users}</div>
                                <div class="metric-label">Usuários Ativos</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.new_users_today}</div>
                                <div class="metric-label">Novos Hoje</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.premium_users}</div>
                                <div class="metric-label">Usuários Premium</div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar stats de usuários:', error);
                showAlert('Erro ao carregar estatísticas de usuários', 'error');
            }
        }

        // Funções para Afiliados
        async function refreshAffiliatesStats() {
            try {
                const response = await fetch('/admin/affiliates/stats');
                const data = await response.json();

                if (data.success) {
                    const stats = data.affiliate_stats;
                    document.getElementById('affiliates-stats').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">${stats.total_affiliates}</div>
                                <div class="metric-label">Total de Afiliados</div>
                            </div>
                            <div>
                                <div class="metric-value">${stats.active_affiliates}</div>
                                <div class="metric-label">Afiliados Ativos</div>
                            </div>
                            <div>
                                <div class="metric-value">$${stats.total_earnings.toFixed(2)}</div>
                                <div class="metric-label">Total de Ganhos</div>
                            </div>
                            <div>
                                <div class="metric-value">$${stats.pending_earnings.toFixed(2)}</div>
                                <div class="metric-label">Ganhos Pendentes</div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar stats de afiliados:', error);
                showAlert('Erro ao carregar estatísticas de afiliados', 'error');
            }
        }

        // Funções para Segurança
        async function refreshSecurityEvents() {
            try {
                const response = await fetch('/admin/security/events');
                const data = await response.json();

                if (data.success) {
                    const events = data.security_events;
                    document.getElementById('security-events').innerHTML = `
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Tipo</th>
                                    <th>Usuário</th>
                                    <th>IP</th>
                                    <th>Risk Score</th>
                                    <th>Data</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${events.map(event => `
                                    <tr>
                                        <td>${event.event_type}</td>
                                        <td>${event.user_id}</td>
                                        <td>${event.ip_address}</td>
                                        <td><span class="status-indicator status-${event.risk_score > 70 ? 'critical' : event.risk_score > 40 ? 'warning' : 'healthy'}"></span>${event.risk_score}</td>
                                        <td>${new Date(event.timestamp).toLocaleString()}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar eventos de segurança:', error);
                showAlert('Erro ao carregar eventos de segurança', 'error');
            }
        }

        // Funções para Exchanges
        async function refreshExchangesStatus() {
            try {
                const response = await fetch('/admin/exchanges/status');
                const data = await response.json();

                if (data.success) {
                    const status = data.exchanges_status;
                    document.getElementById('exchanges-status').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            ${Object.entries(status).map(([exchange, info]) => `
                                <div style="padding: 1rem; border: 1px solid rgba(212, 160, 23, 0.3); border-radius: 8px;">
                                    <h4 style="color: #D4A017; margin-bottom: 0.5rem;">${exchange.toUpperCase()}</h4>
                                    <div>
                                        <span class="status-indicator status-${info.connected ? 'healthy' : 'critical'}"></span>
                                        ${info.connected ? 'Conectado' : 'Desconectado'}
                                    </div>
                                    ${info.last_update ? `<div class="timestamp">Última atualização: ${new Date(info.last_update).toLocaleString()}</div>` : ''}
                                    ${info.error ? `<div style="color: #F44336; font-size: 0.8rem;">${info.error}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erro ao carregar status das exchanges:', error);
                showAlert('Erro ao carregar status das exchanges', 'error');
            }
        }

        // Funções para IA
        async function refreshAIStatus() {
            try {
                const response = await fetch('/ai/status');
                const data = await response.json();

                if (data.success) {
                    const aiStatus = data.ai_status;
                    document.getElementById('ai-models-status').innerHTML = `
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                            <div>
                                <div class="metric-value">${aiStatus.ml_available ? '✅' : '❌'}</div>
                                <div class="metric-label">ML Disponível</div>
                            </div>
                            <div>
                                <div class="metric-value">${aiStatus.models_trained}</div>
                                <div class="metric-label">Modelos Treinados</div>
                            </div>
                            <div>
                                <div class="metric-value">${aiStatus.training_samples}</div>
                                <div class="metric-label">Amostras de Treino</div>
                            </div>
                            <div>
                                <div class="metric-value">${aiStatus.cache_size}</div>
                                <div class="metric-label">Cache de Predições</div>
                            </div>
                        </div>
                    `;

                    // Mostrar performance dos modelos
                    if (aiStatus.models) {
                        const modelsHtml = Object.entries(aiStatus.models).map(([model, info]) => `
                            <div style="padding: 1rem; border: 1px solid rgba(212, 160, 23, 0.3); border-radius: 8px; margin-bottom: 1rem;">
                                <h4 style="color: #D4A017;">${model}</h4>
                                <div>Accuracy: ${(info.accuracy * 100).toFixed(1)}%</div>
                                <div>Último treino: ${new Date(info.last_trained).toLocaleString()}</div>
                                <div>Amostras: ${info.training_samples}</div>
                            </div>
                        `).join('');

                        document.getElementById('ai-performance').innerHTML = modelsHtml;
                    }
                }
            } catch (error) {
                console.error('Erro ao carregar status da IA:', error);
                showAlert('Erro ao carregar status da IA', 'error');
            }
        }

        // Funções de ação
        async function trainModels() {
            if (!confirm('Tem certeza que deseja treinar os modelos de IA? Isso pode levar alguns minutos.')) {
                return;
            }

            try {
                showAlert('Iniciando treinamento dos modelos...', 'warning');

                const response = await fetch('/ai/train', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Modelos treinados com sucesso!', 'success');
                    refreshAIStatus();
                } else {
                    showAlert(data.error || 'Erro ao treinar modelos', 'error');
                }
            } catch (error) {
                console.error('Erro ao treinar modelos:', error);
                showAlert('Erro ao treinar modelos', 'error');
            }
        }

        async function testExchangeConnections() {
            try {
                showAlert('Testando conexões com exchanges...', 'warning');

                const response = await fetch('/admin/exchanges/test', {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    showAlert('Teste de conexões concluído!', 'success');
                    refreshExchangesStatus();
                } else {
                    showAlert(data.error || 'Erro no teste de conexões', 'error');
                }
            } catch (error) {
                console.error('Erro ao testar conexões:', error);
                showAlert('Erro ao testar conexões', 'error');
            }
        }

        async function blockSuspiciousIPs() {
            if (!confirm('Tem certeza que deseja bloquear IPs suspeitos automaticamente?')) {
                return;
            }

            try {
                const response = await fetch('/admin/security/block-ips', {
                    method: 'POST'
                });

                const data = await response.json();

                if (data.success) {
                    showAlert(`${data.blocked_count} IPs bloqueados`, 'success');
                    refreshSecurityEvents();
                } else {
                    showAlert(data.error || 'Erro ao bloquear IPs', 'error');
                }
            } catch (error) {
                console.error('Erro ao bloquear IPs:', error);
                showAlert('Erro ao bloquear IPs', 'error');
            }
        }

        // Limpar interval ao sair da página
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
