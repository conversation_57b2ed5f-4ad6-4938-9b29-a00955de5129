<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro - CryptoSignals</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">
    
    <style>
        /* Dark Mode Register Page */
        :root {
            --primary-gold: #FFD700;
            --dark-bg: #0a0a0a;
            --dark-surface: #1a1a1a;
            --text-light: #ffffff;
            --text-muted: #b0b0b0;
            --gradient-gold: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--gradient-dark);
            color: var(--text-light);
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 20px 0;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .register-container {
            background: var(--dark-surface);
            padding: 50px 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            width: 100%;
            max-width: 500px;
            position: relative;
            z-index: 2;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-container {
            margin-bottom: 25px;
        }
        
        .logo-container img {
            height: 50px;
            filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.3));
        }
        
        .register-title {
            font-size: 1.8rem;
            font-weight: 700;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .register-subtitle {
            color: var(--text-muted);
            font-size: 0.95rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            color: var(--text-light);
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            background: var(--dark-bg);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: var(--text-light);
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1);
        }
        
        .form-input::placeholder {
            color: var(--text-muted);
        }
        
        .form-select {
            width: 100%;
            padding: 12px 16px;
            background: var(--dark-bg);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: var(--text-light);
            font-size: 0.95rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .form-select:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.1);
        }
        
        .form-select option {
            background: var(--dark-bg);
            color: var(--text-light);
        }
        
        .plan-info {
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            font-size: 0.85rem;
            color: var(--text-muted);
        }
        
        .plan-highlight {
            color: var(--primary-gold);
            font-weight: 600;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: var(--gradient-gold);
            color: var(--dark-bg);
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .divider span {
            background: var(--dark-surface);
            padding: 0 20px;
            color: var(--text-muted);
            font-size: 0.9rem;
        }
        
        .login-link {
            text-align: center;
            color: var(--text-muted);
            font-size: 0.9rem;
        }
        
        .login-link a {
            color: var(--primary-gold);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .login-link a:hover {
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: var(--text-muted);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            z-index: 3;
        }
        
        .back-link:hover {
            color: var(--primary-gold);
        }
        
        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #FCA5A5;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }
        
        .success-message {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #6EE7B7;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            display: none;
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .loading .btn {
            position: relative;
        }
        
        .loading .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid var(--dark-bg);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .register-container {
                margin: 20px;
                padding: 40px 25px;
            }
            
            .register-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="/" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Voltar
    </a>
    
    <div class="register-container">
        <div class="register-header">
            <div class="logo-container">
                <img src="/logo.png" alt="CryptoSignals Logo">
            </div>
            <h1 class="register-title">Crie sua conta</h1>
            <p class="register-subtitle">Comece seu trial gratuito de 14 dias</p>
        </div>
        
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="email" class="form-label">E-mail</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input" 
                    placeholder="<EMAIL>"
                    required
                >
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Senha</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-input" 
                    placeholder="Mínimo 6 caracteres"
                    minlength="6"
                    required
                >
            </div>
            
            <div class="form-group">
                <label for="confirm_password" class="form-label">Confirmar Senha</label>
                <input 
                    type="password" 
                    id="confirm_password" 
                    name="confirm_password" 
                    class="form-input" 
                    placeholder="Digite a senha novamente"
                    required
                >
            </div>
            
            <div class="form-group">
                <label for="plan" class="form-label">Escolha seu plano</label>
                <select id="plan" name="plan" class="form-select" required>
                    <option value="">Selecione um plano</option>
                    <option value="free">Free - Grátis para sempre</option>
                    <option value="starter">Starter - $29/mês (14 dias grátis)</option>
                    <option value="professional" selected>Professional - $79/mês (14 dias grátis)</option>
                    <option value="enterprise">Enterprise - Sob consulta</option>
                </select>
                <div id="plan-info" class="plan-info" style="display: none;"></div>
            </div>
            
            <button type="submit" class="btn">
                Criar Conta Gratuita
            </button>
        </form>
        
        <div class="divider">
            <span>ou</span>
        </div>
        
        <div class="login-link">
            Já tem uma conta? 
            <a href="/login">Faça login</a>
        </div>
    </div>

    <script>
        const planInfo = {
            'free': {
                title: 'Plano Free',
                description: 'Acesso básico com <span class="plan-highlight">5 análises por dia</span> e recursos limitados.',
                price: 'Grátis para sempre'
            },
            'starter': {
                title: 'Plano Starter',
                description: '<span class="plan-highlight">50 análises por dia</span>, alertas básicos e suporte por email.',
                price: '$29/mês com 14 dias grátis'
            },
            'professional': {
                title: 'Plano Professional',
                description: '<span class="plan-highlight">Análises ilimitadas</span>, alertas premium, API access e suporte prioritário.',
                price: '$79/mês com 14 dias grátis'
            },
            'enterprise': {
                title: 'Plano Enterprise',
                description: 'Todos os recursos + <span class="plan-highlight">white-label</span>, integração customizada e suporte dedicado.',
                price: 'Preço sob consulta'
            }
        };
        
        document.getElementById('plan').addEventListener('change', function() {
            const selectedPlan = this.value;
            const infoDiv = document.getElementById('plan-info');
            
            if (selectedPlan && planInfo[selectedPlan]) {
                const info = planInfo[selectedPlan];
                infoDiv.innerHTML = `
                    <strong>${info.title}</strong><br>
                    ${info.description}<br>
                    <span class="plan-highlight">${info.price}</span>
                `;
                infoDiv.style.display = 'block';
            } else {
                infoDiv.style.display = 'none';
            }
        });
        
        // Trigger change event for pre-selected option
        document.getElementById('plan').dispatchEvent(new Event('change'));
        
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            
            // Validate password confirmation
            const password = formData.get('password');
            const confirmPassword = formData.get('confirm_password');
            
            if (password !== confirmPassword) {
                showMessage('As senhas não coincidem', 'error');
                return;
            }
            
            const data = {
                email: formData.get('email'),
                password: password,
                plan: formData.get('plan')
            };
            
            // Show loading state
            form.classList.add('loading');
            hideMessages();
            
            try {
                const response = await fetch('/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('Conta criada com sucesso! Redirecionando...', 'success');
                    setTimeout(() => {
                        window.location.href = result.redirect || '/dashboard';
                    }, 1500);
                } else {
                    showMessage(result.error || 'Erro no registro', 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                showMessage('Erro de conexão. Tente novamente.', 'error');
            } finally {
                form.classList.remove('loading');
            }
        });
        
        function showMessage(message, type) {
            hideMessages();
            const messageEl = document.getElementById(type + '-message');
            messageEl.textContent = message;
            messageEl.style.display = 'block';
        }
        
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }
        
        // Auto-focus no primeiro campo
        document.getElementById('email').focus();
        
        // Get plan from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const planParam = urlParams.get('plan');
        if (planParam && planInfo[planParam]) {
            document.getElementById('plan').value = planParam;
            document.getElementById('plan').dispatchEvent(new Event('change'));
        }
    </script>
</body>
</html>
