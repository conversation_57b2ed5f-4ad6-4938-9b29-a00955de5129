"""
Sistema de Afiliados - CryptoSignals
Sistema completo de afiliados com comissões e tracking
"""

import sqlite3
import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json
from decimal import Decimal

class AffiliateStatus(Enum):
    """Status do afiliado"""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"

class CommissionStatus(Enum):
    """Status da comissão"""
    PENDING = "pending"
    APPROVED = "approved"
    PAID = "paid"
    CANCELLED = "cancelled"

class PayoutMethod(Enum):
    """Métodos de pagamento"""
    USDT = "usdt"
    BITCOIN = "bitcoin"
    ETHEREUM = "ethereum"
    BANK_TRANSFER = "bank_transfer"
    PAYPAL = "paypal"

@dataclass
class AffiliateProfile:
    """Perfil do afiliado"""
    id: str
    user_id: str
    affiliate_code: str
    status: AffiliateStatus
    commission_rate: float  # Porcentagem (ex: 20.0 para 20%)
    total_referrals: int
    total_earnings: Decimal
    pending_earnings: Decimal
    paid_earnings: Decimal
    created_at: datetime
    approved_at: Optional[datetime] = None
    payout_method: PayoutMethod = PayoutMethod.USDT
    payout_address: Optional[str] = None
    notes: Optional[str] = None

@dataclass
class Referral:
    """Referência de afiliado"""
    id: str
    affiliate_id: str
    referred_user_id: str
    referral_code: str
    ip_address: str
    user_agent: str
    created_at: datetime
    converted_at: Optional[datetime] = None
    first_payment_at: Optional[datetime] = None

@dataclass
class Commission:
    """Comissão de afiliado"""
    id: str
    affiliate_id: str
    referral_id: str
    payment_id: str
    amount: Decimal
    commission_rate: float
    status: CommissionStatus
    created_at: datetime
    approved_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    notes: Optional[str] = None

@dataclass
class Payout:
    """Pagamento para afiliado"""
    id: str
    affiliate_id: str
    amount: Decimal
    method: PayoutMethod
    address: str
    transaction_hash: Optional[str]
    status: str
    created_at: datetime
    processed_at: Optional[datetime] = None
    commission_ids: List[str] = None
    
    def __post_init__(self):
        if self.commission_ids is None:
            self.commission_ids = []

class AffiliateSystem:
    """Sistema principal de afiliados"""
    
    def __init__(self, db_path: str = "affiliates.db"):
        self.db_path = db_path
        self.default_commission_rate = 20.0  # 20%
        self.min_payout_amount = Decimal('50.00')  # Mínimo $50 para saque
        self.cookie_duration_days = 30  # Cookie de referência válido por 30 dias
        
        # Inicializar banco
        self._init_database()
    
    def _init_database(self):
        """Inicializa banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabela de afiliados
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS affiliates (
                id TEXT PRIMARY KEY,
                user_id TEXT UNIQUE NOT NULL,
                affiliate_code TEXT UNIQUE NOT NULL,
                status TEXT NOT NULL,
                commission_rate REAL NOT NULL,
                total_referrals INTEGER DEFAULT 0,
                total_earnings DECIMAL(10,2) DEFAULT 0.00,
                pending_earnings DECIMAL(10,2) DEFAULT 0.00,
                paid_earnings DECIMAL(10,2) DEFAULT 0.00,
                created_at TIMESTAMP NOT NULL,
                approved_at TIMESTAMP,
                payout_method TEXT DEFAULT 'usdt',
                payout_address TEXT,
                notes TEXT
            )
        ''')
        
        # Tabela de referências
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS referrals (
                id TEXT PRIMARY KEY,
                affiliate_id TEXT NOT NULL,
                referred_user_id TEXT NOT NULL,
                referral_code TEXT NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP NOT NULL,
                converted_at TIMESTAMP,
                first_payment_at TIMESTAMP,
                FOREIGN KEY (affiliate_id) REFERENCES affiliates (id)
            )
        ''')
        
        # Tabela de comissões
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS commissions (
                id TEXT PRIMARY KEY,
                affiliate_id TEXT NOT NULL,
                referral_id TEXT NOT NULL,
                payment_id TEXT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                commission_rate REAL NOT NULL,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                approved_at TIMESTAMP,
                paid_at TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (affiliate_id) REFERENCES affiliates (id),
                FOREIGN KEY (referral_id) REFERENCES referrals (id)
            )
        ''')
        
        # Tabela de pagamentos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payouts (
                id TEXT PRIMARY KEY,
                affiliate_id TEXT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                method TEXT NOT NULL,
                address TEXT NOT NULL,
                transaction_hash TEXT,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                processed_at TIMESTAMP,
                commission_ids TEXT,
                FOREIGN KEY (affiliate_id) REFERENCES affiliates (id)
            )
        ''')
        
        # Índices
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_affiliates_user_id ON affiliates(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_affiliates_code ON affiliates(affiliate_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_referrals_affiliate ON referrals(affiliate_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_referrals_user ON referrals(referred_user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_commissions_affiliate ON commissions(affiliate_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_commissions_status ON commissions(status)')
        
        conn.commit()
        conn.close()
    
    def create_affiliate(self, user_id: str, commission_rate: Optional[float] = None) -> AffiliateProfile:
        """Cria novo afiliado"""
        affiliate_id = str(uuid.uuid4())
        affiliate_code = self._generate_affiliate_code(user_id)
        
        affiliate = AffiliateProfile(
            id=affiliate_id,
            user_id=user_id,
            affiliate_code=affiliate_code,
            status=AffiliateStatus.PENDING,
            commission_rate=commission_rate or self.default_commission_rate,
            total_referrals=0,
            total_earnings=Decimal('0.00'),
            pending_earnings=Decimal('0.00'),
            paid_earnings=Decimal('0.00'),
            created_at=datetime.now()
        )
        
        # Salvar no banco
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO affiliates 
            (id, user_id, affiliate_code, status, commission_rate, total_referrals,
             total_earnings, pending_earnings, paid_earnings, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            affiliate.id, affiliate.user_id, affiliate.affiliate_code,
            affiliate.status.value, affiliate.commission_rate, affiliate.total_referrals,
            float(affiliate.total_earnings), float(affiliate.pending_earnings),
            float(affiliate.paid_earnings), affiliate.created_at
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Afiliado criado: {affiliate_code}")
        return affiliate
    
    def _generate_affiliate_code(self, user_id: str) -> str:
        """Gera código único de afiliado"""
        # Usar hash do user_id + timestamp para garantir unicidade
        data = f"{user_id}_{datetime.now().timestamp()}"
        hash_obj = hashlib.md5(data.encode())
        return hash_obj.hexdigest()[:8].upper()
    
    def get_affiliate_by_user(self, user_id: str) -> Optional[AffiliateProfile]:
        """Obtém afiliado por user_id"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM affiliates WHERE user_id = ?', (user_id,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return self._row_to_affiliate(row)
        return None
    
    def get_affiliate_by_code(self, affiliate_code: str) -> Optional[AffiliateProfile]:
        """Obtém afiliado por código"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM affiliates WHERE affiliate_code = ?', (affiliate_code,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return self._row_to_affiliate(row)
        return None
    
    def _row_to_affiliate(self, row) -> AffiliateProfile:
        """Converte row do banco para AffiliateProfile"""
        return AffiliateProfile(
            id=row[0],
            user_id=row[1],
            affiliate_code=row[2],
            status=AffiliateStatus(row[3]),
            commission_rate=row[4],
            total_referrals=row[5],
            total_earnings=Decimal(str(row[6])),
            pending_earnings=Decimal(str(row[7])),
            paid_earnings=Decimal(str(row[8])),
            created_at=datetime.fromisoformat(row[9]),
            approved_at=datetime.fromisoformat(row[10]) if row[10] else None,
            payout_method=PayoutMethod(row[11]) if row[11] else PayoutMethod.USDT,
            payout_address=row[12],
            notes=row[13]
        )
    
    def track_referral(self, affiliate_code: str, referred_user_id: str, 
                      ip_address: str = "unknown", user_agent: str = "unknown") -> Optional[Referral]:
        """Registra nova referência"""
        affiliate = self.get_affiliate_by_code(affiliate_code)
        if not affiliate or affiliate.status != AffiliateStatus.ACTIVE:
            return None
        
        # Verificar se usuário já foi referenciado
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id FROM referrals WHERE referred_user_id = ?', (referred_user_id,))
        if cursor.fetchone():
            conn.close()
            return None  # Usuário já foi referenciado
        
        # Criar nova referência
        referral = Referral(
            id=str(uuid.uuid4()),
            affiliate_id=affiliate.id,
            referred_user_id=referred_user_id,
            referral_code=affiliate_code,
            ip_address=ip_address,
            user_agent=user_agent,
            created_at=datetime.now()
        )
        
        cursor.execute('''
            INSERT INTO referrals 
            (id, affiliate_id, referred_user_id, referral_code, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            referral.id, referral.affiliate_id, referral.referred_user_id,
            referral.referral_code, referral.ip_address, referral.user_agent,
            referral.created_at
        ))
        
        # Atualizar contador de referências
        cursor.execute('''
            UPDATE affiliates SET total_referrals = total_referrals + 1 
            WHERE id = ?
        ''', (affiliate.id,))
        
        conn.commit()
        conn.close()
        
        print(f"📈 Nova referência registrada: {affiliate_code} → {referred_user_id}")
        return referral
    
    def process_commission(self, referred_user_id: str, payment_amount: Decimal, payment_id: str) -> Optional[Commission]:
        """Processa comissão quando usuário referenciado faz pagamento"""
        # Encontrar referência
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT r.*, a.commission_rate 
            FROM referrals r 
            JOIN affiliates a ON r.affiliate_id = a.id 
            WHERE r.referred_user_id = ? AND a.status = 'active'
        ''', (referred_user_id,))
        
        row = cursor.fetchone()
        if not row:
            conn.close()
            return None
        
        referral_id = row[0]
        affiliate_id = row[1]
        commission_rate = row[-1]
        
        # Calcular comissão
        commission_amount = payment_amount * Decimal(commission_rate / 100)
        
        # Criar comissão
        commission = Commission(
            id=str(uuid.uuid4()),
            affiliate_id=affiliate_id,
            referral_id=referral_id,
            payment_id=payment_id,
            amount=commission_amount,
            commission_rate=commission_rate,
            status=CommissionStatus.PENDING,
            created_at=datetime.now()
        )
        
        cursor.execute('''
            INSERT INTO commissions 
            (id, affiliate_id, referral_id, payment_id, amount, commission_rate, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            commission.id, commission.affiliate_id, commission.referral_id,
            commission.payment_id, float(commission.amount), commission.commission_rate,
            commission.status.value, commission.created_at
        ))
        
        # Atualizar earnings do afiliado
        cursor.execute('''
            UPDATE affiliates 
            SET pending_earnings = pending_earnings + ?, 
                total_earnings = total_earnings + ?
            WHERE id = ?
        ''', (float(commission_amount), float(commission_amount), affiliate_id))
        
        # Marcar primeira conversão se necessário
        cursor.execute('''
            UPDATE referrals 
            SET converted_at = COALESCE(converted_at, ?),
                first_payment_at = COALESCE(first_payment_at, ?)
            WHERE id = ?
        ''', (datetime.now(), datetime.now(), referral_id))
        
        conn.commit()
        conn.close()
        
        print(f"💰 Comissão processada: ${commission_amount} para afiliado {affiliate_id}")
        return commission
    
    def approve_commission(self, commission_id: str) -> bool:
        """Aprova comissão"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE commissions 
            SET status = 'approved', approved_at = ?
            WHERE id = ? AND status = 'pending'
        ''', (datetime.now(), commission_id))
        
        success = cursor.rowcount > 0
        conn.commit()
        conn.close()
        
        return success
    
    def request_payout(self, affiliate_id: str, amount: Decimal, method: PayoutMethod, address: str) -> Optional[Payout]:
        """Solicita pagamento"""
        affiliate = self.get_affiliate_by_id(affiliate_id)
        if not affiliate or affiliate.pending_earnings < amount or amount < self.min_payout_amount:
            return None
        
        payout = Payout(
            id=str(uuid.uuid4()),
            affiliate_id=affiliate_id,
            amount=amount,
            method=method,
            address=address,
            status="pending",
            created_at=datetime.now()
        )
        
        # Salvar no banco
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO payouts 
            (id, affiliate_id, amount, method, address, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            payout.id, payout.affiliate_id, float(payout.amount),
            payout.method.value, payout.address, payout.status, payout.created_at
        ))
        
        conn.commit()
        conn.close()
        
        return payout
    
    def get_affiliate_by_id(self, affiliate_id: str) -> Optional[AffiliateProfile]:
        """Obtém afiliado por ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM affiliates WHERE id = ?', (affiliate_id,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return self._row_to_affiliate(row)
        return None
    
    def get_affiliate_stats(self, affiliate_id: str) -> Dict[str, Any]:
        """Obtém estatísticas do afiliado"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Estatísticas básicas
        cursor.execute('''
            SELECT 
                COUNT(*) as total_referrals,
                COUNT(CASE WHEN converted_at IS NOT NULL THEN 1 END) as converted_referrals,
                COUNT(CASE WHEN first_payment_at IS NOT NULL THEN 1 END) as paying_referrals
            FROM referrals 
            WHERE affiliate_id = ?
        ''', (affiliate_id,))
        
        referral_stats = cursor.fetchone()
        
        # Comissões
        cursor.execute('''
            SELECT 
                COUNT(*) as total_commissions,
                SUM(amount) as total_amount,
                SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
                SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount,
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as paid_amount
            FROM commissions 
            WHERE affiliate_id = ?
        ''', (affiliate_id,))
        
        commission_stats = cursor.fetchone()
        
        # Referências recentes
        cursor.execute('''
            SELECT created_at, converted_at, first_payment_at
            FROM referrals 
            WHERE affiliate_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ''', (affiliate_id,))
        
        recent_referrals = cursor.fetchall()
        
        conn.close()
        
        # Calcular taxa de conversão
        conversion_rate = 0
        if referral_stats[0] > 0:
            conversion_rate = (referral_stats[1] / referral_stats[0]) * 100
        
        return {
            'total_referrals': referral_stats[0],
            'converted_referrals': referral_stats[1],
            'paying_referrals': referral_stats[2],
            'conversion_rate': round(conversion_rate, 2),
            'total_commissions': commission_stats[0] or 0,
            'total_commission_amount': float(commission_stats[1] or 0),
            'pending_amount': float(commission_stats[2] or 0),
            'approved_amount': float(commission_stats[3] or 0),
            'paid_amount': float(commission_stats[4] or 0),
            'recent_referrals': [
                {
                    'created_at': r[0],
                    'converted_at': r[1],
                    'first_payment_at': r[2]
                }
                for r in recent_referrals
            ]
        }
    
    def get_platform_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas da plataforma"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Estatísticas gerais
        cursor.execute('''
            SELECT 
                COUNT(*) as total_affiliates,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_affiliates,
                SUM(total_referrals) as total_referrals,
                SUM(total_earnings) as total_earnings,
                SUM(pending_earnings) as pending_earnings,
                SUM(paid_earnings) as paid_earnings
            FROM affiliates
        ''')
        
        platform_stats = cursor.fetchone()
        
        # Top afiliados
        cursor.execute('''
            SELECT affiliate_code, total_referrals, total_earnings
            FROM affiliates
            WHERE status = 'active'
            ORDER BY total_earnings DESC
            LIMIT 10
        ''')
        
        top_affiliates = cursor.fetchall()
        
        conn.close()
        
        return {
            'total_affiliates': platform_stats[0] or 0,
            'active_affiliates': platform_stats[1] or 0,
            'total_referrals': platform_stats[2] or 0,
            'total_earnings': float(platform_stats[3] or 0),
            'pending_earnings': float(platform_stats[4] or 0),
            'paid_earnings': float(platform_stats[5] or 0),
            'top_affiliates': [
                {
                    'code': row[0],
                    'referrals': row[1],
                    'earnings': float(row[2])
                }
                for row in top_affiliates
            ]
        }

# Instância global do sistema de afiliados
affiliate_system = AffiliateSystem()
