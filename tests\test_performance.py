"""
Testes de Performance - CryptoSignals
Validação de performance, carga e stress testing
"""

import unittest
import sys
import os
import time
import threading
import tempfile
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
import statistics

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from saas_auth import SaaSAuthManager, PlanType
from saas_payments import TetherPaymentProcessor

class TestPerformanceAuth(unittest.TestCase):
    """Testes de performance para sistema de autenticação"""

    def setUp(self):
        """Configuração inicial para testes de performance"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.auth_manager = SaaSAuthManager(db_path=self.test_db.name)

        # Métricas de performance esperadas
        self.max_response_time = 200  # ms
        self.max_concurrent_users = 100
        self.test_duration = 30  # segundos

    def tearDown(self):
        """Limpeza após testes de performance"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)

    def test_user_creation_performance(self):
        """Testa performance de criação de usuários"""
        start_time = time.time()

        # Criar 100 usuários
        for i in range(100):
            user = self.auth_manager.create_user(
                email=f'perf_test_{i}@cryptosignals.com',
                password='TestPassword123!',
                plan=PlanType.FREE
            )
            self.assertIsNotNone(user)

        end_time = time.time()
        total_time = (end_time - start_time) * 1000  # em ms
        avg_time_per_user = total_time / 100

        print(f"\n📊 Performance - Criação de Usuários:")
        print(f"  • Total: {total_time:.2f}ms para 100 usuários")
        print(f"  • Média: {avg_time_per_user:.2f}ms por usuário")

        # Verificar se está dentro do limite aceitável
        self.assertLess(avg_time_per_user, self.max_response_time,
                       f"Criação de usuário muito lenta: {avg_time_per_user:.2f}ms")

    def test_authentication_performance(self):
        """Testa performance de autenticação"""
        # Criar usuário de teste
        test_email = '<EMAIL>'
        test_password = 'TestPassword123!'

        user = self.auth_manager.create_user(
            email=test_email,
            password=test_password,
            plan=PlanType.FREE
        )
        self.assertIsNotNone(user)

        # Testar 1000 autenticações
        response_times = []

        for i in range(1000):
            start_time = time.time()

            authenticated_user = self.auth_manager.authenticate_user(
                email=test_email,
                password=test_password
            )

            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # em ms
            response_times.append(response_time)

            self.assertIsNotNone(authenticated_user)

        # Calcular estatísticas
        avg_time = statistics.mean(response_times)
        median_time = statistics.median(response_times)
        max_time = max(response_times)
        min_time = min(response_times)

        print(f"\n📊 Performance - Autenticação (1000 requests):")
        print(f"  • Média: {avg_time:.2f}ms")
        print(f"  • Mediana: {median_time:.2f}ms")
        print(f"  • Máximo: {max_time:.2f}ms")
        print(f"  • Mínimo: {min_time:.2f}ms")

        # Verificar se está dentro dos limites
        self.assertLess(avg_time, self.max_response_time,
                       f"Autenticação muito lenta: {avg_time:.2f}ms")
        self.assertLess(max_time, self.max_response_time * 2,
                       f"Pico de latência muito alto: {max_time:.2f}ms")

    def test_concurrent_authentication(self):
        """Testa autenticação concorrente"""
        # Criar usuários de teste
        test_users = []
        for i in range(50):
            email = f'concurrent_test_{i}@cryptosignals.com'
            password = 'TestPassword123!'
            user = self.auth_manager.create_user(email=email, password=password, plan=PlanType.FREE)
            test_users.append((email, password))

        def authenticate_user(user_data):
            """Função para autenticar um usuário"""
            email, password = user_data
            start_time = time.time()

            result = self.auth_manager.authenticate_user(email=email, password=password)

            end_time = time.time()
            response_time = (end_time - start_time) * 1000

            return {
                'success': result is not None,
                'response_time': response_time,
                'email': email
            }

        # Executar autenticações concorrentes
        start_time = time.time()
        results = []

        with ThreadPoolExecutor(max_workers=10) as executor:
            # Submeter todas as tarefas
            futures = [executor.submit(authenticate_user, user_data) for user_data in test_users]

            # Coletar resultados
            for future in as_completed(futures):
                results.append(future.result())

        end_time = time.time()
        total_time = (end_time - start_time) * 1000

        # Analisar resultados
        successful_auths = [r for r in results if r['success']]
        response_times = [r['response_time'] for r in results]

        success_rate = len(successful_auths) / len(results) * 100
        avg_response_time = statistics.mean(response_times)

        print(f"\n📊 Performance - Autenticação Concorrente:")
        print(f"  • Total de usuários: {len(test_users)}")
        print(f"  • Taxa de sucesso: {success_rate:.1f}%")
        print(f"  • Tempo total: {total_time:.2f}ms")
        print(f"  • Tempo médio por auth: {avg_response_time:.2f}ms")

        # Verificações
        self.assertGreaterEqual(success_rate, 95.0, "Taxa de sucesso muito baixa")
        self.assertLess(avg_response_time, self.max_response_time * 1.5,
                       "Tempo de resposta concorrente muito alto")

    def test_jwt_token_performance(self):
        """Testa performance de geração e validação de tokens JWT"""
        # Criar usuário de teste
        user = self.auth_manager.create_user(
            email='<EMAIL>',
            password='TestPassword123!',
            plan=PlanType.FREE
        )

        # Testar geração de tokens
        generation_times = []
        validation_times = []
        tokens = []

        for i in range(1000):
            # Geração
            start_time = time.time()
            token = self.auth_manager.generate_jwt_token(user)
            end_time = time.time()
            generation_times.append((end_time - start_time) * 1000)
            tokens.append(token)

            # Validação
            start_time = time.time()
            payload = self.auth_manager.verify_jwt_token(token)
            end_time = time.time()
            validation_times.append((end_time - start_time) * 1000)

            self.assertIsNotNone(payload)

        # Estatísticas
        avg_generation = statistics.mean(generation_times)
        avg_validation = statistics.mean(validation_times)

        print(f"\n📊 Performance - JWT Tokens (1000 operações):")
        print(f"  • Geração média: {avg_generation:.2f}ms")
        print(f"  • Validação média: {avg_validation:.2f}ms")

        # Verificações
        self.assertLess(avg_generation, 50, "Geração de JWT muito lenta")
        self.assertLess(avg_validation, 30, "Validação de JWT muito lenta")

class TestPerformancePayments(unittest.TestCase):
    """Testes de performance para sistema de pagamentos"""

    def setUp(self):
        """Configuração inicial"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()

        # Inicializar tabelas
        self._init_test_database()

        self.test_wallet = "******************************************"
        self.processor = TetherPaymentProcessor(self.test_wallet, db_path=self.test_db.name)

    def _init_test_database(self):
        """Inicializa banco de dados de teste"""
        import sqlite3
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                plan TEXT,
                amount REAL,
                currency TEXT,
                tx_hash TEXT,
                status TEXT,
                created_at TEXT,
                confirmed_at TEXT
            )
        """)

        conn.commit()
        conn.close()

    def tearDown(self):
        """Limpeza"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)

    def test_payment_creation_performance(self):
        """Testa performance de criação de pagamentos"""
        response_times = []

        for i in range(500):
            start_time = time.time()

            payment = self.processor.create_payment_request(
                user_id=f'perf_user_{i}',
                plan='starter',
                amount=29.0,
                annual=False
            )

            end_time = time.time()
            response_times.append((end_time - start_time) * 1000)

            self.assertIsNotNone(payment)

        avg_time = statistics.mean(response_times)
        max_time = max(response_times)

        print(f"\n📊 Performance - Criação de Pagamentos (500 requests):")
        print(f"  • Tempo médio: {avg_time:.2f}ms")
        print(f"  • Tempo máximo: {max_time:.2f}ms")

        self.assertLess(avg_time, 100, "Criação de pagamento muito lenta")

    def test_payment_retrieval_performance(self):
        """Testa performance de recuperação de pagamentos"""
        # Criar pagamentos de teste
        payment_ids = []
        for i in range(100):
            payment = self.processor.create_payment_request(
                user_id=f'retrieval_user_{i}',
                plan='professional',
                amount=79.0
            )
            payment_ids.append(payment.id)

        # Testar recuperação
        response_times = []

        for payment_id in payment_ids:
            start_time = time.time()

            payment = self.processor.get_payment(payment_id)

            end_time = time.time()
            response_times.append((end_time - start_time) * 1000)

            self.assertIsNotNone(payment)

        avg_time = statistics.mean(response_times)

        print(f"\n📊 Performance - Recuperação de Pagamentos (100 requests):")
        print(f"  • Tempo médio: {avg_time:.2f}ms")

        self.assertLess(avg_time, 50, "Recuperação de pagamento muito lenta")

class TestStressTest(unittest.TestCase):
    """Testes de stress para o sistema"""

    def setUp(self):
        """Configuração para testes de stress"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.auth_manager = SaaSAuthManager(db_path=self.test_db.name)

    def tearDown(self):
        """Limpeza"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)

    def test_high_load_authentication(self):
        """Testa o sistema sob alta carga"""
        # Criar usuário de teste
        test_email = '<EMAIL>'
        test_password = 'StressTest123!'

        user = self.auth_manager.create_user(
            email=test_email,
            password=test_password,
            plan=PlanType.FREE
        )
        self.assertIsNotNone(user)

        def stress_authenticate():
            """Função para stress test com menos concorrência"""
            results = []
            for _ in range(50):  # Reduzido de 100 para 50
                start_time = time.time()
                try:
                    result = self.auth_manager.authenticate_user(test_email, test_password)
                    success = result is not None
                except Exception as e:
                    print(f"Erro na autenticação: {e}")
                    success = False
                    result = None

                end_time = time.time()

                results.append({
                    'success': success,
                    'time': (end_time - start_time) * 1000
                })

                # Pequena pausa para reduzir contenção
                time.sleep(0.001)
            return results

        # Executar stress test com menos workers
        start_time = time.time()
        all_results = []

        with ThreadPoolExecutor(max_workers=5) as executor:  # Reduzido de 20 para 5
            futures = [executor.submit(stress_authenticate) for _ in range(5)]  # Reduzido de 10 para 5

            for future in as_completed(futures):
                all_results.extend(future.result())

        end_time = time.time()
        total_time = end_time - start_time

        # Analisar resultados
        successful_requests = [r for r in all_results if r['success']]
        success_rate = len(successful_requests) / len(all_results) * 100
        avg_response_time = statistics.mean([r['time'] for r in all_results])
        requests_per_second = len(all_results) / total_time

        print(f"\n📊 Stress Test - Alta Carga:")
        print(f"  • Total de requests: {len(all_results)}")
        print(f"  • Taxa de sucesso: {success_rate:.1f}%")
        print(f"  • Tempo médio: {avg_response_time:.2f}ms")
        print(f"  • Requests/segundo: {requests_per_second:.1f}")
        print(f"  • Duração total: {total_time:.2f}s")

        # Verificações de stress (ajustadas para serem realistas)
        self.assertGreaterEqual(success_rate, 90.0, "Sistema falhou sob stress")
        self.assertLess(avg_response_time, 1000, "Tempo de resposta degradou muito")
        self.assertGreater(requests_per_second, 5, "Throughput muito baixo")

if __name__ == '__main__':
    # Configurar suite de testes de performance
    unittest.main(verbosity=2)
