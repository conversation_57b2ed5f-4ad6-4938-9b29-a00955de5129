<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Métodos de Pagamento - CryptoSignals</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0B1B33 0%, #1a2332 100%);
            color: #F8F7F2;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #D4A017;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            color: #E0E0E0;
        }

        .payment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .payment-category {
            background: rgba(248, 247, 242, 0.05);
            border: 1px solid rgba(212, 160, 23, 0.2);
            border-radius: 16px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #D4A017;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .payment-method {
            background: rgba(248, 247, 242, 0.03);
            border: 1px solid rgba(224, 224, 224, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .payment-method:hover {
            border-color: #D4A017;
            background: rgba(212, 160, 23, 0.1);
            transform: translateY(-2px);
        }

        .method-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .method-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #F8F7F2;
        }

        .method-network {
            font-size: 0.9rem;
            color: #D4A017;
            background: rgba(212, 160, 23, 0.2);
            padding: 4px 8px;
            border-radius: 6px;
        }

        .method-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 0.9rem;
            color: #E0E0E0;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .detail-label {
            color: #E0E0E0;
        }

        .detail-value {
            color: #F8F7F2;
            font-weight: 500;
        }

        .fees-low { color: #4CAF50; }
        .fees-medium { color: #FF9800; }
        .fees-high { color: #F44336; }

        .select-button {
            background: linear-gradient(135deg, #D4A017 0%, #B8941C 100%);
            color: #0B1B33;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
            width: 100%;
        }

        .select-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 160, 23, 0.3);
        }

        .network-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: 10px;
        }

        .ethereum { background: rgba(98, 126, 234, 0.2); color: #627EEA; }
        .bsc { background: rgba(243, 186, 47, 0.2); color: #F3BA2F; }
        .polygon { background: rgba(130, 71, 229, 0.2); color: #8247E5; }
        .traditional { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }

        .info-section {
            background: rgba(248, 247, 242, 0.05);
            border: 1px solid rgba(212, 160, 23, 0.2);
            border-radius: 16px;
            padding: 30px;
            margin-top: 40px;
        }

        .info-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #D4A017;
            margin-bottom: 15px;
        }

        .info-text {
            color: #E0E0E0;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .payment-grid {
                grid-template-columns: 1fr;
            }
            
            .method-details {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 Métodos de Pagamento</h1>
            <p>Escolha a forma de pagamento que preferir - Suportamos múltiplas redes blockchain e métodos tradicionais</p>
        </div>

        <div class="payment-grid">
            <!-- Criptomoedas -->
            <div class="payment-category">
                <div class="category-title">
                    🚀 Criptomoedas
                </div>
                
                <!-- USDT Ethereum -->
                <div class="payment-method" onclick="selectPayment('usdt_eth')">
                    <div class="method-header">
                        <span class="method-name">USDT</span>
                        <span class="network-badge ethereum">Ethereum</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-medium">Baixa-Média</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">1-5 min</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar USDT (ETH)</button>
                </div>

                <!-- USDT BSC -->
                <div class="payment-method" onclick="selectPayment('usdt_bsc')">
                    <div class="method-header">
                        <span class="method-name">USDT (BEP20)</span>
                        <span class="network-badge bsc">Binance Smart Chain</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-low">Muito Baixa</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">1-3 min</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar USDT (BSC)</button>
                </div>

                <!-- USDT Polygon -->
                <div class="payment-method" onclick="selectPayment('usdt_polygon')">
                    <div class="method-header">
                        <span class="method-name">USDT</span>
                        <span class="network-badge polygon">Polygon</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-low">Muito Baixa</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">1-2 min</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar USDT (Polygon)</button>
                </div>

                <!-- BNB -->
                <div class="payment-method" onclick="selectPayment('bnb_bsc')">
                    <div class="method-header">
                        <span class="method-name">BNB</span>
                        <span class="network-badge bsc">Binance Smart Chain</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-low">Muito Baixa</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">1-3 min</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar BNB</button>
                </div>

                <!-- MATIC -->
                <div class="payment-method" onclick="selectPayment('matic_polygon')">
                    <div class="method-header">
                        <span class="method-name">MATIC</span>
                        <span class="network-badge polygon">Polygon</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-low">Muito Baixa</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">1-2 min</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar MATIC</button>
                </div>
            </div>

            <!-- Métodos Tradicionais -->
            <div class="payment-category">
                <div class="category-title">
                    💳 Métodos Tradicionais
                </div>
                
                <!-- Cartão de Crédito -->
                <div class="payment-method" onclick="selectPayment('credit_card')">
                    <div class="method-header">
                        <span class="method-name">Cartão de Crédito</span>
                        <span class="network-badge traditional">Tradicional</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-medium">3-5%</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">Instantâneo</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar Cartão</button>
                </div>

                <!-- PayPal -->
                <div class="payment-method" onclick="selectPayment('paypal')">
                    <div class="method-header">
                        <span class="method-name">PayPal</span>
                        <span class="network-badge traditional">Tradicional</span>
                    </div>
                    <div class="method-details">
                        <div class="detail-item">
                            <span class="detail-label">Taxa:</span>
                            <span class="detail-value fees-medium">2.9% + $0.30</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Confirmação:</span>
                            <span class="detail-value">Instantâneo</span>
                        </div>
                    </div>
                    <button class="select-button">Selecionar PayPal</button>
                </div>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">ℹ️ Informações Importantes</div>
            <p class="info-text">
                <strong>Redes Suportadas:</strong> Oferecemos suporte completo para Ethereum, Binance Smart Chain (BEP20) e Polygon para máxima flexibilidade.
            </p>
            <p class="info-text">
                <strong>Segurança:</strong> Todos os pagamentos são verificados automaticamente na blockchain e processados com segurança máxima.
            </p>
            <p class="info-text">
                <strong>Taxas:</strong> As taxas variam por rede. BSC e Polygon oferecem as menores taxas, enquanto Ethereum pode ter taxas mais altas durante períodos de alta demanda.
            </p>
        </div>
    </div>

    <script>
        function selectPayment(method) {
            // Aqui você pode implementar a lógica para processar a seleção
            console.log('Método selecionado:', method);
            
            // Exemplo de como enviar para o backend
            fetch('/payment/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    payment_method: method,
                    plan: 'starter', // ou obter do contexto
                    annual: false
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirecionar para página de pagamento ou mostrar QR code
                    console.log('Pagamento criado:', data.payment);
                } else {
                    alert('Erro ao criar pagamento: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao processar pagamento');
            });
        }
    </script>
</body>
</html>
