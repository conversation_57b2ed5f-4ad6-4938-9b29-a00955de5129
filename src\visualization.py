"""
Módulo para visualização de dados do Bitcoin.
Cria gráficos interativos usando Plotly.
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta


class BitcoinVisualizer:
    """Classe para criação de visualizações do Bitcoin."""
    
    def __init__(self, df: pd.DataFrame):
        """
        Inicializa o visualizador.
        
        Args:
            df: DataFrame com dados do Bitcoin
        """
        self.df = df.copy()
        self.colors = {
            'primary': '#F7931A',  # Bitcoin Orange
            'secondary': '#4D4D4D',
            'success': '#28A745',
            'danger': '#DC3545',
            'warning': '#FFC107',
            'info': '#17A2B8'
        }
    
    def create_candlestick_chart(self, 
                               indicators: Optional[Dict] = None,
                               height: int = 600) -> go.Figure:
        """
        Cria gráfico de candlestick com indicadores opcionais.
        
        Args:
            indicators: Dicionário com indicadores técnicos
            height: Altura do gráfico
            
        Returns:
            Figura Plotly
        """
        fig = go.Figure()
        
        # Candlestick principal
        fig.add_trace(go.Candlestick(
            x=self.df.index,
            open=self.df['Open'],
            high=self.df['High'],
            low=self.df['Low'],
            close=self.df['Close'],
            name='BTC/USDT',
            increasing_line_color=self.colors['success'],
            decreasing_line_color=self.colors['danger']
        ))
        
        # Adicionar indicadores se fornecidos
        if indicators:
            # Médias móveis
            for key, values in indicators.items():
                if 'SMA' in key or 'EMA' in key:
                    fig.add_trace(go.Scatter(
                        x=self.df.index,
                        y=values,
                        mode='lines',
                        name=key,
                        line=dict(width=2),
                        opacity=0.7
                    ))
                
                # Bandas de Bollinger
                elif 'BB_Upper' in key:
                    fig.add_trace(go.Scatter(
                        x=self.df.index,
                        y=values,
                        mode='lines',
                        name='BB Superior',
                        line=dict(color='rgba(128,128,128,0.5)', width=1),
                        showlegend=False
                    ))
                elif 'BB_Lower' in key:
                    fig.add_trace(go.Scatter(
                        x=self.df.index,
                        y=values,
                        mode='lines',
                        name='BB Inferior',
                        line=dict(color='rgba(128,128,128,0.5)', width=1),
                        fill='tonexty',
                        fillcolor='rgba(128,128,128,0.1)',
                        showlegend=False
                    ))
        
        fig.update_layout(
            title='Bitcoin (BTC/USDT) - Análise de Preços',
            xaxis_title='Data',
            yaxis_title='Preço (USDT)',
            height=height,
            template='plotly_white',
            xaxis_rangeslider_visible=False,
            font=dict(size=12)
        )
        
        return fig
    
    def create_volume_chart(self, height: int = 300) -> go.Figure:
        """
        Cria gráfico de volume.
        
        Args:
            height: Altura do gráfico
            
        Returns:
            Figura Plotly
        """
        # Determinar cores baseadas na direção do preço
        colors = []
        for i in range(len(self.df)):
            if i == 0:
                colors.append(self.colors['secondary'])
            else:
                if self.df['Close'].iloc[i] >= self.df['Close'].iloc[i-1]:
                    colors.append(self.colors['success'])
                else:
                    colors.append(self.colors['danger'])
        
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=self.df.index,
            y=self.df['Volume'],
            name='Volume',
            marker_color=colors,
            opacity=0.7
        ))
        
        # Adicionar média móvel do volume se disponível
        if 'Volume_SMA_20' in self.df.columns:
            fig.add_trace(go.Scatter(
                x=self.df.index,
                y=self.df['Volume_SMA_20'],
                mode='lines',
                name='Volume MA(20)',
                line=dict(color=self.colors['primary'], width=2)
            ))
        
        fig.update_layout(
            title='Volume de Negociação',
            xaxis_title='Data',
            yaxis_title='Volume',
            height=height,
            template='plotly_white',
            font=dict(size=12)
        )
        
        return fig
    
    def create_indicators_subplot(self, indicators: Dict) -> go.Figure:
        """
        Cria subplots com indicadores técnicos.
        
        Args:
            indicators: Dicionário com indicadores
            
        Returns:
            Figura Plotly com subplots
        """
        # Criar subplots
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('RSI', 'MACD', 'Estocástico'),
            vertical_spacing=0.08,
            row_heights=[0.33, 0.33, 0.34]
        )
        
        # RSI
        if 'RSI_14' in indicators:
            fig.add_trace(go.Scatter(
                x=self.df.index,
                y=indicators['RSI_14'],
                mode='lines',
                name='RSI(14)',
                line=dict(color=self.colors['primary'])
            ), row=1, col=1)
            
            # Linhas de referência RSI
            fig.add_hline(y=70, line_dash="dash", line_color="red", 
                         annotation_text="Sobrecomprado", row=1, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", 
                         annotation_text="Sobrevendido", row=1, col=1)
        
        # MACD
        if all(key in indicators for key in ['MACD', 'MACD_Signal', 'MACD_Histogram']):
            fig.add_trace(go.Scatter(
                x=self.df.index,
                y=indicators['MACD'],
                mode='lines',
                name='MACD',
                line=dict(color=self.colors['info'])
            ), row=2, col=1)
            
            fig.add_trace(go.Scatter(
                x=self.df.index,
                y=indicators['MACD_Signal'],
                mode='lines',
                name='Signal',
                line=dict(color=self.colors['warning'])
            ), row=2, col=1)
            
            # Histograma MACD
            colors_hist = ['green' if x >= 0 else 'red' for x in indicators['MACD_Histogram']]
            fig.add_trace(go.Bar(
                x=self.df.index,
                y=indicators['MACD_Histogram'],
                name='Histogram',
                marker_color=colors_hist,
                opacity=0.6
            ), row=2, col=1)
        
        # Estocástico
        if all(key in indicators for key in ['Stoch_K', 'Stoch_D']):
            fig.add_trace(go.Scatter(
                x=self.df.index,
                y=indicators['Stoch_K'],
                mode='lines',
                name='%K',
                line=dict(color=self.colors['primary'])
            ), row=3, col=1)
            
            fig.add_trace(go.Scatter(
                x=self.df.index,
                y=indicators['Stoch_D'],
                mode='lines',
                name='%D',
                line=dict(color=self.colors['secondary'])
            ), row=3, col=1)
            
            # Linhas de referência Estocástico
            fig.add_hline(y=80, line_dash="dash", line_color="red", row=3, col=1)
            fig.add_hline(y=20, line_dash="dash", line_color="green", row=3, col=1)
        
        fig.update_layout(
            height=800,
            template='plotly_white',
            showlegend=True,
            font=dict(size=10)
        )
        
        return fig
    
    def create_correlation_heatmap(self) -> go.Figure:
        """
        Cria heatmap de correlação entre indicadores.
        
        Returns:
            Figura Plotly
        """
        # Selecionar colunas numéricas para correlação
        numeric_cols = self.df.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.df[numeric_cols].corr()
        
        fig = go.Figure(data=go.Heatmap(
            z=correlation_matrix.values,
            x=correlation_matrix.columns,
            y=correlation_matrix.columns,
            colorscale='RdBu',
            zmid=0,
            text=correlation_matrix.round(2).values,
            texttemplate="%{text}",
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        fig.update_layout(
            title='Matriz de Correlação - Indicadores Técnicos',
            height=600,
            template='plotly_white'
        )
        
        return fig
    
    def create_price_distribution(self) -> go.Figure:
        """
        Cria histograma da distribuição de preços.
        
        Returns:
            Figura Plotly
        """
        fig = go.Figure()
        
        fig.add_trace(go.Histogram(
            x=self.df['Close'],
            nbinsx=50,
            name='Distribuição de Preços',
            marker_color=self.colors['primary'],
            opacity=0.7
        ))
        
        # Adicionar linha da média
        mean_price = self.df['Close'].mean()
        fig.add_vline(
            x=mean_price,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Média: ${mean_price:.2f}"
        )
        
        fig.update_layout(
            title='Distribuição de Preços do Bitcoin',
            xaxis_title='Preço (USDT)',
            yaxis_title='Frequência',
            template='plotly_white',
            height=400
        )
        
        return fig
    
    def create_returns_analysis(self) -> go.Figure:
        """
        Cria análise de retornos.
        
        Returns:
            Figura Plotly
        """
        if 'Returns' not in self.df.columns:
            self.df['Returns'] = self.df['Close'].pct_change()
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('Retornos ao Longo do Tempo', 'Distribuição dos Retornos'),
            vertical_spacing=0.1
        )
        
        # Retornos ao longo do tempo
        fig.add_trace(go.Scatter(
            x=self.df.index,
            y=self.df['Returns'] * 100,
            mode='lines',
            name='Retornos (%)',
            line=dict(color=self.colors['primary'])
        ), row=1, col=1)
        
        # Distribuição dos retornos
        fig.add_trace(go.Histogram(
            x=self.df['Returns'] * 100,
            nbinsx=50,
            name='Distribuição',
            marker_color=self.colors['info'],
            opacity=0.7
        ), row=2, col=1)
        
        fig.update_layout(
            height=600,
            template='plotly_white',
            showlegend=False
        )
        
        fig.update_xaxes(title_text="Data", row=1, col=1)
        fig.update_yaxes(title_text="Retorno (%)", row=1, col=1)
        fig.update_xaxes(title_text="Retorno (%)", row=2, col=1)
        fig.update_yaxes(title_text="Frequência", row=2, col=1)
        
        return fig
    
    def create_volatility_chart(self) -> go.Figure:
        """
        Cria gráfico de volatilidade.
        
        Returns:
            Figura Plotly
        """
        if 'Volatility_24h' not in self.df.columns:
            if 'Returns' not in self.df.columns:
                self.df['Returns'] = self.df['Close'].pct_change()
            self.df['Volatility_24h'] = self.df['Returns'].rolling(window=24).std()
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=self.df.index,
            y=self.df['Volatility_24h'] * 100,
            mode='lines',
            name='Volatilidade 24h (%)',
            line=dict(color=self.colors['warning'], width=2),
            fill='tonexty',
            fillcolor=f'rgba(255, 193, 7, 0.1)'
        ))
        
        # Adicionar média da volatilidade
        mean_vol = self.df['Volatility_24h'].mean() * 100
        fig.add_hline(
            y=mean_vol,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Volatilidade Média: {mean_vol:.2f}%"
        )
        
        fig.update_layout(
            title='Volatilidade do Bitcoin (24h)',
            xaxis_title='Data',
            yaxis_title='Volatilidade (%)',
            template='plotly_white',
            height=400
        )
        
        return fig


def create_dashboard_charts(df: pd.DataFrame, indicators: Dict) -> Dict[str, go.Figure]:
    """
    Cria todos os gráficos para o dashboard.
    
    Args:
        df: DataFrame com dados do Bitcoin
        indicators: Dicionário com indicadores técnicos
        
    Returns:
        Dicionário com todas as figuras
    """
    visualizer = BitcoinVisualizer(df)
    
    charts = {
        'candlestick': visualizer.create_candlestick_chart(indicators),
        'volume': visualizer.create_volume_chart(),
        'indicators': visualizer.create_indicators_subplot(indicators),
        'correlation': visualizer.create_correlation_heatmap(),
        'distribution': visualizer.create_price_distribution(),
        'returns': visualizer.create_returns_analysis(),
        'volatility': visualizer.create_volatility_chart()
    }
    
    return charts


if __name__ == "__main__":
    # Teste do módulo
    from data_processing import load_bitcoin_data
    from technical_analysis import TechnicalAnalyzer
    
    # Carregar e processar dados
    data = load_bitcoin_data()
    analyzer = TechnicalAnalyzer(data)
    indicators_data = analyzer.calculate_all_indicators()
    indicators_dict = analyzer.indicators
    
    # Criar visualizações
    visualizer = BitcoinVisualizer(indicators_data)
    
    # Exemplo: criar gráfico de candlestick
    fig = visualizer.create_candlestick_chart(indicators_dict)
    fig.show()
    
    print("Visualizações criadas com sucesso!")
