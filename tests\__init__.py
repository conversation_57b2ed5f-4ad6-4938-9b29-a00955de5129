"""
Módulo de testes para CryptoSignals
Contém todos os testes unitários e de integração
"""

__version__ = "1.0.0"
__author__ = "CryptoSignals Team"

# Configurações de teste
TEST_CONFIG = {
    'database': {
        'use_memory': True,
        'backup_real_db': True
    },
    'auth': {
        'test_users': [
            {
                'username': 'testuser1',
                'email': '<EMAIL>',
                'password': 'TestPass123!',
                'plan': 'free'
            },
            {
                'username': 'testuser2',
                'email': '<EMAIL>',
                'password': 'TestPass456!',
                'plan': 'professional'
            }
        ]
    },
    'payments': {
        'test_wallet': '******************************************',
        'test_amounts': [10, 50, 100, 500]
    },
    'performance': {
        'max_response_time': 200,  # ms
        'concurrent_users': 100,
        'test_duration': 60  # seconds
    }
}

# Utilitários de teste
def setup_test_environment():
    """Configura ambiente de teste"""
    import os
    import tempfile
    
    # Criar diretório temporário para testes
    test_dir = tempfile.mkdtemp(prefix='cryptosignals_test_')
    os.environ['CRYPTOSIGNALS_TEST_DIR'] = test_dir
    
    return test_dir

def cleanup_test_environment():
    """Limpa ambiente de teste"""
    import os
    import shutil
    
    test_dir = os.environ.get('CRYPTOSIGNALS_TEST_DIR')
    if test_dir and os.path.exists(test_dir):
        shutil.rmtree(test_dir)
        del os.environ['CRYPTOSIGNALS_TEST_DIR']
