/* <PERSON><PERSON><PERSON><PERSON><PERSON> */
:root {
    /* Cores */
    --primary-color: #FFD700;
    --secondary-color: #1a1a1a;
    --accent-color: #4a90e2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --light-bg: #f8f9fa;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --card-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
    --gradient-primary: linear-gradient(45deg, var(--primary-color), #FFA500);
    --gradient-dark: linear-gradient(135deg, var(--secondary-color), #000);
    --gradient-glow: linear-gradient(45deg, rgba(255, 215, 0, 0.5), rgba(255, 165, 0, 0.5));
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --gradient-shine: linear-gradient(120deg, transparent 0%, rgba(255, 215, 0, 0.3) 50%, transparent 100%);
    --gradient-hover: linear-gradient(45deg, rgba(255,215,0,0.1), rgba(255,165,0,0.1));
    --shadow-soft: 0 5px 15px rgba(0, 0, 0, 0.1);
    --shadow-strong: 0 10px 30px rgba(0, 0, 0, 0.15);
    --shadow-glow: 0 5px 20px rgba(255, 215, 0, 0.2);
    --border-radius: 20px;
    --border-glow: 1px solid rgba(255, 215, 0, 0.2);

    /* Dimensões */
    --header-height: 70px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --menu-item-height: 50px;
    --menu-category-height: 40px;
    --card-border-radius: 15px;
    --button-border-radius: 8px;
}

/* Reset e Estilos Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    background-color: var(--light-bg);
}

/* Tipografia */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.display-4 {
    font-size: clamp(2rem, 5vw, 3.5rem);
}

.lead {
    font-size: clamp(1rem, 2vw, 1.25rem);
}

/* Links */
a {
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition-smooth);
}

a:hover {
    color: var(--primary-color);
}

/* Container Responsivo */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Glassmorphism */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-soft);
}

/* Melhorias Gerais */
body {
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--light-bg);
    overflow-x: hidden;
}

/* Animações Otimizadas */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes shine {
    to {
        background-position: 200% center;
    }
}

/* Efeitos de Glassmorphism Melhorados */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--card-shadow);
    transition: var(--transition-smooth);
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 40px rgba(31, 38, 135, 0.2);
}

/* Seção de Features Atualizada */
.features-section {
    position: relative;
    overflow: hidden;
    padding: 120px 0;
    background: var(--gradient-glow);
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="%23FFD700" opacity="0.1"/></svg>');
    opacity: 0.4;
    z-index: 0;
}

.feature-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 40px;
    transition: var(--transition-smooth);
    border: 1px solid rgba(255,215,0,0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

.feature-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: -1;
}

.feature-card:hover::before {
    opacity: 0.05;
}

.feature-icon {
    width: 90px;
    height: 90px;
    background: var(--gradient-primary);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    position: relative;
    transform: rotate(-5deg);
    transition: var(--transition-smooth);
}

.feature-icon::after {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: inherit;
    padding: 3px;
    background: var(--gradient-primary);
    -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
    mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
}

.feature-icon i {
    font-size: 2.8rem;
    color: var(--secondary-color);
    transition: var(--transition-smooth);
}

.feature-card:hover .feature-icon {
    transform: rotate(0deg) scale(1.1);
}

.feature-card:hover .feature-icon i {
    transform: scale(1.2);
}

.feature-card h4 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% auto;
    animation: shine 3s linear infinite;
}

.feature-card p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 25px;
    color: rgba(0,0,0,0.7);
}

.feature-card .badge {
    padding: 8px 16px;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 20px;
    background: var(--gradient-primary);
    border: none;
    transition: var(--transition-smooth);
}

.feature-card:hover .badge {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255,215,0,0.3);
}

/* Efeito de Link Melhorado */
.feature-card .btn-link {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--primary-color);
    text-decoration: none;
    position: relative;
    padding: 0;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition-smooth);
}

.feature-card .btn-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.feature-card .btn-link:hover {
    color: #FFA500;
}

.feature-card .btn-link:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.feature-card .btn-link i {
    transition: transform 0.3s ease;
}

.feature-card .btn-link:hover i {
    transform: translateX(5px);
}

/* Efeito de Destaque Melhorado */
.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255,215,0,0.1) 25%,
        rgba(255,215,0,0.1) 50%,
        transparent 100%
    );
    transform: translateX(-100%);
    transition: transform 0.8s ease;
}

.feature-card:hover::after {
    transform: translateX(50%);
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Navbar Melhorado */
.navbar {
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(0);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.navbar.hidden {
    transform: translateY(-100%);
}

.navbar.scrolled {
    background: rgba(26, 26, 26, 0.95);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand .gradient-text {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: -0.5px;
}

/* Hero Section Melhorada */
.hero-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
    position: relative;
    padding: 160px 0 120px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.1) 0%, transparent 40%);
    pointer-events: none;
}

/* Cards Melhorados */
.crypto-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
    perspective: 1000px;
}

.crypto-card:hover {
    transform: translateX(10px) translateY(-5px) rotateY(5deg);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.feature-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-card:hover::before {
    opacity: 0.1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.pricing-card {
    background: white;
    border-radius: 24px;
    padding: 40px 30px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(45deg, var(--primary-color), #FFA500);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.1);
}

.pricing-card:hover::before {
    opacity: 1;
}

/* Botões Melhorados */
.btn {
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #FFA500);
    border: none;
    color: var(--secondary-color);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transform: skewX(-25deg);
    transition: all 0.5s ease;
}

.btn-primary:hover::before {
    left: 200%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

/* Estatísticas Melhoradas */
.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 35px 25px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: var(--gradient-shine);
    transform: rotate(45deg);
    animation: stat-shine 3s infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

@keyframes stat-shine {
    0% { transform: rotate(45deg) translateY(-100%); }
    100% { transform: rotate(45deg) translateY(100%); }
}

/* Footer Melhorado */
footer {
    background: var(--secondary-color);
    color: white;
    padding: 80px 0 40px;
}

footer h5 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 25px;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

footer a::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

footer a:hover::before {
    transform: scaleX(1);
    transform-origin: left;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-3px);
}

/* Animações Suaves */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.floating {
    animation: float 3s ease-in-out infinite;
}

/* Responsividade Melhorada */
@media (max-width: 768px) {
    .hero-section {
        padding: 120px 0 80px;
    }
    
    .navbar-brand .gradient-text {
        font-size: 1.5rem;
    }
    
    .btn {
        padding: 10px 25px;
        font-size: 0.85rem;
    }
    
    .pricing-card {
        padding: 30px 20px;
    }
    
    .feature-card {
        margin-bottom: 30px;
    }
    
    .stat-card {
        margin-bottom: 20px;
    }
    
    .hero-section {
        text-align: center;
    }
    
    .trusted-by {
        justify-content: center;
    }
}

/* Animação de Preço */
@keyframes priceChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: var(--primary-color); }
    100% { transform: scale(1); }
}

.price-changed {
    animation: priceChange 0.5s ease;
}

/* Efeito de Brilho */
@keyframes shine {
    0% { background-position: -100% 50%; }
    100% { background-position: 200% 50%; }
}

.gradient-text {
    background: linear-gradient(90deg, var(--primary-color) 0%, #FFA500 50%, var(--primary-color) 100%);
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine 3s linear infinite;
}

/* Efeito de Partículas */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.3;
    animation: particleFloat 15s infinite linear;
}

@keyframes particleFloat {
    0% { transform: translateY(0) translateX(0); opacity: 0; }
    50% { opacity: 0.5; }
    100% { transform: translateY(-100vh) translateX(100vw); opacity: 0; }
}

/* Efeito de Loading */
.loading-bar {
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, 
        rgba(255,215,0,0.1) 0%,
        rgba(255,215,0,0.2) 25%,
        rgba(255,215,0,0.1) 50%,
        rgba(255,215,0,0.2) 75%,
        rgba(255,215,0,0.1) 100%
    );
    background-size: 200% 100%;
    animation: loading-gradient 2s linear infinite;
    position: relative;
    overflow: hidden;
    border-radius: 2px;
}

@keyframes loading-gradient {
    0% { background-position: 100% 0; }
    100% { background-position: -100% 0; }
}

/* Animação do Logo */
@keyframes logoSpin {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
}

@keyframes logoPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); filter: brightness(1.2); }
    100% { transform: scale(1); }
}

.navbar-brand img {
    transition: all 0.3s ease;
    animation: logoPulse 3s infinite;
}

.navbar-brand:hover img {
    animation: logoSpin 1s ease-out;
}

.navbar-brand {
    position: relative;
}

.navbar-brand::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.navbar-brand:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Efeito de Scroll Suave */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

/* Barra de Scroll Personalizada */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 6px;
    border: 3px solid var(--secondary-color);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Efeito de Texto Brilhante */
.shine-text {
    position: relative;
    overflow: hidden;
}

.shine-text::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: var(--gradient-shine);
    transform: skewX(-25deg);
    animation: shine-effect 3s infinite;
}

@keyframes shine-effect {
    0% { left: -100%; }
    50% { left: 200%; }
    100% { left: 200%; }
}

/* Efeito de Foco nos Inputs */
.form-control:focus {
    box-shadow: 0 0 0 3px var(--border-glow);
    border-color: var(--primary-color);
}

/* Efeito de Destaque nos Preços */
.card-price {
    position: relative;
    display: inline-block;
}

.card-price::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0.7);
    transition: transform 0.3s ease;
}

.pricing-card:hover .card-price::after {
    transform: scaleX(1);
}

/* Animação de Entrada para Elementos */
[data-aos] {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

/* Melhorias nos Sinais */
.latest-signals {
    background: var(--light-bg);
    position: relative;
    overflow: hidden;
}

.signal-card {
    background: linear-gradient(145deg, #ffffff, #f5f5f5);
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
}

.signal-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
    border-color: var(--primary-color);
}

.signal-type {
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
    font-weight: 700;
}

.signal-card h4 {
    font-weight: 700;
    color: var(--secondary-color);
}

.signal-time {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.signal-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.signal-details span {
    display: flex;
    justify-content: space-between;
    color: #6c757d;
    font-size: 0.9rem;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.signal-details span:last-child {
    border-bottom: none;
}

.signal-details strong {
    color: #2c3e50;
    font-weight: 600;
}

/* Navegação do Swiper */
.swiper-button-next,
.swiper-button-prev {
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.swiper-pagination-bullet {
    background: var(--primary-color);
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--primary-color);
}

/* Melhorias nos Cards de Cripto */
.crypto-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 20px;
    transition: all 0.4s ease;
}

.crypto-card:hover {
    transform: translateY(-5px) scale(1.02);
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
}

.crypto-card .price {
    font-size: 1.5rem;
    font-weight: 700;
}

.crypto-card .text-success {
    color: #28c76f !important;
    font-weight: 600;
}

/* Melhorias na Seção Hero */
.hero-section {
    padding: 180px 0 120px;
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.1) 0%, transparent 40%);
    pointer-events: none;
}

/* Melhorias nos Badges */
.badge {
    padding: 8px 16px;
    border-radius: 30px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.8rem;
}

.badge.bg-warning {
    background: linear-gradient(45deg, var(--primary-color), #FFA500) !important;
    color: var(--secondary-color) !important;
    border: none;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

/* Melhorias nos Botões */
.btn {
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.4s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #FFA500);
    border: none;
    color: var(--secondary-color);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: transparent;
    color: white;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-2px);
}

/* Melhorias na Responsividade */
@media (max-width: 768px) {
    .hero-section {
        padding: 120px 0 80px;
        text-align: center;
    }

    .signal-details {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .crypto-card {
        margin: 10px;
    }

    .swiper-button-next,
    .swiper-button-prev {
        display: none;
    }
}

/* Animações Suaves */
@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.floating {
    animation: float 3s ease-in-out infinite;
}

/* Preloader Melhorado */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader-hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Cursor Personalizado */
.custom-cursor {
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s ease;
    transform: translate(-50%, -50%);
}

.custom-cursor-dot {
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.15s ease;
    transform: translate(-50%, -50%);
}

/* Efeitos de Hover */
a:hover ~ .custom-cursor {
    transform: translate(-50%, -50%) scale(1.5);
    background: rgba(255, 215, 0, 0.1);
}

/* Scrollbar Personalizada */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #FFA500;
}

/* Estilos dos Depoimentos */
.testimonial-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
    border-color: var(--primary-color);
}

.user-avatar {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.1);
    transition: var(--transition-smooth);
}

.testimonial-card:hover .user-avatar {
    transform: scale(1.1);
    background: rgba(255, 215, 0, 0.2);
}

.testimonial-card:hover .user-avatar i {
    transform: scale(1.1);
    filter: brightness(1.2);
}

.testimonial-card h5 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.testimonial-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.7);
    font-style: italic;
}

/* Header Styles */
.dashboard-header {
    height: var(--header-height);
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    position: relative;
    cursor: pointer;
    color: var(--dark-color);
    transition: var(--transition-smooth);
}

.header-icon:hover {
    color: var(--primary-color);
}

.header-icon .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary-color);
    color: var(--secondary-color);
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50px;
    transition: var(--transition-smooth);
}

.user-profile:hover {
    background: rgba(0, 0, 0, 0.05);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
}

.user-info {
    line-height: 1.2;
}

.user-name {
    font-weight: 600;
    color: var(--dark-color);
}

.user-role {
    font-size: 0.85rem;
    color: var(--gray-color);
}

/* Estilos para a Página de Análise Técnica */
/* Layout Principal */
.chart-container {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 600px;
    margin-bottom: 20px;
}

.chart-container:hover {
    box-shadow: var(--shadow-medium);
}

/* Seletor de Par e Timeframe */
.pair-selector .form-select {
    height: 45px;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 500;
    background-color: var(--white);
    transition: var(--transition-smooth);
}

.pair-selector .form-select:hover,
.pair-selector .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

/* Timeframe Selector */
.timeframe-selector .btn-group {
    width: 100%;
    background: var(--white);
    border-radius: 10px;
    padding: 5px;
    box-shadow: var(--shadow-soft);
}

.timeframe-selector .btn {
    flex: 1;
    border: none;
    padding: 8px 15px;
    font-weight: 500;
    color: var(--text-muted);
    transition: var(--transition-smooth);
}

.timeframe-selector .btn:hover {
    color: var(--primary-color);
}

.timeframe-selector .btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-radius: 8px;
}

/* Card de Informações do Ativo */
.asset-info-card {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.asset-info-card:hover {
    box-shadow: var(--shadow-medium);
}

.asset-info-card .card-title {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 10px;
    font-weight: 500;
}

.price-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.current-price {
    font-size: 1.8rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.price-change {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.price-change.up {
    background: rgba(40, 199, 111, 0.1);
    color: #28c76f;
}

.price-change.down {
    background: rgba(234, 84, 85, 0.1);
    color: #ea5455;
}

.trading-info {
    margin-top: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
}

.info-item .label {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.info-item .value {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Cards de Indicadores */
.indicators-container {
    display: grid;
    gap: 15px;
}

.indicator-card {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.indicator-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: var(--transition-smooth);
}

.indicator-card:hover {
    box-shadow: var(--shadow-medium);
}

.indicator-card:hover::before {
    opacity: 1;
}

.indicator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.indicator-title {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 500;
}

.indicator-value {
    font-size: 1.2rem;
    font-weight: 600;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.indicator-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.indicator-trend.up {
    background: rgba(40, 199, 111, 0.1);
    color: #28c76f;
}

.indicator-trend.down {
    background: rgba(234, 84, 85, 0.1);
    color: #ea5455;
}

.indicator-trend.neutral {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
}

/* Card de Resumo da Análise */
.analysis-summary-card {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.analysis-summary-card:hover {
    box-shadow: var(--shadow-medium);
}

.analysis-summary-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.trend-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
}

.trend-label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.trend-value {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    font-size: 0.9rem;
}

.trend-value.up {
    color: #28c76f;
}

.trend-value.down {
    color: #ea5455;
}

.support-resistance {
    display: grid;
    gap: 10px;
}

.level {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
}

.level.resistance {
    border-left: 3px solid #ea5455;
}

.level.support {
    border-left: 3px solid #28c76f;
}

.level .label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.level .value {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Responsividade */
@media (max-width: 992px) {
    .chart-container {
        height: 400px;
    }
    
    .timeframe-selector .btn {
        padding: 6px 10px;
        font-size: 0.9rem;
    }
    
    .pair-selector .form-select {
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .header-actions {
        gap: 10px;
    }
    
    .pair-selector,
    .timeframe-selector {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .timeframe-selector .btn-group {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;
    }
}

/* Estilos do Dashboard */
.portfolio-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 25px;
    height: 100%;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
}

.portfolio-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    transform: rotate(45deg);
    z-index: 0;
    transition: var(--transition-smooth);
}

.portfolio-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-strong);
}

.portfolio-card .card-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
}

.portfolio-card .card-icon i {
    font-size: 24px;
    color: var(--secondary-color);
}

.portfolio-card .card-info h6 {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.portfolio-card .card-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 15px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.portfolio-card .trend {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.portfolio-card .trend.up {
    background: rgba(40, 199, 111, 0.1);
    color: #28c76f;
}

.portfolio-card .trend.down {
    background: rgba(234, 84, 85, 0.1);
    color: #ea5455;
}

.portfolio-card .trend.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Chart Cards */
.chart-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 25px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    height: 100%;
}

.chart-card:hover {
    box-shadow: var(--shadow-strong);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 10px;
}

.chart-actions .btn {
    padding: 5px 15px;
    font-size: 0.85rem;
}

/* Table Cards */
.table-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
}

.table-card:hover {
    box-shadow: var(--shadow-strong);
}

.table-card .card-header {
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-card .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.table-card .table {
    margin: 0;
}

.table-card .table th {
    font-weight: 600;
    color: var(--gray-color);
    border-bottom-width: 1px;
    padding: 15px 25px;
}

.table-card .table td {
    padding: 15px 25px;
    vertical-align: middle;
}

.status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-dot.active {
    background: var(--warning-color);
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

.status-dot.completed {
    background: var(--success-color);
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.profit {
    color: var(--success-color);
    font-weight: 600;
}

.loss {
    color: var(--danger-color);
    font-weight: 600;
}

/* Notifications Card */
.notifications-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
}

.notifications-card:hover {
    box-shadow: var(--shadow-strong);
}

.notifications-card .card-header {
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-card .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.notifications-list {
    padding: 0;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: var(--transition-smooth);
}

.notification-item:hover {
    background: rgba(0, 0, 0, 0.02);
}

.notification-item.unread {
    background: rgba(255, 215, 0, 0.05);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.notification-icon.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.notification-icon.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.notification-icon.info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.notification-content {
    flex: 1;
}

.notification-content h6 {
    margin: 0 0 5px;
    font-size: 1rem;
    font-weight: 600;
}

.notification-content p {
    margin: 0;
    color: var(--gray-color);
    font-size: 0.9rem;
}

.notification-time {
    font-size: 0.8rem;
    color: var(--gray-color);
    margin-top: 5px;
    display: block;
}

/* Responsividade */
@media (max-width: 992px) {
    .portfolio-card {
        margin-bottom: 20px;
    }

    .chart-card {
        margin-bottom: 20px;
    }

    .notifications-card .card-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .notifications-card .btn {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .chart-actions {
        flex-wrap: wrap;
    }

    .chart-actions .btn {
        flex: 1;
    }

    .table-card {
        margin-bottom: 20px;
    }

    .notification-item {
        flex-direction: column;
    }

    .notification-icon {
        margin-bottom: 10px;
    }
}

/* Estilos da Página de Sinais */
.signals-stats-card {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 25px;
    box-shadow: var(--card-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.signals-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.signals-stats-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
    transition: var(--transition-smooth);
}

.signals-stats-card .card-icon.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.signals-stats-card .card-icon.primary {
    background: rgba(255, 215, 0, 0.1);
    color: var(--primary-color);
}

.signals-stats-card .card-icon.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.signals-stats-card .card-icon.info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.signals-stats-card .card-info h6 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.signals-stats-card .card-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.signals-stats-card .trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    color: #666;
}

.signals-stats-card .trend.up {
    color: var(--success-color);
}

.signals-stats-card .trend.down {
    color: var(--danger-color);
}

.signals-stats-card .trend.neutral {
    color: var(--warning-color);
}

/* Filtros */
.signals-filters {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--card-shadow);
}

.filter-group label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
}

.filter-group .form-select,
.filter-group .form-control {
    border-radius: var(--button-border-radius);
    border: 1px solid #eee;
    padding: 10px 15px;
    transition: var(--transition-smooth);
}

.filter-group .form-select:focus,
.filter-group .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

/* Tabela de Sinais */
.signals-table {
    background: white;
    border-radius: var(--card-border-radius);
    padding: 25px;
    box-shadow: var(--card-shadow);
}

.signals-table .table {
    margin: 0;
}

.signals-table th {
    font-weight: 600;
    color: #555;
    padding: 15px;
    border-bottom: 2px solid #eee;
}

.signals-table td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
}

.signals-table tr:last-child td {
    border-bottom: none;
}

.crypto-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
}

.signal-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.signal-status.active {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.signal-status.completed {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.signal-status.cancelled {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.05);
    color: #666;
    border: none;
    transition: var(--transition-smooth);
}

.btn-icon:hover {
    background: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Modal de Novo Sinal */
.modal-content {
    border-radius: var(--card-border-radius);
    border: none;
    box-shadow: var(--card-shadow);
}

.modal-header {
    border-bottom: 1px solid #eee;
    padding: 20px 25px;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 20px 25px;
}

/* Paginação */
.signals-pagination {
    margin-top: 30px;
}

.pagination {
    gap: 5px;
}

.page-link {
    border: none;
    padding: 8px 16px;
    color: #666;
    border-radius: var(--button-border-radius);
    transition: var(--transition-smooth);
}

.page-link:hover {
    background: rgba(255, 215, 0, 0.1);
    color: var(--primary-color);
}

.page-item.active .page-link {
    background: var(--primary-color);
    color: var(--secondary-color);
}

.page-item.disabled .page-link {
    background: none;
    color: #999;
}

/* Responsividade */
@media (max-width: 992px) {
    .signals-filters .row {
        gap: 15px;
    }
    
    .signals-table {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .signals-stats-card {
        margin-bottom: 15px;
    }
    
    .signals-table {
        overflow-x: auto;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }
}

/* Estilos do Menu Lateral */
.sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    background: linear-gradient(135deg, var(--secondary-color), #000);
    color: white;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    height: var(--header-height);
    padding: 0 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-logo img {
    height: 35px;
    width: auto;
    transition: all 0.3s ease;
}

.sidebar-logo .gradient-text {
    font-size: 1.2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    white-space: nowrap;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: none;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--primary-color);
}

.sidebar-menu {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.sidebar-menu::-webkit-scrollbar {
    width: 5px;
}

.sidebar-menu::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-menu::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
}

.menu-category {
    padding: 0 25px;
    height: var(--menu-category-height);
    display: flex;
    align-items: center;
    font-size: 0.75rem;
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.5);
    letter-spacing: 1px;
    margin-top: 20px;
}

.menu-item {
    height: var(--menu-item-height);
    padding: 0 25px;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    border-left: 3px solid transparent;
}

.menu-item i {
    width: 20px;
    font-size: 1.1rem;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.menu-item span {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.menu-item .badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    border-radius: 20px;
    background: var(--primary-color);
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.menu-item:hover i {
    transform: translateX(3px);
    color: var(--primary-color);
}

.menu-item:hover .badge {
    transform: scale(1.1);
}

.menu-item.active {
    background: rgba(255, 215, 0, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.menu-item.active i {
    color: var(--primary-color);
}

.menu-item.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 4px 0 0 4px;
}

/* Responsividade do Menu */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
    }

    .sidebar-toggle {
        display: block;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
    }

    .sidebar-logo .gradient-text {
        font-size: 1rem;
    }

    .menu-item {
        padding: 0 20px;
    }

    .menu-category {
        padding: 0 20px;
    }
}

/* Ajuste do conteúdo principal */
.main-content {
    margin-left: var(--sidebar-width);
    transition: all 0.3s ease;
    min-height: 100vh;
    background: var(--light-bg);
}

/* Estilos para Cards de Estatísticas dos Bots */
.bot-stats-card {
    background: var(--white);
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.bot-stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: var(--transition-smooth);
}

.bot-stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.bot-stats-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
    transition: var(--transition-smooth);
}

.bot-stats-card .card-icon.success {
    background: rgba(40, 199, 111, 0.1);
    color: var(--success-color);
}

.bot-stats-card .card-icon.primary {
    background: rgba(115, 103, 240, 0.1);
    color: #7367f0;
}

.bot-stats-card .card-icon.warning {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
}

.bot-stats-card .card-icon.info {
    background: rgba(0, 207, 232, 0.1);
    color: #00cfe8;
}

.bot-stats-card .card-info h6 {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.bot-stats-card .card-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Lista de Bots */
.bot-list-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bot-list-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bot-list-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.bot-list-card .table {
    margin: 0;
}

.bot-list-card .table th {
    font-weight: 500;
    color: var(--text-muted);
    border-bottom-width: 1px;
    padding: 15px 20px;
}

.bot-list-card .table td {
    padding: 15px 20px;
    vertical-align: middle;
}

.bot-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.bot-icon.success {
    background: rgba(40, 199, 111, 0.1);
    color: var(--success-color);
}

.bot-icon.warning {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
}

.bot-status {
    padding: 6px 12px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
}

.bot-status.active {
    background: rgba(40, 199, 111, 0.1);
    color: var(--success-color);
}

.bot-status.paused {
    background: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
}

.bot-status.stopped {
    background: rgba(234, 84, 85, 0.1);
    color: #ea5455;
}

/* Histórico de Operações */
.bot-history-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bot-history-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bot-history-card .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.bot-history-card .table {
    margin: 0;
}

.bot-history-card .table th {
    font-weight: 500;
    color: var(--text-muted);
    border-bottom-width: 1px;
    padding: 15px 20px;
}

.bot-history-card .table td {
    padding: 15px 20px;
    vertical-align: middle;
}

/* Responsividade */
@media (max-width: 992px) {
    .bot-stats-card {
        margin-bottom: 20px;
    }
    
    .bot-list-card .table {
        min-width: 800px;
    }
    
    .bot-history-card .table {
        min-width: 800px;
    }
}

@media (max-width: 768px) {
    .bot-stats-card {
        padding: 15px;
    }
    
    .bot-stats-card .card-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .bot-stats-card .card-info h3 {
        font-size: 1.3rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 5px;
    }
}

/* Estilos para a Página da Carteira */
.transactions-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    height: 100%;
}

.transactions-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.transactions-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.transactions-list {
    padding: 20px;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-speed) ease;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-item:hover {
    background: rgba(0, 0, 0, 0.02);
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 16px;
}

.transaction-icon.buy {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.transaction-icon.sell {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.transaction-icon.deposit {
    background: rgba(0, 123, 255, 0.1);
    color: #0d6efd;
}

.transaction-info {
    flex: 1;
}

.transaction-info h6 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.transaction-info p {
    margin: 0;
    font-size: 0.85rem;
    color: #6c757d;
}

.transaction-amount {
    text-align: right;
}

.transaction-amount .amount {
    display: block;
    font-weight: 600;
    color: var(--dark-color);
}

.transaction-amount .time {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
}

.table-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.table-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* Estilos para a Página de Configurações */
.settings-nav {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.settings-nav .list-group-item {
    border: none;
    padding: 15px 20px;
    color: var(--dark-color);
    font-weight: 500;
    transition: all var(--transition-speed) ease;
}

.settings-nav .list-group-item:hover {
    background: rgba(255, 165, 0, 0.05);
    color: var(--primary-color);
}

.settings-nav .list-group-item.active {
    background: var(--primary-color);
    color: white;
}

.settings-nav .list-group-item i {
    width: 20px;
    text-align: center;
}

.settings-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.settings-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.settings-card .card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.settings-card .card-body {
    padding: 25px;
}

.avatar-upload {
    position: relative;
    width: 150px;
    margin: 0 auto;
}

.avatar-preview {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: #adb5bd;
    border: 2px dashed #dee2e6;
    transition: all var(--transition-speed) ease;
}

.avatar-preview:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.notification-group h6 {
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.current-plan {
    background: rgba(255, 165, 0, 0.05);
    border-radius: var(--card-border-radius);
    padding: 20px;
}

.plan-details {
    margin: 15px 0;
}

.plan-details p {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.plan-actions {
    margin-top: 20px;
}

.integration-item {
    background: #f8f9fa;
    border-radius: var(--card-border-radius);
    padding: 20px;
}

.integration-item h6 {
    margin: 0;
    color: var(--dark-color);
}

.api-details {
    margin-top: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 10px;
}

.api-details p {
    margin: 0;
    font-family: monospace;
}

@media (max-width: 992px) {
    .settings-nav {
        margin-bottom: 20px;
    }
    
    .settings-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .avatar-upload {
        margin-bottom: 20px;
    }
    
    .plan-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .plan-actions button {
        width: 100%;
    }
}

.table-card {
    background: white;
    border-radius: var(--card-border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.table-card .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
}

/* Estilos da Página de Perfil */
.profile-card {
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

.profile-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2.5rem;
    transition: all 0.3s ease;
}

.profile-avatar:hover {
    transform: scale(1.05);
}

.profile-badges {
    margin-top: 1rem;
}

.profile-badges .badge {
    margin: 0 0.25rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 1.5rem 0;
    text-align: center;
}

.stat-item h6 {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.stat-item span {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.performance-card {
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.performance-card .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-card {
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
}

.activity-list {
    padding: 1.25rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.activity-icon.success {
    background: var(--success-light);
    color: var(--success);
}

.activity-icon.primary {
    background: var(--primary-light);
    color: var(--primary);
}

.activity-icon.warning {
    background: var(--warning-light);
    color: var(--warning);
}

.activity-content h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.activity-content p {
    margin-bottom: 0.25rem;
    color: var(--text-muted);
}

.history-card {
    background: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.history-card .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-card .table {
    margin-bottom: 0;
}

.history-card .table th {
    font-weight: 600;
    color: var(--text-muted);
    border-bottom-width: 1px;
}

.history-card .table td {
    vertical-align: middle;
    padding: 1rem 1.25rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .profile-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .activity-item {
        flex-direction: column;
    }
    
    .activity-icon {
        margin-bottom: 1rem;
    }
    
    .history-card .table {
        min-width: 800px;
    }
}

@media (max-width: 576px) {
    .profile-stats {
        grid-template-columns: 1fr;
    }
}

.card-bg {
    background-color: var(--white);
}

.card-shadow {
    box-shadow: var(--shadow-soft);
}

.card-shadow-hover {
    box-shadow: var(--shadow-medium);
}

.border-color {
    border-color: var(--border-glow);
}

.text-muted {
    color: var(--text-muted);
}

.text-primary {
    color: var(--primary-color);
}

.success-light {
    background-color: rgba(40, 199, 111, 0.1);
    color: var(--success-color);
}

.warning-light {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.primary-light {
    background-color: rgba(255, 215, 0, 0.1);
    color: var(--primary-color);
}

.danger-light {
    background-color: rgba(234, 84, 85, 0.1);
    color: var(--danger-color);
}

.success-dark {
    background-color: rgba(40, 199, 111, 0.2);
    color: var(--success-color);
}

.warning-dark {
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.primary-dark {
    background-color: rgba(255, 215, 0, 0.2);
    color: var(--primary-color);
}

.danger-dark {
    background-color: rgba(234, 84, 85, 0.2);
    color: var(--danger-color);
}

.success-glow {
    background-color: rgba(40, 199, 111, 0.3);
    color: var(--success-color);
}

.warning-glow {
    background-color: rgba(255, 193, 7, 0.3);
    color: var(--warning-color);
}

.primary-glow {
    background-color: rgba(255, 215, 0, 0.3);
    color: var(--primary-color);
}

.danger-glow {
    background-color: rgba(234, 84, 85, 0.3);
    color: var(--danger-color);
}

.success-shine {
    background: linear-gradient(120deg, transparent 0%, rgba(40, 199, 111, 0.3) 50%, transparent 100%);
    color: var(--success-color);
}

.warning-shine {
    background: linear-gradient(120deg, transparent 0%, rgba(255, 193, 7, 0.3) 50%, transparent 100%);
    color: var(--warning-color);
}

.primary-shine {
    background: linear-gradient(120deg, transparent 0%, rgba(255, 215, 0, 0.3) 50%, transparent 100%);
    color: var(--primary-color);
}

.danger-shine {
    background: linear-gradient(120deg, transparent 0%, rgba(234, 84, 85, 0.3) 50%, transparent 100%);
    color: var(--danger-color);
}

.success-shadow {
    box-shadow: 0 5px 15px rgba(40, 199, 111, 0.2);
    color: var(--success-color);
}

.warning-shadow {
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.primary-shadow {
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
    color: var(--primary-color);
}

.danger-shadow {
    box-shadow: 0 5px 15px rgba(234, 84, 85, 0.2);
    color: var(--danger-color);
}

.success-glow-shadow {
    box-shadow: 0 5px 20px rgba(40, 199, 111, 0.2);
    color: var(--success-color);
}

.warning-glow-shadow {
    box-shadow: 0 5px 20px rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.primary-glow-shadow {
    box-shadow: 0 5px 20px rgba(255, 215, 0, 0.2);
    color: var(--primary-color);
}

.danger-glow-shadow {
    box-shadow: 0 5px 20px rgba(234, 84, 85, 0.2);
    color: var(--danger-color);
}

.success-border {
    border-color: rgba(40, 199, 111, 0.2);
}

.warning-border {
    border-color: rgba(255, 193, 7, 0.2);
}

.primary-border {
    border-color: rgba(255, 215, 0, 0.2);
}

.danger-border {
    border-color: rgba(234, 84, 85, 0.2);
}

.success-glow-border {
    border-color: rgba(40, 199, 111, 0.3);
}

.warning-glow-border {
    border-color: rgba(255, 193, 7, 0.3);
}

.primary-glow-border {
    border-color: rgba(255, 215, 0, 0.3);
}

.danger-glow-border {
    border-color: rgba(234, 84, 85, 0.3);
}

.success-glow-text {
    color: rgba(40, 199, 111, 0.3);
}

.warning-glow-text {
    color: rgba(255, 193, 7, 0.3);
}

.primary-glow-text {
    color: rgba(255, 215, 0, 0.3);
}

.danger-glow-text {
    color: rgba(234, 84, 85, 0.3);
}

.success-glow-bg {
    background-color: rgba(40, 199, 111, 0.1);
}

.warning-glow-bg {
    background-color: rgba(255, 193, 7, 0.1);
}

.primary-glow-bg {
    background-color: rgba(255, 215, 0, 0.1);
}

.danger-glow-bg {
    background-color: rgba(234, 84, 85, 0.1);
}

.success-glow-border-hover {
    border-color: rgba(40, 199, 111, 0.4);
}

.warning-glow-border-hover {
    border-color: rgba(255, 193, 7, 0.4);
}

.primary-glow-border-hover {
    border-color: rgba(255, 215, 0, 0.4);
}

.danger-glow-border-hover {
    border-color: rgba(234, 84, 85, 0.4);
}

.success-glow-text-hover {
    color: rgba(40, 199, 111, 0.4);
}

.warning-glow-text-hover {
    color: rgba(255, 193, 7, 0.4);
}

.primary-glow-text-hover {
    color: rgba(255, 215, 0, 0.4);
}

.danger-glow-text-hover {
    color: rgba(234, 84, 85, 0.4);
}

.success-glow-bg-hover {
    background-color: rgba(40, 199, 111, 0.2);
}

.warning-glow-bg-hover {
    background-color: rgba(255, 193, 7, 0.2);
}

.primary-glow-bg-hover {
    background-color: rgba(255, 215, 0, 0.2);
}

.danger-glow-bg-hover {
    background-color: rgba(234, 84, 85, 0.2);
}

.success-glow-border-active {
    border-color: rgba(40, 199, 111, 0.5);
}

.warning-glow-border-active {
    border-color: rgba(255, 193, 7, 0.5);
}

.primary-glow-border-active {
    border-color: rgba(255, 215, 0, 0.5);
}

.danger-glow-border-active {
    border-color: rgba(234, 84, 85, 0.5);
}

.success-glow-text-active {
    color: rgba(40, 199, 111, 0.5);
}

.warning-glow-text-active {
    color: rgba(255, 193, 7, 0.5);
}

.primary-glow-text-active {
    color: rgba(255, 215, 0, 0.5);
}

.danger-glow-text-active {
    color: rgba(234, 84, 85, 0.5);
}

.success-glow-bg-active {
    background-color: rgba(40, 199, 111, 0.3);
}

.warning-glow-bg-active {
    background-color: rgba(255, 193, 7, 0.3);
}

.primary-glow-bg-active {
    background-color: rgba(255, 215, 0, 0.3);
}

.danger-glow-bg-active {
    background-color: rgba(234, 84, 85, 0.3);
}

.success-glow-border-focus {
    border-color: rgba(40, 199, 111, 0.6);
}

.warning-glow-border-focus {
    border-color: rgba(255, 193, 7, 0.6);
}

.primary-glow-border-focus {
    border-color: rgba(255, 215, 0, 0.6);
}

.danger-glow-border-focus {
    border-color: rgba(234, 84, 85, 0.6);
}

.success-glow-text-focus {
    color: rgba(40, 199, 111, 0.6);
}

.warning-glow-text-focus {
    color: rgba(255, 193, 7, 0.6);
}

.primary-glow-text-focus {
    color: rgba(255, 215, 0, 0.6);
}

.danger-glow-text-focus {
    color: rgba(234, 84, 85, 0.6);
}

.success-glow-bg-focus {
    background-color: rgba(40, 199, 111, 0.4);
}

.warning-glow-bg-focus {
    background-color: rgba(255, 193, 7, 0.4);
}

.primary-glow-bg-focus {
    background-color: rgba(255, 215, 0, 0.4);
}

.danger-glow-bg-focus {
    background-color: rgba(234, 84, 85, 0.4);
}

.success-glow-border-hover {
    border-color: rgba(40, 199, 111, 0.4);
}

.warning-glow-border-hover {
    border-color: rgba(255, 193, 7, 0.4);
}

.primary-glow-border-hover {
    border-color: rgba(255, 215, 0, 0.4);
}

.danger-glow-border-hover {
    border-color: rgba(234, 84, 85, 0.4);
}

.success-glow-text-hover {
    color: rgba(40, 199, 111, 0.4);
}

.warning-glow-text-hover {
}

/* Signals Section Styles */
.signals-ticker {
    margin: 40px 0;
    overflow: hidden;
}

.signal-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    min-height: 200px;
}

.signal-card:hover {
    transform: translateY(-5px);
}

.signal-type {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 14px;
}

.signal-type.buy {
    background-color: rgba(40, 199, 111, 0.2);
    color: #28c76f;
}

.signal-type.sell {
    background-color: rgba(234, 84, 85, 0.2);
    color: #ea5455;
}

.signal-card h4 {
    font-size: 1.25rem;
    margin-bottom: 15px;
    color: #2c3e50;
}

.signal-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.signal-details span {
    display: flex;
    justify-content: space-between;
    color: #6c757d;
    font-size: 0.9rem;
}

.signal-time {
    font-size: 0.85rem;
    color: #adb5bd;
    text-align: right;
}

/* Swiper Customization */
.swiper-container {
    padding: 20px 0;
}

.swiper-slide {
    width: 300px;
    margin-right: 30px;
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 18px;
}

.swiper-pagination-bullet {
    background: var(--primary-color);
}

.swiper-pagination-bullet-active {
    background: var(--primary-color);
}

@media (max-width: 768px) {
    .swiper-slide {
        width: 280px;
        margin-right: 20px;
    }
    
    .signal-card {
        padding: 15px;
    }
}

/* Stats Section Styles */
.stats-section {
    background: linear-gradient(135deg, var(--secondary-color), #000);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('img/crypto-pattern.png');
    opacity: 0.05;
    pointer-events: none;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.1);
}

.stat-icon {
    margin-bottom: 20px;
}

.stat-icon i {
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--primary-color), #FFA500);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 15px 0;
    background: linear-gradient(45deg, var(--primary-color), #FFA500);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-card p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin: 0;
}

@media (max-width: 768px) {
    .stat-card {
        padding: 20px 15px;
    }

    .stat-icon i {
        font-size: 2rem;
    }

    .stat-card h2 {
        font-size: 2rem;
    }

    .stat-card p {
        font-size: 1rem;
    }
}