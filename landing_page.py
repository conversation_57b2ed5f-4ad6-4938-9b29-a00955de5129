"""
CryptoSignals - Landing Page de Alta Conversão
Sistema SaaS Premium com Modo Escuro
"""

import sys
import os
from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for
import threading
import webbrowser
from datetime import datetime

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer

# Importar sistema SaaS
from saas_auth import SaaSAuthManager, PlanType
from saas_payments import TetherPaymentProcessor, TANOSIntegration

# Inicializar Flask
app = Flask(__name__)
app.secret_key = "cryptosignals_premium_2024"

# Inicializar sistemas
data_manager = CryptoDataManager()
auth_manager = SaaSAuthManager()
payment_processor = TetherPaymentProcessor("******************************************")
tanos_integration = TANOSIntegration()

# Template HTML da Landing Page
LANDING_PAGE_TEMPLATE = """
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals - Sinais Premium de Criptomoedas</title>
    <meta name="description" content="Plataforma premium de sinais de trading para criptomoedas. Análise técnica avançada, gráficos interativos e sinais de alta precisão.">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">

    <!-- Plotly for Charts -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <style>
        /* Dark Mode Override */
        :root {
            --primary-color: #FFD700;
            --secondary-color: #1a1a1a;
            --background-dark: #0a0a0a;
            --surface-dark: #1a1a1a;
            --text-light: #ffffff;
            --text-muted: #b0b0b0;
            --accent-gold: #FFD700;
            --gradient-gold: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
        }

        body {
            background: var(--background-dark);
            color: var(--text-light);
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            background: var(--gradient-dark);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-content {
            max-width: 800px;
            z-index: 2;
            position: relative;
        }

        .logo-container {
            margin-bottom: 40px;
        }

        .logo-container img {
            height: 80px;
            width: auto;
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.3));
        }

        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 800;
            margin-bottom: 20px;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: clamp(1.1rem, 3vw, 1.5rem);
            color: var(--text-muted);
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }

        .btn-primary {
            background: var(--gradient-gold);
            color: var(--secondary-color);
            padding: 18px 40px;
            border: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-light);
            padding: 18px 40px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--accent-gold);
            transform: translateY(-3px);
        }

        /* Stats Section */
        .stats-section {
            padding: 80px 20px;
            background: var(--surface-dark);
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
        }

        .stat-card {
            text-align: center;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
            border-color: var(--accent-gold);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 1.1rem;
            font-weight: 500;
        }

        /* Features Section */
        .features-section {
            padding: 100px 20px;
            background: var(--background-dark);
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: clamp(2rem, 5vw, 3rem);
            font-weight: 800;
            margin-bottom: 20px;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-subtitle {
            text-align: center;
            color: var(--text-muted);
            font-size: 1.2rem;
            margin-bottom: 60px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .feature-card {
            background: var(--surface-dark);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient-gold);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-color: var(--accent-gold);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-gold);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            font-size: 2rem;
            color: var(--secondary-color);
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--text-light);
        }

        .feature-description {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            color: var(--text-muted);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-list li::before {
            content: '✓';
            color: var(--accent-gold);
            font-weight: bold;
        }

        /* Pricing Section */
        .pricing-section {
            padding: 100px 20px;
            background: var(--surface-dark);
        }

        .pricing-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .pricing-card {
            background: var(--background-dark);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .pricing-card.featured {
            border-color: var(--accent-gold);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'MAIS POPULAR';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gradient-gold);
            color: var(--secondary-color);
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            border-color: var(--accent-gold);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .pricing-card.featured:hover {
            transform: translateY(-10px) scale(1.05);
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-light);
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 5px;
        }

        .plan-period {
            color: var(--text-muted);
            margin-bottom: 30px;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin-bottom: 40px;
        }

        .plan-features li {
            color: var(--text-muted);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .plan-features li::before {
            content: '✓';
            color: var(--accent-gold);
            font-weight: bold;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn-primary,
            .btn-secondary {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .pricing-grid {
                grid-template-columns: 1fr;
            }

            .pricing-card.featured {
                transform: none;
            }

            .pricing-card.featured:hover {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <div class="logo-container">
                <img src="/logo.png" alt="CryptoSignals Logo">
            </div>

            <h1 class="hero-title">CryptoSignals</h1>
            <p class="hero-subtitle">
                Sinais premium de trading para criptomoedas com análise técnica avançada,
                gráficos interativos e precisão de mercado profissional.
            </p>

            <div class="cta-buttons">
                <a href="#pricing" class="btn-primary">
                    <i class="fas fa-rocket"></i>
                    Começar Agora
                </a>
                <a href="#features" class="btn-secondary">
                    <i class="fas fa-play"></i>
                    Ver Demo
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">95%</div>
                <div class="stat-label">Precisão dos Sinais</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10k+</div>
                <div class="stat-label">Traders Ativos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Monitoramento</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">50+</div>
                <div class="stat-label">Criptomoedas</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="features-container">
            <h2 class="section-title">Recursos Premium</h2>
            <p class="section-subtitle">
                Tecnologia de ponta para maximizar seus resultados no trading de criptomoedas
            </p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Análise Técnica Avançada</h3>
                    <p class="feature-description">
                        Algoritmos proprietários que analisam mais de 50 indicadores técnicos em tempo real.
                    </p>
                    <ul class="feature-list">
                        <li>RSI, MACD, Bollinger Bands</li>
                        <li>Padrões de candlestick</li>
                        <li>Suporte e resistência</li>
                        <li>Análise de volume</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3 class="feature-title">Sinais em Tempo Real</h3>
                    <p class="feature-description">
                        Receba alertas instantâneos de oportunidades de trading com alta probabilidade de sucesso.
                    </p>
                    <ul class="feature-list">
                        <li>Notificações push</li>
                        <li>Alertas por email</li>
                        <li>Integração Telegram</li>
                        <li>API para bots</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Dashboard Interativo</h3>
                    <p class="feature-description">
                        Interface moderna e intuitiva com gráficos interativos e métricas em tempo real.
                    </p>
                    <ul class="feature-list">
                        <li>Gráficos Plotly avançados</li>
                        <li>Modo escuro premium</li>
                        <li>Responsivo mobile</li>
                        <li>Customização completa</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section">
        <div class="pricing-container">
            <h2 class="section-title">Planos e Preços</h2>
            <p class="section-subtitle">
                Escolha o plano ideal para seu nível de trading
            </p>

            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3 class="plan-name">Starter</h3>
                    <div class="plan-price">$29</div>
                    <div class="plan-period">por mês</div>
                    <ul class="plan-features">
                        <li>Sinais básicos</li>
                        <li>5 alertas por dia</li>
                        <li>Suporte por email</li>
                        <li>Dashboard básico</li>
                    </ul>
                    <a href="#" class="btn-primary">Começar Trial</a>
                </div>

                <div class="pricing-card featured">
                    <h3 class="plan-name">Professional</h3>
                    <div class="plan-price">$79</div>
                    <div class="plan-period">por mês</div>
                    <ul class="plan-features">
                        <li>Sinais premium</li>
                        <li>Alertas ilimitados</li>
                        <li>Suporte prioritário</li>
                        <li>Dashboard completo</li>
                        <li>API access</li>
                        <li>Análise personalizada</li>
                    </ul>
                    <a href="#" class="btn-primary">Mais Popular</a>
                </div>

                <div class="pricing-card">
                    <h3 class="plan-name">Enterprise</h3>
                    <div class="plan-price">Custom</div>
                    <div class="plan-period">sob consulta</div>
                    <ul class="plan-features">
                        <li>Tudo do Professional</li>
                        <li>White-label</li>
                        <li>Integração customizada</li>
                        <li>Suporte dedicado</li>
                        <li>SLA garantido</li>
                    </ul>
                    <a href="#" class="btn-secondary">Contatar</a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Animate stats on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.stat-card, .feature-card, .pricing-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
"""

@app.route('/')
def landing_page():
    """Página principal - Landing Page"""
    return render_template_string(LANDING_PAGE_TEMPLATE)

@app.route('/logo.png')
def serve_logo():
    """Serve o logo"""
    from flask import send_file
    try:
        return send_file('logo.png', mimetype='image/png')
    except FileNotFoundError:
        return "", 404

@app.route('/static/css/custom.css')
def serve_custom_css():
    """Serve o CSS customizado"""
    try:
        with open('dashboard/assets/custom.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        return css_content, 200, {'Content-Type': 'text/css'}
    except FileNotFoundError:
        return "", 404

if __name__ == "__main__":
    print("🚀 Iniciando CryptoSignals - Landing Page Premium...")
    print("🌐 Acesse: http://localhost:5000")
    print("🎨 Modo escuro ativado por padrão")
    print("💎 Landing page de alta conversão pronta!")

    # Abrir navegador automaticamente
    threading.Timer(1.5, lambda: webbrowser.open('http://localhost:5000')).start()

    app.run(debug=True, host='0.0.0.0', port=5000)
