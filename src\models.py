"""
Módulo para modelos de previsão de preços do Bitcoin.
Implementa modelos de séries temporais e machine learning.
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
import warnings
warnings.filterwarnings('ignore')
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta


class BitcoinPredictor:
    """Classe para previsão de preços do Bitcoin."""
    
    def __init__(self, df: pd.DataFrame):
        """
        Inicializa o preditor.
        
        Args:
            df: DataFrame com dados históricos
        """
        self.df = df.copy()
        self.models = {}
        self.scalers = {}
        self.predictions = {}
        
    def prepare_features(self, target_col: str = 'Close') -> <PERSON><PERSON>[pd.DataFrame, pd.Series]:
        """
        Prepara features para modelos de ML.
        
        Args:
            target_col: Coluna alvo para previsão
            
        Returns:
            Tupla com features (X) e target (y)
        """
        df = self.df.copy()
        
        # Criar features de lag
        for lag in [1, 2, 3, 6, 12, 24]:
            df[f'Close_lag_{lag}'] = df['Close'].shift(lag)
            df[f'Volume_lag_{lag}'] = df['Volume'].shift(lag)
        
        # Features de médias móveis
        for window in [5, 10, 20]:
            df[f'MA_{window}'] = df['Close'].rolling(window=window).mean()
            df[f'MA_{window}_ratio'] = df['Close'] / df[f'MA_{window}']
        
        # Features de volatilidade
        df['Returns'] = df['Close'].pct_change()
        df['Volatility_5'] = df['Returns'].rolling(window=5).std()
        df['Volatility_20'] = df['Returns'].rolling(window=20).std()
        
        # Features de volume
        df['Volume_MA_5'] = df['Volume'].rolling(window=5).mean()
        df['Volume_ratio'] = df['Volume'] / df['Volume_MA_5']
        
        # Features de tempo
        df['Hour'] = df.index.hour
        df['DayOfWeek'] = df.index.dayofweek
        df['Month'] = df.index.month
        
        # Features de preço
        df['High_Low_ratio'] = df['High'] / df['Low']
        df['Open_Close_ratio'] = df['Open'] / df['Close']
        
        # Selecionar features numéricas
        feature_cols = [col for col in df.columns if col not in [target_col, 'Open', 'High', 'Low']]
        feature_cols = [col for col in feature_cols if df[col].dtype in ['float64', 'int64']]
        
        # Remover linhas com NaN
        df_clean = df.dropna()
        
        X = df_clean[feature_cols]
        y = df_clean[target_col]
        
        return X, y
    
    def train_random_forest(self, test_size: float = 0.2) -> Dict:
        """
        Treina modelo Random Forest.
        
        Args:
            test_size: Proporção dos dados para teste
            
        Returns:
            Dicionário com métricas do modelo
        """
        X, y = self.prepare_features()
        
        # Dividir dados
        split_idx = int(len(X) * (1 - test_size))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Normalizar features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Treinar modelo
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        model.fit(X_train_scaled, y_train)
        
        # Fazer previsões
        y_pred_train = model.predict(X_train_scaled)
        y_pred_test = model.predict(X_test_scaled)
        
        # Calcular métricas
        metrics = {
            'train_mae': mean_absolute_error(y_train, y_pred_train),
            'test_mae': mean_absolute_error(y_test, y_pred_test),
            'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
            'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'train_r2': r2_score(y_train, y_pred_train),
            'test_r2': r2_score(y_test, y_pred_test)
        }
        
        # Salvar modelo e scaler
        self.models['random_forest'] = model
        self.scalers['random_forest'] = scaler
        
        # Salvar previsões
        self.predictions['random_forest'] = {
            'train_pred': y_pred_train,
            'test_pred': y_pred_test,
            'train_actual': y_train,
            'test_actual': y_test,
            'test_dates': X_test.index
        }
        
        return metrics
    
    def train_linear_regression(self, test_size: float = 0.2) -> Dict:
        """
        Treina modelo de Regressão Linear.
        
        Args:
            test_size: Proporção dos dados para teste
            
        Returns:
            Dicionário com métricas do modelo
        """
        X, y = self.prepare_features()
        
        # Dividir dados
        split_idx = int(len(X) * (1 - test_size))
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        # Normalizar features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Treinar modelo
        model = LinearRegression()
        model.fit(X_train_scaled, y_train)
        
        # Fazer previsões
        y_pred_train = model.predict(X_train_scaled)
        y_pred_test = model.predict(X_test_scaled)
        
        # Calcular métricas
        metrics = {
            'train_mae': mean_absolute_error(y_train, y_pred_train),
            'test_mae': mean_absolute_error(y_test, y_pred_test),
            'train_rmse': np.sqrt(mean_squared_error(y_train, y_pred_train)),
            'test_rmse': np.sqrt(mean_squared_error(y_test, y_pred_test)),
            'train_r2': r2_score(y_train, y_pred_train),
            'test_r2': r2_score(y_test, y_pred_test)
        }
        
        # Salvar modelo e scaler
        self.models['linear_regression'] = model
        self.scalers['linear_regression'] = scaler
        
        # Salvar previsões
        self.predictions['linear_regression'] = {
            'train_pred': y_pred_train,
            'test_pred': y_pred_test,
            'train_actual': y_train,
            'test_actual': y_test,
            'test_dates': X_test.index
        }
        
        return metrics
    
    def train_arima(self, order: Tuple[int, int, int] = (1, 1, 1)) -> Dict:
        """
        Treina modelo ARIMA.
        
        Args:
            order: Ordem do modelo ARIMA (p, d, q)
            
        Returns:
            Dicionário com métricas do modelo
        """
        # Usar apenas preços de fechamento
        prices = self.df['Close'].dropna()
        
        # Dividir dados (80% treino, 20% teste)
        split_idx = int(len(prices) * 0.8)
        train_data = prices[:split_idx]
        test_data = prices[split_idx:]
        
        try:
            # Treinar modelo ARIMA
            model = ARIMA(train_data, order=order)
            fitted_model = model.fit()
            
            # Fazer previsões
            forecast_steps = len(test_data)
            forecast = fitted_model.forecast(steps=forecast_steps)
            
            # Calcular métricas
            mae = mean_absolute_error(test_data, forecast)
            rmse = np.sqrt(mean_squared_error(test_data, forecast))
            r2 = r2_score(test_data, forecast)
            
            metrics = {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic
            }
            
            # Salvar modelo
            self.models['arima'] = fitted_model
            
            # Salvar previsões
            self.predictions['arima'] = {
                'forecast': forecast,
                'actual': test_data,
                'test_dates': test_data.index
            }
            
            return metrics
            
        except Exception as e:
            print(f"Erro ao treinar ARIMA: {e}")
            return {'error': str(e)}
    
    def predict_next_prices(self, model_name: str, steps: int = 24) -> pd.Series:
        """
        Prevê próximos preços usando modelo treinado.
        
        Args:
            model_name: Nome do modelo a usar
            steps: Número de períodos para prever
            
        Returns:
            Série com previsões
        """
        if model_name not in self.models:
            raise ValueError(f"Modelo {model_name} não foi treinado")
        
        if model_name == 'arima':
            # Previsão ARIMA
            forecast = self.models[model_name].forecast(steps=steps)
            
            # Criar índice de datas futuras
            last_date = self.df.index[-1]
            future_dates = pd.date_range(
                start=last_date + timedelta(hours=1),
                periods=steps,
                freq='H'
            )
            
            return pd.Series(forecast, index=future_dates)
        
        else:
            # Previsão ML
            # Para modelos ML, usar últimas observações como features
            X, _ = self.prepare_features()
            last_features = X.iloc[-1:].values
            
            # Normalizar
            scaler = self.scalers[model_name]
            last_features_scaled = scaler.transform(last_features)
            
            # Prever próximo preço
            next_price = self.models[model_name].predict(last_features_scaled)[0]
            
            # Para múltiplos steps, usar previsão iterativa (simplificado)
            predictions = [next_price]
            for _ in range(steps - 1):
                predictions.append(predictions[-1] * (1 + np.random.normal(0, 0.01)))
            
            # Criar índice de datas futuras
            last_date = self.df.index[-1]
            future_dates = pd.date_range(
                start=last_date + timedelta(hours=1),
                periods=steps,
                freq='H'
            )
            
            return pd.Series(predictions, index=future_dates)
    
    def get_feature_importance(self, model_name: str = 'random_forest') -> pd.Series:
        """
        Retorna importância das features para modelos ML.
        
        Args:
            model_name: Nome do modelo
            
        Returns:
            Série com importância das features
        """
        if model_name not in self.models:
            raise ValueError(f"Modelo {model_name} não foi treinado")
        
        model = self.models[model_name]
        
        if hasattr(model, 'feature_importances_'):
            X, _ = self.prepare_features()
            importance = pd.Series(
                model.feature_importances_,
                index=X.columns
            ).sort_values(ascending=False)
            
            return importance
        else:
            raise ValueError(f"Modelo {model_name} não suporta importância de features")
    
    def compare_models(self) -> pd.DataFrame:
        """
        Compara performance de todos os modelos treinados.
        
        Returns:
            DataFrame com métricas de comparação
        """
        comparison_data = []
        
        for model_name, pred_data in self.predictions.items():
            if model_name == 'arima':
                mae = mean_absolute_error(pred_data['actual'], pred_data['forecast'])
                rmse = np.sqrt(mean_squared_error(pred_data['actual'], pred_data['forecast']))
                r2 = r2_score(pred_data['actual'], pred_data['forecast'])
                
                comparison_data.append({
                    'Model': model_name.upper(),
                    'MAE': mae,
                    'RMSE': rmse,
                    'R²': r2
                })
            else:
                mae = mean_absolute_error(pred_data['test_actual'], pred_data['test_pred'])
                rmse = np.sqrt(mean_squared_error(pred_data['test_actual'], pred_data['test_pred']))
                r2 = r2_score(pred_data['test_actual'], pred_data['test_pred'])
                
                comparison_data.append({
                    'Model': model_name.replace('_', ' ').title(),
                    'MAE': mae,
                    'RMSE': rmse,
                    'R²': r2
                })
        
        return pd.DataFrame(comparison_data).sort_values('MAE')


def train_all_models(df: pd.DataFrame) -> Tuple[BitcoinPredictor, pd.DataFrame]:
    """
    Treina todos os modelos disponíveis.
    
    Args:
        df: DataFrame com dados históricos
        
    Returns:
        Tupla com predictor e comparação de modelos
    """
    predictor = BitcoinPredictor(df)
    
    print("Treinando Random Forest...")
    rf_metrics = predictor.train_random_forest()
    
    print("Treinando Regressão Linear...")
    lr_metrics = predictor.train_linear_regression()
    
    print("Treinando ARIMA...")
    arima_metrics = predictor.train_arima()
    
    # Comparar modelos
    comparison = predictor.compare_models()
    
    return predictor, comparison


if __name__ == "__main__":
    # Teste do módulo
    from data_processing import load_bitcoin_data
    
    # Carregar dados
    data = load_bitcoin_data()
    
    # Treinar modelos
    predictor, comparison = train_all_models(data)
    
    print("\nComparação de Modelos:")
    print(comparison)
    
    # Fazer previsões
    print("\nPrevisões para as próximas 24 horas:")
    for model_name in predictor.models.keys():
        try:
            predictions = predictor.predict_next_prices(model_name, steps=24)
            print(f"{model_name}: ${predictions.iloc[-1]:.2f}")
        except Exception as e:
            print(f"Erro em {model_name}: {e}")
