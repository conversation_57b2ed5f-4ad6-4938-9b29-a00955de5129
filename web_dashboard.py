"""
CryptoSignals - Dashboard SaaS Premium
Sistema completo de análise de criptomoedas com planos de assinatura
Pagamentos em TETHER USD via TANOS
"""

import sys
import os
import json
from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for
import threading
import webbrowser
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.utils
from functools import wraps

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer
from visualization import BitcoinVisualizer

# Importar sistema SaaS
from saas_auth import SaaSAuthManager, PlanType
from saas_payments import TetherPaymentProcessor, TANOSIntegration

# Inicializar Flask
app = Flask(__name__)
app.secret_key = "crypto_analytics_pro_secret_key_2024"  # Mudar em produção

# Inicializar sistemas
data_manager = CryptoDataManager()
auth_manager = SaaSAuthManager()
payment_processor = TetherPaymentProcessor("******************************************")
tanos_integration = TANOSIntegration()

# Cache global
cache = {
    'current_symbol': 'BTC',
    'current_period': '3m',
    'current_data': None,
    'current_analysis': None,
    'last_update': None
}

# Decorador para verificar autenticação
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'success': False, 'error': 'Login required', 'redirect': '/login'})
        return f(*args, **kwargs)
    return decorated_function

# Decorador para verificar limites do plano
def check_plan_limits(action):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return jsonify({'success': False, 'error': 'Login required'})

            user = auth_manager.get_user_by_id(session['user_id'])
            if not user:
                return jsonify({'success': False, 'error': 'User not found'})

            if not auth_manager.check_user_limits(user, action):
                return jsonify({
                    'success': False,
                    'error': 'Plan limit exceeded',
                    'upgrade_required': True
                })

            # Log do uso da API
            auth_manager.log_api_usage(user.id, request.endpoint, request.remote_addr)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Template HTML
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>CryptoSignals - Dashboard SaaS</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        /* Reset e Variáveis - Paleta Corporativa */
        :root {
            /* Cores Principais */
            --primary-color: #D4A017;              /* Gold - elementos de destaque */
            --primary-gradient: linear-gradient(135deg, #D4A017 0%, #E6B84A 100%);
            --secondary-color: #0B1B33;            /* Navy Blue - gráficos e botões primários */
            --accent-color: #D4A017;               /* Gold para hover e chamadas à ação */

            /* Cores de Estado */
            --success-color: #10B981;              /* Verde para alta/compra */
            --warning-color: #F59E0B;              /* Amarelo para neutro */
            --danger-color: #EF4444;               /* Vermelho para baixa/venda */
            --neutral-color: #6B7280;              /* Cinza neutro */

            /* Cores de Fundo */
            --background-color: #F8F7F2;           /* Off-White - fundo principal */
            --background-gradient: linear-gradient(135deg, #F8F7F2 0%, #FFFFFF 100%);
            --card-color: #FFFFFF;                 /* Branco para cartões */
            --card-hover: #F8F7F2;                 /* Off-White para hover */

            /* Cores de Texto */
            --text-primary: #333333;               /* Dark Charcoal - texto principal */
            --text-secondary: #666666;             /* Cinza médio para texto secundário */
            --text-muted: #999999;                 /* Cinza claro para texto auxiliar */
            --text-navy: #0B1B33;                  /* Navy Blue para títulos importantes */

            /* Cores de Interface */
            --border-color: #E0E0E0;               /* Light Slate - bordas e separadores */
            --hover-color: #E0E0E0;                /* Light Slate para hover */

            /* Geometria */
            --border-radius: 12px;
            --border-radius-sm: 6px;
            --border-radius-lg: 20px;

            /* Sombras */
            --shadow-sm: 0 1px 3px rgba(11, 27, 51, 0.1), 0 1px 2px rgba(11, 27, 51, 0.06);
            --shadow-md: 0 4px 6px rgba(11, 27, 51, 0.07), 0 2px 4px rgba(11, 27, 51, 0.06);
            --shadow-lg: 0 10px 15px rgba(11, 27, 51, 0.1), 0 4px 6px rgba(11, 27, 51, 0.05);
            --shadow-xl: 0 20px 25px rgba(11, 27, 51, 0.1), 0 10px 10px rgba(11, 27, 51, 0.04);

            /* Transições */
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: var(--background-color);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Layout e Componentes Base */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header e Navigation */
        .header {
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(248, 247, 242, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: var(--shadow-sm);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .logo {
            height: 44px;
            width: auto;
        }

        .logo-text {
            font-size: 26px;
            font-weight: 700;
            color: var(--text-navy);
            position: relative;
        }

        .logo-text::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .plan-badge {
            background: var(--primary-gradient);
            color: white;
            padding: 4px 12px;
            border-radius: var(--border-radius-sm);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            margin-right: 12px;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(11, 27, 51, 0.8);
            backdrop-filter: blur(8px);
        }

        .modal-content {
            background: var(--card-color);
            margin: 5% auto;
            padding: 32px;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 500px;
            box-shadow: var(--shadow-xl);
            position: relative;
        }

        .modal-header {
            margin-bottom: 24px;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-navy);
            margin-bottom: 8px;
        }

        .modal-subtitle {
            color: var(--text-secondary);
            font-size: 14px;
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            color: var(--text-secondary);
            cursor: pointer;
            transition: var(--transition-fast);
        }

        .close:hover {
            color: var(--text-primary);
        }

        /* Main Content */
        .main {
            padding: 40px 0;
        }

        /* Estilos para Gráficos */
        .chart-container {
            background: var(--card-color);
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }

        .chart-container:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-navy);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 20px;
            font-weight: 400;
        }

        .plotly-chart {
            width: 100%;
            height: auto;
        }

        /* Controls Section */
        .controls-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-navy);
            position: relative;
            padding-bottom: 8px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        .controls-card {
            background: var(--card-color);
            border-radius: var(--border-radius);
            padding: 32px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .controls-card:hover {
            box-shadow: var(--shadow-lg);
        }

        .control-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 24px;
        }

        .control-group:last-child {
            margin-bottom: 0;
        }

        .input-group {
            flex: 1;
            min-width: 200px;
        }

        .input-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        input, select {
            width: 100%;
            padding: 14px 18px;
            font-size: 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            background: var(--card-color);
            color: var(--text-primary);
            transition: var(--transition-fast);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            font-weight: 500;
        }

        select {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 18px center;
            padding-right: 50px;
        }

        input:focus, select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        input:hover, select:hover {
            border-color: var(--primary-color);
        }

        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .btn {
            padding: 14px 24px;
            font-size: 16px;
            font-weight: 600;
            border-radius: var(--border-radius-sm);
            border: none;
            cursor: pointer;
            transition: var(--transition-fast);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition);
        }

        .btn-primary:hover::before {
            opacity: 1;
        }

        .btn-primary span {
            position: relative;
            z-index: 1;
        }

        .btn-secondary {
            background: var(--card-color);
            color: var(--text-navy);
            border: 2px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary:hover {
            background: var(--hover-color);
            border-color: var(--primary-color);
            color: var(--text-navy);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover:before {
            left: 100%;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 24px;
            margin-top: 40px;
        }

        .card {
            background: var(--card-color);
            border-radius: var(--border-radius);
            padding: 28px;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            overflow: hidden;
            border: 1px solid var(--border-color);
            position: relative;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition);
        }

        .card:hover:before {
            opacity: 1;
        }

        .card-sm {
            grid-column: span 3;
        }

        .card-md {
            grid-column: span 6;
        }

        .card-lg {
            grid-column: span 12;
        }

        .card-header {
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-navy);
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 400;
        }

        /* Elementos Específicos */
        .price-display {
            font-size: 52px;
            font-weight: 800;
            text-align: center;
            margin: 32px 0;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            position: relative;
            letter-spacing: -1px;
        }

        .price-display:after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(212, 160, 23, 0.3);
        }

        .trend {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            padding: 12px;
            border-radius: var(--border-radius);
            margin: 16px 0;
        }

        .trend.ALTA {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .trend.BAIXA {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .trend.LATERAL {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .signals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
        }

        .signal {
            padding: 16px;
            border-radius: var(--border-radius);
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            transition: var(--transition);
        }

        .signal.COMPRA {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(52, 199, 89, 0.2);
        }

        .signal.VENDA {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(255, 59, 48, 0.2);
        }

        .signal.NEUTRO {
            background-color: rgba(142, 142, 147, 0.1);
            color: var(--neutral-color);
            border: 1px solid rgba(142, 142, 147, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 16px;
        }

        .stat-card {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 6px;
            color: var(--primary-color);
            text-shadow: 0 1px 2px rgba(212, 160, 23, 0.2);
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Estados e Feedback */
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 80px 0;
            text-align: center;
            color: var(--text-secondary);
        }

        .loading-spinner {
            width: 44px;
            height: 44px;
            border: 4px solid rgba(212, 160, 23, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(212, 160, 23, 0.2);
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .alert {
            padding: 16px;
            border-radius: var(--border-radius);
            margin: 16px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-error {
            background-color: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
            border: 1px solid rgba(255, 59, 48, 0.2);
        }

        .alert-success {
            background-color: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
            border: 1px solid rgba(52, 199, 89, 0.2);
        }

        /* Histórico e Resultados de Busca */
        .history-item, .search-result-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition);
        }

        .search-result-item {
            cursor: pointer;
        }

        .search-result-item:hover {
            border-color: var(--primary-color);
            box-shadow: var(--shadow-sm);
        }

        .history-container {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 8px;
        }

        .history-container::-webkit-scrollbar {
            width: 8px;
        }

        .history-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
        }

        .history-container::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        .history-container::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        /* Responsividade */
        @media (max-width: 1024px) {
            .card-sm {
                grid-column: span 6;
            }
        }

        @media (max-width: 768px) {
            .card-sm, .card-md {
                grid-column: span 12;
            }

            .control-group {
                flex-direction: column;
            }

            .price-display {
                font-size: 32px;
            }

            .input-group {
                width: 100%;
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <img src="/logo.png" alt="CryptoSignals Logo" class="logo">
                    <span class="logo-text">CryptoSignals</span>
                </div>
                <div class="header-actions">
                    <div id="user-info" style="display: none;">
                        <span id="user-email"></span>
                        <span id="user-plan" class="plan-badge"></span>
                        <button class="btn btn-secondary" onclick="logout()">Sair</button>
                    </div>
                    <div id="auth-buttons">
                        <button class="btn btn-secondary" onclick="showLogin()">Login</button>
                        <button class="btn btn-primary" onclick="showRegister()">
                            <span>Cadastrar</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <section class="controls-section">
                <h2 class="section-title">Configurações de Análise</h2>
                <div class="controls-card">
                    <div class="control-group">
                        <div class="input-group">
                            <label class="input-label" for="symbol">Criptomoeda</label>
                            <input type="text" id="symbol" value="BTC" placeholder="Ex: BTC, ETH, ADA">
                        </div>
                        <div class="input-group">
                            <label class="input-label" for="popular-symbols">Seleção Rápida</label>
                            <select id="popular-symbols" onchange="selectPopular()">
                                <option value="">Selecione uma criptomoeda</option>
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="BNB">Binance Coin (BNB)</option>
                                <option value="ADA">Cardano (ADA)</option>
                                <option value="SOL">Solana (SOL)</option>
                                <option value="DOT">Polkadot (DOT)</option>
                                <option value="MATIC">Polygon (MATIC)</option>
                                <option value="LINK">Chainlink (LINK)</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <label class="input-label" for="period">Período de Análise</label>
                            <select id="period">
                                <option value="1d">1 Dia</option>
                                <option value="1w">1 Semana</option>
                                <option value="1m">1 Mês</option>
                                <option value="3m" selected>3 Meses</option>
                                <option value="6m">6 Meses</option>
                                <option value="1y">1 Ano</option>
                                <option value="2y">2 Anos</option>
                            </select>
                        </div>
                    </div>

                    <div class="control-group">
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="analyzeToken()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                                <span>Analisar</span>
                            </button>
                            <button class="btn btn-primary" onclick="searchSymbol()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                                <span>Buscar</span>
                            </button>
                            <button class="btn btn-secondary" onclick="saveAnalysis()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17 21 17 13 7 13 7 21"></polyline><polyline points="7 3 7 8 15 8"></polyline></svg>
                                <span>Salvar</span>
                            </button>
                            <button class="btn btn-secondary" onclick="showHistory()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                                <span>Histórico</span>
                            </button>
                            <button class="btn btn-secondary" onclick="autoRefresh()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M23 4v6h-6"></path><path d="M1 20v-6h6"></path><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path><path d="M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg>
                                <span>Auto-Refresh</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <section id="content">
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>Carregando dados iniciais...</p>
                    <p>Digite um símbolo e clique em Analisar</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Modal de Login -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('loginModal')">&times;</span>
            <div class="modal-header">
                <h2 class="modal-title">🔐 Login</h2>
                <p class="modal-subtitle">Acesse sua conta CryptoSignals</p>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">E-mail</label>
                    <input type="email" id="loginEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Senha</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    <span>Entrar</span>
                </button>
            </form>
            <p style="text-align: center; margin-top: 16px; color: var(--text-secondary);">
                Não tem conta? <a href="#" onclick="showRegister(); closeModal('loginModal');" style="color: var(--primary-color);">Cadastre-se</a>
            </p>
        </div>
    </div>

    <!-- Modal de Registro -->
    <div id="registerModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('registerModal')">&times;</span>
            <div class="modal-header">
                <h2 class="modal-title">🚀 Cadastro</h2>
                <p class="modal-subtitle">Crie sua conta e comece com 14 dias grátis</p>
            </div>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerEmail">E-mail</label>
                    <input type="email" id="registerEmail" name="email" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Senha</label>
                    <input type="password" id="registerPassword" name="password" required>
                </div>
                <div class="form-group">
                    <label for="registerPlan">Plano Inicial</label>
                    <select id="registerPlan" name="plan">
                        <option value="free">Free - Grátis</option>
                        <option value="starter" selected>Starter - $29/mês (14 dias grátis)</option>
                        <option value="professional">Professional - $79/mês (14 dias grátis)</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    <span>Criar Conta</span>
                </button>
            </form>
            <p style="text-align: center; margin-top: 16px; color: var(--text-secondary);">
                Já tem conta? <a href="#" onclick="showLogin(); closeModal('registerModal');" style="color: var(--primary-color);">Faça login</a>
            </p>
        </div>
    </div>

    <!-- Modal de Planos -->
    <div id="plansModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <span class="close" onclick="closeModal('plansModal')">&times;</span>
            <div class="modal-header">
                <h2 class="modal-title">💎 Planos e Preços</h2>
                <p class="modal-subtitle">Escolha o plano ideal para suas necessidades</p>
            </div>
            <div id="plansContent">
                <!-- Conteúdo dos planos será carregado via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Modal de Pagamento -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('paymentModal')">&times;</span>
            <div class="modal-header">
                <h2 class="modal-title">💳 Pagamento USDT</h2>
                <p class="modal-subtitle">Pague com Tether USD na rede Ethereum</p>
            </div>
            <div id="paymentContent">
                <!-- Conteúdo do pagamento será carregado via JavaScript -->
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let currentUser = null;

        // Verificar se usuário está logado ao carregar a página
        window.onload = function() {
            checkAuthStatus();
        };

        // Funções de Autenticação
        function checkAuthStatus() {
            fetch('/auth/status')
                .then(response => response.json())
                .then(data => {
                    if (data.authenticated) {
                        currentUser = data.user;
                        showUserInfo(data.user);
                    } else {
                        showAuthButtons();
                    }
                })
                .catch(error => {
                    console.error('Erro ao verificar autenticação:', error);
                    showAuthButtons();
                });
        }

        function showUserInfo(user) {
            document.getElementById('auth-buttons').style.display = 'none';
            document.getElementById('user-info').style.display = 'flex';
            document.getElementById('user-email').textContent = user.email;
            document.getElementById('user-plan').textContent = user.plan.toUpperCase();
        }

        function showAuthButtons() {
            document.getElementById('user-info').style.display = 'none';
            document.getElementById('auth-buttons').style.display = 'flex';
        }

        function showLogin() {
            document.getElementById('loginModal').style.display = 'block';
        }

        function showRegister() {
            document.getElementById('registerModal').style.display = 'block';
        }

        function showPlans() {
            loadPlans();
            document.getElementById('plansModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Fechar modal clicando fora
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // Login
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                email: formData.get('email'),
                password: formData.get('password')
            };

            fetch('/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal('loginModal');
                    checkAuthStatus();
                    showMessage('Login realizado com sucesso!', 'success');
                } else {
                    showMessage(data.error || 'Erro no login', 'error');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                showMessage('Erro de conexão', 'error');
            });
        });

        // Registro
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                email: formData.get('email'),
                password: formData.get('password'),
                plan: formData.get('plan')
            };

            fetch('/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal('registerModal');
                    checkAuthStatus();
                    showMessage('Conta criada com sucesso!', 'success');

                    // Se não for plano free, mostrar opções de pagamento
                    if (data.plan !== 'free') {
                        setTimeout(() => {
                            showPaymentOptions(data.plan);
                        }, 1000);
                    }
                } else {
                    showMessage(data.error || 'Erro no cadastro', 'error');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                showMessage('Erro de conexão', 'error');
            });
        });

        function logout() {
            fetch('/auth/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                currentUser = null;
                showAuthButtons();
                showMessage('Logout realizado com sucesso!', 'success');
            })
            .catch(error => {
                console.error('Erro:', error);
            });
        }

        function selectPopular() {
            const select = document.getElementById('popular-symbols');
            const symbol = document.getElementById('symbol');
            if (select.value) {
                symbol.value = select.value;
                select.value = '';
                // Analisar automaticamente ao selecionar uma criptomoeda popular
                analyzeToken();
            }
        }

        function analyzeToken() {
            const symbol = document.getElementById('symbol').value.trim().toUpperCase();
            const period = document.getElementById('period').value;

            if (!symbol) {
                showAlert('Por favor, digite um símbolo para analisar', 'error');
                return;
            }

            document.getElementById('content').innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>Analisando ${symbol}...</p>
                </div>
            `;

            fetch('/analyze', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({symbol: symbol, period: period})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayAnalysis(data.analysis);
                } else {
                    showAlert(data.error, 'error');
                }
            })
            .catch(error => {
                showAlert(`Erro: ${error}`, 'error');
            });
        }

        function showAlert(message, type) {
            document.getElementById('content').innerHTML = `
                <div class="alert alert-${type}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        ${type === 'error' ?
                            '<circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line>' :
                            '<path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>'}
                    </svg>
                    <span>${message}</span>
                </div>
            `;
        }

        function displayAnalysis(analysis) {
            const summary = analysis.summary;
            const symbol = analysis.symbol;
            const charts = analysis.charts || {};

            const html = `
                <h2 class="section-title">Análise Completa de ${symbol}</h2>

                <!-- KPIs Section -->
                <div class="dashboard-grid">
                    <div class="card card-md">
                        <div class="card-header">
                            <h3 class="card-title">💰 Preço Atual</h3>
                            <div class="card-subtitle">Atualizado em tempo real</div>
                        </div>
                        <div class="price-display">$${summary.current_price.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</div>
                        <div class="trend ${summary.overall_trend}">
                            ${summary.overall_trend === 'ALTA' ? '📈' : summary.overall_trend === 'BAIXA' ? '📉' : '➡️'}
                            ${summary.overall_trend}
                        </div>
                    </div>

                    <div class="card card-md">
                        <div class="card-header">
                            <h3 class="card-title">📊 Indicadores Técnicos</h3>
                            <div class="card-subtitle">Resumo dos principais indicadores</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">${summary.rsi ? summary.rsi.toFixed(2) : 'N/A'}</div>
                                <div class="stat-label">RSI (14)</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${summary.signal_count.buy}</div>
                                <div class="stat-label">🟢 Compra</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${summary.signal_count.sell}</div>
                                <div class="stat-label">🔴 Venda</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${analysis.data_count.toLocaleString('pt-BR')}</div>
                                <div class="stat-label">📈 Registros</div>
                            </div>
                        </div>
                    </div>

                    <div class="card card-lg">
                        <div class="card-header">
                            <h3 class="card-title">🎯 Sinais de Trading</h3>
                            <div class="card-subtitle">Baseados em análise técnica avançada</div>
                        </div>
                        <div class="signals-grid">
                            ${Object.entries(summary.signals).map(([indicator, signal]) =>
                                `<div class="signal ${signal}">
                                    ${indicator}: ${signal}
                                    ${signal === 'COMPRA' ? '🟢' : signal === 'VENDA' ? '🔴' : '⚪'}
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                ${charts.candlestick ? `
                <div class="chart-container">
                    <div class="chart-title">
                        📈 Gráfico de Candlestick com Indicadores
                        <span style="font-size: 14px; font-weight: 400; color: var(--text-secondary);">
                            Análise de preços com médias móveis e bandas de Bollinger
                        </span>
                    </div>
                    <div id="candlestick-chart" class="plotly-chart"></div>
                </div>
                ` : ''}

                ${charts.volume ? `
                <div class="chart-container">
                    <div class="chart-title">
                        📊 Volume de Negociação
                        <span style="font-size: 14px; font-weight: 400; color: var(--text-secondary);">
                            Análise do volume de transações
                        </span>
                    </div>
                    <div id="volume-chart" class="plotly-chart"></div>
                </div>
                ` : ''}

                ${charts.indicators ? `
                <div class="chart-container">
                    <div class="chart-title">
                        🔍 Indicadores Técnicos Detalhados
                        <span style="font-size: 14px; font-weight: 400; color: var(--text-secondary);">
                            RSI, MACD e Oscilador Estocástico
                        </span>
                    </div>
                    <div id="indicators-chart" class="plotly-chart"></div>
                </div>
                ` : ''}

                ${charts.volatility ? `
                <div class="chart-container">
                    <div class="chart-title">
                        ⚡ Análise de Volatilidade
                        <span style="font-size: 14px; font-weight: 400; color: var(--text-secondary);">
                            Volatilidade histórica de 24 horas
                        </span>
                    </div>
                    <div id="volatility-chart" class="plotly-chart"></div>
                </div>
                ` : ''}
            `;

            document.getElementById('content').innerHTML = html;

            // Renderizar gráficos Plotly
            setTimeout(() => {
                if (charts.candlestick) {
                    Plotly.newPlot('candlestick-chart', charts.candlestick.data, charts.candlestick.layout, {responsive: true});
                }
                if (charts.volume) {
                    Plotly.newPlot('volume-chart', charts.volume.data, charts.volume.layout, {responsive: true});
                }
                if (charts.indicators) {
                    Plotly.newPlot('indicators-chart', charts.indicators.data, charts.indicators.layout, {responsive: true});
                }
                if (charts.volatility) {
                    Plotly.newPlot('volatility-chart', charts.volatility.data, charts.volatility.layout, {responsive: true});
                }
            }, 100);
        }

        function saveAnalysis() {
            fetch('/save', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Análise salva com sucesso!', 'success');
                    setTimeout(() => {
                        analyzeToken(); // Recarregar a tela principal após salvar
                    }, 2000);
                } else {
                    showAlert(data.error, 'error');
                }
            });
        }

        function showHistory() {
            document.getElementById('content').innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>Carregando histórico...</p>
                </div>
            `;

            fetch('/history')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = `
                        <div class="card card-lg">
                            <div class="card-header">
                                <h3 class="card-title">Histórico de Análises</h3>
                                <div class="card-subtitle">Análises salvas anteriormente</div>
                            </div>
                    `;

                    if (data.analyses.length === 0) {
                        html += '<p>Nenhuma análise salva encontrada.</p>';
                    } else {
                        html += '<div class="history-container">';
                        data.analyses.forEach(analysis => {
                            const date = new Date(analysis.created_at).toLocaleString('pt-BR');
                            html += `
                                <div class="history-item">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                        <strong style="font-size: 18px;">${analysis.symbol}</strong>
                                        <span style="color: var(--text-secondary); font-size: 14px;">${date}</span>
                                    </div>
                                    <div style="display: flex; gap: 16px; flex-wrap: wrap;">
                                        <div>
                                            <span style="color: var(--text-secondary); font-size: 14px;">Preço:</span>
                                            <strong>$${analysis.analysis_data.current_price ? analysis.analysis_data.current_price.toFixed(2) : 'N/A'}</strong>
                                        </div>
                                        <div>
                                            <span style="color: var(--text-secondary); font-size: 14px;">Tendência:</span>
                                            <span class="${analysis.analysis_data.overall_trend || 'NEUTRO'}" style="padding: 2px 8px; border-radius: 4px; font-size: 14px;">
                                                ${analysis.analysis_data.overall_trend || 'N/A'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    }
                    html += '</div>';
                    document.getElementById('content').innerHTML = html;
                }
            });
        }

        function autoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                showAlert('Auto-refresh desativado', 'success');
            } else {
                autoRefreshInterval = setInterval(analyzeToken, 300000); // 5 minutos
                showAlert('Auto-refresh ativado (5 minutos)', 'success');
            }
        }

        function searchSymbol() {
            const query = document.getElementById('symbol').value.trim();
            if (!query) {
                showAlert('Por favor, digite um termo para buscar', 'error');
                return;
            }

            document.getElementById('content').innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>Buscando "${query}"...</p>
                </div>
            `;

            fetch('/search', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({query: query})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.results.length > 0) {
                    let html = `
                        <div class="card card-lg">
                            <div class="card-header">
                                <h3 class="card-title">Resultados da Busca</h3>
                                <div class="card-subtitle">Clique em um resultado para analisar</div>
                            </div>
                    `;

                    data.results.forEach(result => {
                        html += `
                            <div class="search-result-item" onclick="document.getElementById('symbol').value='${result.symbol}'; analyzeToken();">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong style="font-size: 18px;">${result.symbol}</strong>
                                        ${result.name ? ' <span style="color: var(--text-secondary);">- ' + result.name + '</span>' : ''}
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                                </div>
                                ${result.variants ? '<div style="color: var(--text-secondary); font-size: 14px; margin-top: 4px;">Variantes: ' + result.variants.join(', ') + '</div>' : ''}
                            </div>
                        `;
                    });
                    html += '</div>';
                    document.getElementById('content').innerHTML = html;
                } else {
                    showAlert('Nenhum resultado encontrado para "' + query + '"', 'error');
                }
            });
        }

        // Carregar análise inicial
        window.onload = function() {
            analyzeToken();
        };
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Página principal."""
    return render_template_string(HTML_TEMPLATE)

# Rotas de Autenticação
@app.route('/auth/status')
def auth_status():
    """Verifica o status de autenticação do usuário"""
    if 'user_id' in session:
        user = auth_manager.get_user_by_id(session['user_id'])
        if user:
            return jsonify({
                'authenticated': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'plan': user.plan.value,
                    'subscription_end': user.subscription_end.isoformat()
                }
            })

    return jsonify({'authenticated': False})

@app.route('/auth/login', methods=['POST'])
def login():
    """Login do usuário"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')

        user = auth_manager.authenticate_user(email, password)
        if user:
            session['user_id'] = user.id
            return jsonify({
                'success': True,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'plan': user.plan.value
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Credenciais inválidas'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth/register', methods=['POST'])
def register():
    """Registro de novo usuário"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        plan = data.get('plan', 'free')

        # Converter string para enum
        plan_enum = PlanType(plan)

        user = auth_manager.create_user(email, password, plan_enum)
        if user:
            session['user_id'] = user.id
            return jsonify({
                'success': True,
                'plan': plan,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'plan': user.plan.value
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Erro ao criar usuário'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth/logout', methods=['POST'])
def logout():
    """Logout do usuário"""
    session.pop('user_id', None)
    return jsonify({'success': True})

# Rotas de Planos e Pagamentos
@app.route('/plans')
def get_plans():
    """Retorna informações dos planos"""
    plans_info = {}
    for plan_type, plan in auth_manager.plans.items():
        plans_info[plan_type.value] = {
            'name': plan.name,
            'price_monthly': plan.price_monthly,
            'price_annual': plan.price_annual,
            'features': plan.features,
            'limits': plan.limits
        }

    return jsonify(plans_info)

@app.route('/payment/create', methods=['POST'])
@login_required
def create_payment():
    """Cria uma solicitação de pagamento"""
    try:
        data = request.get_json()
        plan = data.get('plan')
        annual = data.get('annual', False)

        user = auth_manager.get_user_by_id(session['user_id'])
        if not user:
            return jsonify({'success': False, 'error': 'Usuário não encontrado'})

        # Obter informações do plano
        plan_enum = PlanType(plan)
        payment_info = auth_manager.get_payment_info(plan_enum, annual)

        # Criar solicitação de pagamento
        payment = payment_processor.create_payment_request(
            user.id, plan, payment_info['amount'], annual
        )

        return jsonify({
            'success': True,
            'payment': {
                'id': payment.id,
                'amount': payment.amount,
                'currency': payment.currency,
                'wallet_address': payment.wallet_address,
                'expires_at': payment.expires_at.isoformat()
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/payment/verify', methods=['POST'])
@login_required
def verify_payment():
    """Verifica um pagamento"""
    try:
        data = request.get_json()
        payment_id = data.get('payment_id')
        tx_hash = data.get('tx_hash')

        if payment_processor.verify_payment(payment_id, tx_hash):
            # Processar upgrade da assinatura
            payment = payment_processor.get_payment(payment_id)
            if payment and payment_processor.process_subscription_upgrade(
                payment.user_id, payment.plan, payment_id
            ):
                return jsonify({'success': True, 'message': 'Pagamento confirmado!'})

        return jsonify({'success': False, 'error': 'Pagamento não confirmado'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/analyze', methods=['POST'])
@check_plan_limits('api_call')
def analyze():
    """Analisa uma criptomoeda."""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'BTC').upper()
        period = data.get('period', '3m')

        # Buscar dados
        crypto_data = data_manager.fetch_crypto_data(symbol, period)

        if crypto_data is None or len(crypto_data) == 0:
            return jsonify({'success': False, 'error': 'Não foi possível obter dados'})

        # Análise técnica
        analyzer = TechnicalAnalyzer(crypto_data)
        indicators_df = analyzer.calculate_all_indicators()
        summary = analyzer.get_market_summary()

        # Criar visualizações
        visualizer = BitcoinVisualizer(indicators_df)

        # Gerar gráficos
        charts = {}
        try:
            # Gráfico principal de candlestick
            candlestick_fig = visualizer.create_candlestick_chart(analyzer.indicators, height=500)
            charts['candlestick'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(candlestick_fig))

            # Gráfico de volume
            volume_fig = visualizer.create_volume_chart(height=200)
            charts['volume'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(volume_fig))

            # Gráfico de indicadores
            indicators_fig = visualizer.create_indicators_subplot(analyzer.indicators)
            charts['indicators'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(indicators_fig))

            # Gráfico de volatilidade
            volatility_fig = visualizer.create_volatility_chart()
            charts['volatility'] = json.loads(plotly.utils.PlotlyJSONEncoder().encode(volatility_fig))

        except Exception as chart_error:
            print(f"Erro ao gerar gráficos: {chart_error}")
            charts = {}

        # Converter dados para formato serializável
        def convert_to_serializable(obj):
            """Converte objetos não serializáveis para JSON."""
            import numpy as np
            import pandas as pd

            if obj is None:
                return None
            elif isinstance(obj, (np.integer, np.floating)):
                return obj.item()
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, pd.Series):
                return obj.tolist()
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict('records')
            elif hasattr(obj, 'isoformat'):  # datetime
                return obj.isoformat()
            elif isinstance(obj, (int, float, str, bool, list, dict)):
                return obj
            else:
                try:
                    return float(obj)
                except (ValueError, TypeError):
                    return str(obj)

        # Converter summary para formato serializável
        serializable_summary = {}
        for key, value in summary.items():
            if isinstance(value, dict):
                serializable_summary[key] = {k: convert_to_serializable(v) for k, v in value.items()}
            else:
                serializable_summary[key] = convert_to_serializable(value)

        # Atualizar cache
        cache['current_symbol'] = symbol
        cache['current_period'] = period
        cache['current_data'] = crypto_data
        cache['current_analysis'] = {
            'symbol': symbol,
            'period': period,
            'summary': serializable_summary,
            'data_count': len(crypto_data),
            'timestamp': datetime.now().isoformat(),
            'charts': charts
        }
        cache['last_update'] = datetime.now()

        return jsonify({
            'success': True,
            'analysis': cache['current_analysis']
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/logo.png')
def logo():
    """Retorna o logo."""
    try:
        with open('logo.png', 'rb') as f:
            return f.read(), 200, {'Content-Type': 'image/png'}
    except Exception:
        return '', 404

@app.route('/save', methods=['POST'])
def save():
    """Salva análise atual."""
    try:
        if not cache.get('current_analysis'):
            return jsonify({'success': False, 'error': 'Nenhuma análise para salvar'})

        analysis = cache['current_analysis']

        # Garantir que os dados estão em formato serializável
        summary_to_save = analysis['summary'].copy()

        data_manager.save_analysis(
            analysis['symbol'],
            'technical_analysis',
            summary_to_save
        )

        return jsonify({'success': True, 'message': f'Análise de {analysis["symbol"]} salva com sucesso!'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/history')
def history():
    """Retorna histórico de análises."""
    try:
        analyses = data_manager.get_saved_analyses()
        return jsonify({'success': True, 'analyses': analyses})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/search', methods=['POST'])
def search():
    """Busca símbolos."""
    try:
        data = request.get_json()
        query = data.get('query', '')

        results = data_manager.search_symbols(query)
        return jsonify({'success': True, 'results': results})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

def run_server():
    """Executa o servidor Flask."""
    app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == "__main__":
    print("🚀 Iniciando CryptoSignals - Dashboard SaaS...")
    print("🌐 Acesse: http://localhost:5000")
    print("📊 Recursos: Gráficos interativos, análise técnica avançada e interface premium")

    # Iniciar servidor em thread separada
    server_thread = threading.Thread(target=run_server)
    server_thread.daemon = True
    server_thread.start()

    # Abrir navegador
    import time
    time.sleep(2)
    webbrowser.open('http://localhost:5000')

    try:
        # Manter aplicação rodando
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 Encerrando aplicação...")
