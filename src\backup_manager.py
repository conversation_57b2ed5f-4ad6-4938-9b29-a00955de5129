"""
Sistema de Backup Automático - CryptoSignals
Backup inteligente de dados críticos com versionamento
"""

import os
import shutil
import sqlite3
import json
import gzip
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import schedule

@dataclass
class BackupInfo:
    """Informações de um backup"""
    backup_id: str
    timestamp: datetime
    backup_type: str  # 'full', 'incremental', 'differential'
    file_path: str
    file_size: int
    checksum: str
    description: str
    retention_days: int

@dataclass
class BackupConfig:
    """Configuração de backup"""
    source_path: str
    backup_dir: str
    retention_days: int = 30
    max_backups: int = 50
    compress: bool = True
    encrypt: bool = False
    schedule_interval: str = "daily"  # 'hourly', 'daily', 'weekly'

class BackupManager:
    """Gerenciador de backups automáticos"""
    
    def __init__(self, config: BackupConfig):
        self.config = config
        self.backup_history: List[BackupInfo] = []
        self.backup_dir = Path(config.backup_dir)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Arquivo de metadados dos backups
        self.metadata_file = self.backup_dir / "backup_metadata.json"
        
        # Carregar histórico existente
        self._load_backup_history()
        
        # Configurar agendamento
        self._setup_schedule()
        
        # Iniciar thread de monitoramento
        self._start_monitoring_thread()
    
    def _load_backup_history(self):
        """Carrega histórico de backups"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.backup_history = [
                        BackupInfo(
                            backup_id=item['backup_id'],
                            timestamp=datetime.fromisoformat(item['timestamp']),
                            backup_type=item['backup_type'],
                            file_path=item['file_path'],
                            file_size=item['file_size'],
                            checksum=item['checksum'],
                            description=item['description'],
                            retention_days=item['retention_days']
                        )
                        for item in data
                    ]
            except Exception as e:
                print(f"Erro ao carregar histórico de backup: {e}")
                self.backup_history = []
    
    def _save_backup_history(self):
        """Salva histórico de backups"""
        try:
            data = [asdict(backup) for backup in self.backup_history]
            # Converter datetime para string
            for item in data:
                item['timestamp'] = item['timestamp'].isoformat()
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Erro ao salvar histórico de backup: {e}")
    
    def create_backup(self, backup_type: str = "full", description: str = "") -> Optional[BackupInfo]:
        """Cria um novo backup"""
        try:
            timestamp = datetime.now()
            backup_id = f"{backup_type}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            # Determinar arquivos para backup
            if backup_type == "full":
                files_to_backup = self._get_all_files()
            elif backup_type == "incremental":
                files_to_backup = self._get_incremental_files()
            else:  # differential
                files_to_backup = self._get_differential_files()
            
            if not files_to_backup:
                print("Nenhum arquivo para backup")
                return None
            
            # Criar arquivo de backup
            backup_filename = f"{backup_id}.tar.gz" if self.config.compress else f"{backup_id}.tar"
            backup_path = self.backup_dir / backup_filename
            
            # Criar backup
            if self.config.compress:
                self._create_compressed_backup(files_to_backup, backup_path)
            else:
                self._create_uncompressed_backup(files_to_backup, backup_path)
            
            # Calcular checksum
            checksum = self._calculate_checksum(backup_path)
            
            # Criar informações do backup
            backup_info = BackupInfo(
                backup_id=backup_id,
                timestamp=timestamp,
                backup_type=backup_type,
                file_path=str(backup_path),
                file_size=backup_path.stat().st_size,
                checksum=checksum,
                description=description or f"Backup {backup_type} automático",
                retention_days=self.config.retention_days
            )
            
            # Adicionar ao histórico
            self.backup_history.append(backup_info)
            self._save_backup_history()
            
            print(f"✅ Backup criado: {backup_id} ({backup_info.file_size / 1024 / 1024:.2f} MB)")
            
            # Limpar backups antigos
            self._cleanup_old_backups()
            
            return backup_info
            
        except Exception as e:
            print(f"❌ Erro ao criar backup: {e}")
            return None
    
    def _get_all_files(self) -> List[str]:
        """Retorna todos os arquivos para backup completo"""
        files = []
        source_path = Path(self.config.source_path)
        
        if source_path.is_file():
            files.append(str(source_path))
        elif source_path.is_dir():
            # Incluir arquivos importantes
            patterns = [
                "*.db", "*.sqlite", "*.json", "*.csv",
                "*.log", "*.conf", "*.config", "*.py"
            ]
            
            for pattern in patterns:
                files.extend([str(f) for f in source_path.rglob(pattern)])
        
        return files
    
    def _get_incremental_files(self) -> List[str]:
        """Retorna arquivos modificados desde último backup"""
        if not self.backup_history:
            return self._get_all_files()
        
        last_backup = max(self.backup_history, key=lambda x: x.timestamp)
        last_backup_time = last_backup.timestamp
        
        files = []
        source_path = Path(self.config.source_path)
        
        for file_path in self._get_all_files():
            file_stat = Path(file_path).stat()
            if datetime.fromtimestamp(file_stat.st_mtime) > last_backup_time:
                files.append(file_path)
        
        return files
    
    def _get_differential_files(self) -> List[str]:
        """Retorna arquivos modificados desde último backup completo"""
        # Encontrar último backup completo
        full_backups = [b for b in self.backup_history if b.backup_type == "full"]
        if not full_backups:
            return self._get_all_files()
        
        last_full_backup = max(full_backups, key=lambda x: x.timestamp)
        last_full_time = last_full_backup.timestamp
        
        files = []
        for file_path in self._get_all_files():
            file_stat = Path(file_path).stat()
            if datetime.fromtimestamp(file_stat.st_mtime) > last_full_time:
                files.append(file_path)
        
        return files
    
    def _create_compressed_backup(self, files: List[str], backup_path: Path):
        """Cria backup comprimido"""
        import tarfile
        
        with tarfile.open(backup_path, 'w:gz') as tar:
            for file_path in files:
                if Path(file_path).exists():
                    # Usar nome relativo no arquivo
                    arcname = Path(file_path).relative_to(Path(self.config.source_path).parent)
                    tar.add(file_path, arcname=str(arcname))
    
    def _create_uncompressed_backup(self, files: List[str], backup_path: Path):
        """Cria backup não comprimido"""
        import tarfile
        
        with tarfile.open(backup_path, 'w') as tar:
            for file_path in files:
                if Path(file_path).exists():
                    arcname = Path(file_path).relative_to(Path(self.config.source_path).parent)
                    tar.add(file_path, arcname=str(arcname))
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calcula checksum MD5 do arquivo"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def restore_backup(self, backup_id: str, restore_path: Optional[str] = None) -> bool:
        """Restaura um backup específico"""
        try:
            # Encontrar backup
            backup_info = next((b for b in self.backup_history if b.backup_id == backup_id), None)
            if not backup_info:
                print(f"❌ Backup {backup_id} não encontrado")
                return False
            
            backup_path = Path(backup_info.file_path)
            if not backup_path.exists():
                print(f"❌ Arquivo de backup não encontrado: {backup_path}")
                return False
            
            # Verificar integridade
            current_checksum = self._calculate_checksum(backup_path)
            if current_checksum != backup_info.checksum:
                print(f"❌ Checksum inválido para backup {backup_id}")
                return False
            
            # Determinar diretório de restauração
            target_dir = Path(restore_path) if restore_path else Path(self.config.source_path).parent
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Extrair backup
            import tarfile
            with tarfile.open(backup_path, 'r:*') as tar:
                tar.extractall(target_dir)
            
            print(f"✅ Backup {backup_id} restaurado em {target_dir}")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao restaurar backup: {e}")
            return False
    
    def _cleanup_old_backups(self):
        """Remove backups antigos baseado na política de retenção"""
        now = datetime.now()
        
        # Remover backups expirados
        expired_backups = [
            b for b in self.backup_history
            if (now - b.timestamp).days > b.retention_days
        ]
        
        for backup in expired_backups:
            try:
                backup_path = Path(backup.file_path)
                if backup_path.exists():
                    backup_path.unlink()
                self.backup_history.remove(backup)
                print(f"🗑️  Backup expirado removido: {backup.backup_id}")
            except Exception as e:
                print(f"Erro ao remover backup {backup.backup_id}: {e}")
        
        # Limitar número máximo de backups
        if len(self.backup_history) > self.config.max_backups:
            # Manter os mais recentes
            self.backup_history.sort(key=lambda x: x.timestamp, reverse=True)
            excess_backups = self.backup_history[self.config.max_backups:]
            
            for backup in excess_backups:
                try:
                    backup_path = Path(backup.file_path)
                    if backup_path.exists():
                        backup_path.unlink()
                    self.backup_history.remove(backup)
                    print(f"🗑️  Backup excedente removido: {backup.backup_id}")
                except Exception as e:
                    print(f"Erro ao remover backup {backup.backup_id}: {e}")
        
        # Salvar histórico atualizado
        self._save_backup_history()
    
    def _setup_schedule(self):
        """Configura agendamento automático"""
        if self.config.schedule_interval == "hourly":
            schedule.every().hour.do(self._scheduled_backup)
        elif self.config.schedule_interval == "daily":
            schedule.every().day.at("02:00").do(self._scheduled_backup)
        elif self.config.schedule_interval == "weekly":
            schedule.every().week.do(self._scheduled_backup)
    
    def _scheduled_backup(self):
        """Executa backup agendado"""
        # Determinar tipo de backup baseado no histórico
        recent_full_backups = [
            b for b in self.backup_history
            if b.backup_type == "full" and (datetime.now() - b.timestamp).days < 7
        ]
        
        if not recent_full_backups:
            backup_type = "full"
        else:
            backup_type = "incremental"
        
        self.create_backup(backup_type, f"Backup {backup_type} agendado")
    
    def _start_monitoring_thread(self):
        """Inicia thread de monitoramento de agendamentos"""
        def monitor():
            while True:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # Verificar a cada minuto
                except Exception as e:
                    print(f"Erro no monitoramento de backup: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Retorna status dos backups"""
        if not self.backup_history:
            return {
                'total_backups': 0,
                'last_backup': None,
                'total_size': 0,
                'status': 'no_backups'
            }
        
        total_size = sum(b.file_size for b in self.backup_history)
        last_backup = max(self.backup_history, key=lambda x: x.timestamp)
        
        # Verificar se backup está atualizado
        hours_since_last = (datetime.now() - last_backup.timestamp).total_seconds() / 3600
        
        if hours_since_last > 48:
            status = 'outdated'
        elif hours_since_last > 24:
            status = 'warning'
        else:
            status = 'healthy'
        
        return {
            'total_backups': len(self.backup_history),
            'last_backup': {
                'id': last_backup.backup_id,
                'timestamp': last_backup.timestamp.isoformat(),
                'type': last_backup.backup_type,
                'size_mb': last_backup.file_size / 1024 / 1024
            },
            'total_size_mb': total_size / 1024 / 1024,
            'status': status,
            'hours_since_last': hours_since_last
        }
    
    def list_backups(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Lista backups recentes"""
        recent_backups = sorted(self.backup_history, key=lambda x: x.timestamp, reverse=True)[:limit]
        
        return [
            {
                'id': backup.backup_id,
                'timestamp': backup.timestamp.isoformat(),
                'type': backup.backup_type,
                'size_mb': backup.file_size / 1024 / 1024,
                'description': backup.description,
                'checksum': backup.checksum[:8] + "..."
            }
            for backup in recent_backups
        ]

# Configuração padrão para CryptoSignals
default_config = BackupConfig(
    source_path=".",
    backup_dir="./backups",
    retention_days=30,
    max_backups=50,
    compress=True,
    schedule_interval="daily"
)

# Instância global do backup manager
backup_manager = BackupManager(default_config)
