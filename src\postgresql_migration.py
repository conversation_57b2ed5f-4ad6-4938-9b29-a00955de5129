"""
Sistema de Migração PostgreSQL - CryptoSignals
Migração completa de SQLite para PostgreSQL para produção
"""

import psycopg2
import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging
from contextlib import contextmanager

@dataclass
class MigrationConfig:
    """Configuração de migração"""
    sqlite_path: str
    postgres_host: str
    postgres_port: int
    postgres_db: str
    postgres_user: str
    postgres_password: str
    batch_size: int = 1000
    backup_before_migration: bool = True

@dataclass
class TableMapping:
    """Mapeamento de tabela"""
    sqlite_table: str
    postgres_table: str
    columns: Dict[str, str]  # sqlite_col: postgres_col
    transformations: Dict[str, callable] = None  # Transformações de dados
    
    def __post_init__(self):
        if self.transformations is None:
            self.transformations = {}

class PostgreSQLMigrator:
    """Migrador principal para PostgreSQL"""
    
    def __init__(self, config: MigrationConfig):
        self.config = config
        self.logger = self._setup_logging()
        self.table_mappings = self._define_table_mappings()
        
    def _setup_logging(self) -> logging.Logger:
        """Configura logging"""
        logger = logging.getLogger('postgresql_migrator')
        logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def _define_table_mappings(self) -> List[TableMapping]:
        """Define mapeamentos de tabelas"""
        return [
            # Tabela de usuários
            TableMapping(
                sqlite_table="users",
                postgres_table="users",
                columns={
                    "id": "id",
                    "email": "email",
                    "password_hash": "password_hash",
                    "plan": "plan",
                    "created_at": "created_at",
                    "is_active": "is_active",
                    "email_verified": "email_verified"
                }
            ),
            
            # Tabela de pagamentos
            TableMapping(
                sqlite_table="payments",
                postgres_table="payments",
                columns={
                    "id": "id",
                    "user_id": "user_id",
                    "amount": "amount",
                    "currency": "currency",
                    "status": "status",
                    "created_at": "created_at",
                    "completed_at": "completed_at"
                }
            ),
            
            # Tabela de análises
            TableMapping(
                sqlite_table="analysis_history",
                postgres_table="analysis_history",
                columns={
                    "id": "id",
                    "user_id": "user_id",
                    "symbol": "symbol",
                    "analysis_data": "analysis_data",
                    "created_at": "created_at"
                },
                transformations={
                    "analysis_data": lambda x: json.loads(x) if isinstance(x, str) else x
                }
            ),
            
            # Tabela de afiliados
            TableMapping(
                sqlite_table="affiliates",
                postgres_table="affiliates",
                columns={
                    "id": "id",
                    "user_id": "user_id",
                    "affiliate_code": "affiliate_code",
                    "status": "status",
                    "commission_rate": "commission_rate",
                    "total_referrals": "total_referrals",
                    "total_earnings": "total_earnings",
                    "pending_earnings": "pending_earnings",
                    "paid_earnings": "paid_earnings",
                    "created_at": "created_at"
                }
            ),
            
            # Tabela de eventos de segurança
            TableMapping(
                sqlite_table="security_events",
                postgres_table="security_events",
                columns={
                    "id": "id",
                    "user_id": "user_id",
                    "event_type": "event_type",
                    "ip_address": "ip_address",
                    "user_agent": "user_agent",
                    "details": "details",
                    "risk_score": "risk_score",
                    "blocked": "blocked",
                    "timestamp": "timestamp"
                },
                transformations={
                    "details": lambda x: json.loads(x) if isinstance(x, str) else x
                }
            )
        ]
    
    @contextmanager
    def get_sqlite_connection(self):
        """Context manager para conexão SQLite"""
        conn = sqlite3.connect(self.config.sqlite_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    @contextmanager
    def get_postgres_connection(self):
        """Context manager para conexão PostgreSQL"""
        conn = psycopg2.connect(
            host=self.config.postgres_host,
            port=self.config.postgres_port,
            database=self.config.postgres_db,
            user=self.config.postgres_user,
            password=self.config.postgres_password
        )
        try:
            yield conn
        finally:
            conn.close()
    
    def create_postgres_schema(self):
        """Cria schema PostgreSQL"""
        schema_sql = """
        -- Tabela de usuários
        CREATE TABLE IF NOT EXISTS users (
            id VARCHAR(36) PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            plan VARCHAR(50) DEFAULT 'FREE',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            email_verified BOOLEAN DEFAULT FALSE,
            last_login TIMESTAMP,
            profile_data JSONB
        );
        
        -- Tabela de pagamentos
        CREATE TABLE IF NOT EXISTS payments (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) REFERENCES users(id),
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(10) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            payment_method VARCHAR(50),
            transaction_hash VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP,
            metadata JSONB
        );
        
        -- Tabela de análises
        CREATE TABLE IF NOT EXISTS analysis_history (
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(36) REFERENCES users(id),
            symbol VARCHAR(20) NOT NULL,
            analysis_data JSONB NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            analysis_type VARCHAR(50),
            confidence_score DECIMAL(5,2)
        );
        
        -- Tabela de afiliados
        CREATE TABLE IF NOT EXISTS affiliates (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) REFERENCES users(id) UNIQUE,
            affiliate_code VARCHAR(20) UNIQUE NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            commission_rate DECIMAL(5,2) DEFAULT 20.00,
            total_referrals INTEGER DEFAULT 0,
            total_earnings DECIMAL(10,2) DEFAULT 0.00,
            pending_earnings DECIMAL(10,2) DEFAULT 0.00,
            paid_earnings DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            approved_at TIMESTAMP,
            payout_method VARCHAR(20) DEFAULT 'usdt',
            payout_address VARCHAR(255)
        );
        
        -- Tabela de referências
        CREATE TABLE IF NOT EXISTS referrals (
            id VARCHAR(36) PRIMARY KEY,
            affiliate_id VARCHAR(36) REFERENCES affiliates(id),
            referred_user_id VARCHAR(36) REFERENCES users(id),
            referral_code VARCHAR(20) NOT NULL,
            ip_address INET,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            converted_at TIMESTAMP,
            first_payment_at TIMESTAMP
        );
        
        -- Tabela de comissões
        CREATE TABLE IF NOT EXISTS commissions (
            id VARCHAR(36) PRIMARY KEY,
            affiliate_id VARCHAR(36) REFERENCES affiliates(id),
            referral_id VARCHAR(36) REFERENCES referrals(id),
            payment_id VARCHAR(36) REFERENCES payments(id),
            amount DECIMAL(10,2) NOT NULL,
            commission_rate DECIMAL(5,2) NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            approved_at TIMESTAMP,
            paid_at TIMESTAMP,
            notes TEXT
        );
        
        -- Tabela de eventos de segurança
        CREATE TABLE IF NOT EXISTS security_events (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) REFERENCES users(id),
            event_type VARCHAR(50) NOT NULL,
            ip_address INET,
            user_agent TEXT,
            details JSONB,
            risk_score INTEGER NOT NULL,
            blocked BOOLEAN DEFAULT FALSE,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Tabela de 2FA
        CREATE TABLE IF NOT EXISTS two_factor_auth (
            user_id VARCHAR(36) PRIMARY KEY REFERENCES users(id),
            type VARCHAR(20) NOT NULL,
            secret VARCHAR(255) NOT NULL,
            enabled BOOLEAN DEFAULT FALSE,
            backup_codes JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP
        );
        
        -- Tabela de analytics de usuários
        CREATE TABLE IF NOT EXISTS user_events (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) REFERENCES users(id),
            event_type VARCHAR(50) NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            session_id VARCHAR(36),
            ip_address INET,
            user_agent TEXT,
            data JSONB,
            duration_ms INTEGER,
            success BOOLEAN DEFAULT TRUE
        );
        
        -- Tabela de sessões
        CREATE TABLE IF NOT EXISTS user_sessions (
            session_id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36) REFERENCES users(id),
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time TIMESTAMP,
            ip_address INET,
            user_agent TEXT,
            events_count INTEGER DEFAULT 0,
            duration_seconds INTEGER,
            pages_visited JSONB
        );
        
        -- Índices para performance
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX IF NOT EXISTS idx_users_plan ON users(plan);
        CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
        CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
        CREATE INDEX IF NOT EXISTS idx_analysis_user_id ON analysis_history(user_id);
        CREATE INDEX IF NOT EXISTS idx_analysis_symbol ON analysis_history(symbol);
        CREATE INDEX IF NOT EXISTS idx_analysis_created_at ON analysis_history(created_at);
        CREATE INDEX IF NOT EXISTS idx_affiliates_code ON affiliates(affiliate_code);
        CREATE INDEX IF NOT EXISTS idx_referrals_affiliate ON referrals(affiliate_id);
        CREATE INDEX IF NOT EXISTS idx_security_events_user ON security_events(user_id);
        CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);
        CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_events_timestamp ON user_events(timestamp);
        """
        
        with self.get_postgres_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(schema_sql)
            conn.commit()
            
        self.logger.info("✅ Schema PostgreSQL criado com sucesso")
    
    def backup_sqlite_data(self) -> str:
        """Cria backup dos dados SQLite"""
        if not self.config.backup_before_migration:
            return ""
        
        backup_path = f"{self.config.sqlite_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        import shutil
        shutil.copy2(self.config.sqlite_path, backup_path)
        
        self.logger.info(f"✅ Backup SQLite criado: {backup_path}")
        return backup_path
    
    def migrate_table(self, mapping: TableMapping) -> Tuple[int, int]:
        """Migra uma tabela específica"""
        self.logger.info(f"🔄 Migrando tabela: {mapping.sqlite_table} -> {mapping.postgres_table}")
        
        migrated_count = 0
        error_count = 0
        
        with self.get_sqlite_connection() as sqlite_conn:
            with self.get_postgres_connection() as postgres_conn:
                sqlite_cursor = sqlite_conn.cursor()
                postgres_cursor = postgres_conn.cursor()
                
                # Verificar se tabela existe no SQLite
                sqlite_cursor.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    (mapping.sqlite_table,)
                )
                
                if not sqlite_cursor.fetchone():
                    self.logger.warning(f"⚠️  Tabela {mapping.sqlite_table} não encontrada no SQLite")
                    return 0, 0
                
                # Obter total de registros
                sqlite_cursor.execute(f"SELECT COUNT(*) FROM {mapping.sqlite_table}")
                total_records = sqlite_cursor.fetchone()[0]
                
                if total_records == 0:
                    self.logger.info(f"ℹ️  Tabela {mapping.sqlite_table} está vazia")
                    return 0, 0
                
                # Migrar em lotes
                offset = 0
                while offset < total_records:
                    # Buscar lote do SQLite
                    sqlite_cursor.execute(
                        f"SELECT * FROM {mapping.sqlite_table} LIMIT ? OFFSET ?",
                        (self.config.batch_size, offset)
                    )
                    
                    rows = sqlite_cursor.fetchall()
                    if not rows:
                        break
                    
                    # Preparar dados para PostgreSQL
                    postgres_data = []
                    for row in rows:
                        try:
                            row_dict = dict(row)
                            postgres_row = {}
                            
                            # Mapear colunas
                            for sqlite_col, postgres_col in mapping.columns.items():
                                if sqlite_col in row_dict:
                                    value = row_dict[sqlite_col]
                                    
                                    # Aplicar transformações
                                    if sqlite_col in mapping.transformations:
                                        value = mapping.transformations[sqlite_col](value)
                                    
                                    postgres_row[postgres_col] = value
                            
                            postgres_data.append(postgres_row)
                            
                        except Exception as e:
                            self.logger.error(f"❌ Erro ao processar linha: {e}")
                            error_count += 1
                    
                    # Inserir no PostgreSQL
                    if postgres_data:
                        try:
                            columns = list(postgres_data[0].keys())
                            placeholders = ', '.join(['%s'] * len(columns))
                            insert_sql = f"""
                                INSERT INTO {mapping.postgres_table} ({', '.join(columns)})
                                VALUES ({placeholders})
                                ON CONFLICT DO NOTHING
                            """
                            
                            values = [
                                [row[col] for col in columns]
                                for row in postgres_data
                            ]
                            
                            postgres_cursor.executemany(insert_sql, values)
                            postgres_conn.commit()
                            
                            migrated_count += len(postgres_data)
                            
                        except Exception as e:
                            self.logger.error(f"❌ Erro ao inserir lote: {e}")
                            postgres_conn.rollback()
                            error_count += len(postgres_data)
                    
                    offset += self.config.batch_size
                    
                    # Log progresso
                    progress = (offset / total_records) * 100
                    self.logger.info(f"📊 Progresso {mapping.sqlite_table}: {progress:.1f}% ({migrated_count}/{total_records})")
        
        self.logger.info(f"✅ Migração {mapping.sqlite_table} concluída: {migrated_count} registros, {error_count} erros")
        return migrated_count, error_count
    
    def run_migration(self) -> Dict[str, Any]:
        """Executa migração completa"""
        self.logger.info("🚀 Iniciando migração PostgreSQL")
        
        start_time = datetime.now()
        results = {
            'start_time': start_time,
            'backup_path': '',
            'tables': {},
            'total_migrated': 0,
            'total_errors': 0
        }
        
        try:
            # Backup
            results['backup_path'] = self.backup_sqlite_data()
            
            # Criar schema
            self.create_postgres_schema()
            
            # Migrar cada tabela
            for mapping in self.table_mappings:
                migrated, errors = self.migrate_table(mapping)
                results['tables'][mapping.sqlite_table] = {
                    'migrated': migrated,
                    'errors': errors
                }
                results['total_migrated'] += migrated
                results['total_errors'] += errors
            
            # Verificar integridade
            self.verify_migration()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            results['end_time'] = end_time
            results['duration_seconds'] = duration
            results['success'] = True
            
            self.logger.info(f"🎉 Migração concluída com sucesso em {duration:.2f}s")
            self.logger.info(f"📊 Total migrado: {results['total_migrated']} registros")
            self.logger.info(f"❌ Total de erros: {results['total_errors']}")
            
        except Exception as e:
            self.logger.error(f"💥 Erro na migração: {e}")
            results['success'] = False
            results['error'] = str(e)
        
        return results
    
    def verify_migration(self):
        """Verifica integridade da migração"""
        self.logger.info("🔍 Verificando integridade da migração")
        
        with self.get_sqlite_connection() as sqlite_conn:
            with self.get_postgres_connection() as postgres_conn:
                sqlite_cursor = sqlite_conn.cursor()
                postgres_cursor = postgres_conn.cursor()
                
                for mapping in self.table_mappings:
                    # Contar registros SQLite
                    try:
                        sqlite_cursor.execute(f"SELECT COUNT(*) FROM {mapping.sqlite_table}")
                        sqlite_count = sqlite_cursor.fetchone()[0]
                    except:
                        sqlite_count = 0
                    
                    # Contar registros PostgreSQL
                    try:
                        postgres_cursor.execute(f"SELECT COUNT(*) FROM {mapping.postgres_table}")
                        postgres_count = postgres_cursor.fetchone()[0]
                    except:
                        postgres_count = 0
                    
                    if sqlite_count == postgres_count:
                        self.logger.info(f"✅ {mapping.sqlite_table}: {sqlite_count} = {postgres_count}")
                    else:
                        self.logger.warning(f"⚠️  {mapping.sqlite_table}: SQLite={sqlite_count}, PostgreSQL={postgres_count}")

def create_migration_config() -> MigrationConfig:
    """Cria configuração de migração a partir de variáveis de ambiente"""
    return MigrationConfig(
        sqlite_path=os.getenv('SQLITE_PATH', 'cryptosignals.db'),
        postgres_host=os.getenv('POSTGRES_HOST', 'localhost'),
        postgres_port=int(os.getenv('POSTGRES_PORT', '5432')),
        postgres_db=os.getenv('POSTGRES_DB', 'cryptosignals'),
        postgres_user=os.getenv('POSTGRES_USER', 'cryptosignals'),
        postgres_password=os.getenv('POSTGRES_PASSWORD', 'password'),
        batch_size=int(os.getenv('MIGRATION_BATCH_SIZE', '1000')),
        backup_before_migration=os.getenv('BACKUP_BEFORE_MIGRATION', 'true').lower() == 'true'
    )

if __name__ == "__main__":
    # Executar migração
    config = create_migration_config()
    migrator = PostgreSQLMigrator(config)
    results = migrator.run_migration()
    
    print("\n" + "="*50)
    print("RESULTADO DA MIGRAÇÃO")
    print("="*50)
    print(f"Sucesso: {results.get('success', False)}")
    print(f"Duração: {results.get('duration_seconds', 0):.2f}s")
    print(f"Total migrado: {results.get('total_migrated', 0)}")
    print(f"Total de erros: {results.get('total_errors', 0)}")
    
    if results.get('backup_path'):
        print(f"Backup criado: {results['backup_path']}")
    
    print("\nDetalhes por tabela:")
    for table, stats in results.get('tables', {}).items():
        print(f"  {table}: {stats['migrated']} migrados, {stats['errors']} erros")
