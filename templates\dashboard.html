<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - CryptoSignals</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Plotly for Charts -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">
    
    <style>
        /* Dashboard Dark Mode */
        :root {
            --primary-gold: #FFD700;
            --dark-bg: #0a0a0a;
            --dark-surface: #1a1a1a;
            --dark-card: #1e1e1e;
            --text-light: #ffffff;
            --text-muted: #b0b0b0;
            --gradient-gold: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
            --border-color: rgba(255, 215, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--dark-bg);
            color: var(--text-light);
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }
        
        /* Header */
        .header {
            background: var(--dark-surface);
            border-bottom: 1px solid var(--border-color);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-section img {
            height: 35px;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        }
        
        .logo-text {
            font-size: 1.3rem;
            font-weight: 700;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--text-muted);
            font-size: 0.9rem;
        }
        
        .plan-badge {
            background: var(--gradient-gold);
            color: var(--dark-bg);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .logout-btn {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-light);
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        
        .logout-btn:hover {
            border-color: var(--primary-gold);
            background: rgba(255, 215, 0, 0.1);
        }
        
        /* Main Content */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome-section {
            margin-bottom: 40px;
        }
        
        .welcome-title {
            font-size: 2rem;
            font-weight: 700;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .welcome-subtitle {
            color: var(--text-muted);
            font-size: 1.1rem;
        }
        
        /* Controls */
        .controls-section {
            background: var(--dark-surface);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .control-label {
            color: var(--text-light);
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .control-input {
            padding: 12px 16px;
            background: var(--dark-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: var(--text-light);
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }
        
        .control-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
        }
        
        .analyze-btn {
            background: var(--gradient-gold);
            color: var(--dark-bg);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
        }
        
        .analyze-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* Charts Section */
        .charts-section {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .chart-card {
            background: var(--dark-surface);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
        }
        
        .chart-card:hover {
            border-color: rgba(255, 215, 0, 0.3);
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-light);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chart-container {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-muted);
        }
        
        /* Loading State */
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            color: var(--text-muted);
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 215, 0, 0.1);
            border-top: 3px solid var(--primary-gold);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Error State */
        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #FCA5A5;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* Success State */
        .success-message {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #6EE7B7;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 15px;
            }
            
            .user-section {
                width: 100%;
                justify-content: space-between;
            }
            
            .controls-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-title {
                font-size: 1.5rem;
            }
            
            .main-container {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo-section">
                <img src="/logo.png" alt="CryptoSignals Logo">
                <span class="logo-text">CryptoSignals</span>
            </div>
            
            <div class="user-section">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span>{{ user.email if user else 'Usuário' }}</span>
                    <span class="plan-badge">{{ user.plan.value if user else 'Free' }}</span>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    Sair
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-container">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <h1 class="welcome-title">Dashboard CryptoSignals</h1>
            <p class="welcome-subtitle">Análise técnica avançada para suas decisões de trading</p>
        </section>

        <!-- Error/Success Messages -->
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <!-- Controls Section -->
        <section class="controls-section">
            <div class="controls-grid">
                <div class="control-group">
                    <label class="control-label">Criptomoeda</label>
                    <select id="symbol" class="control-input">
                        <option value="BTC">Bitcoin (BTC)</option>
                        <option value="ETH">Ethereum (ETH)</option>
                        <option value="ADA">Cardano (ADA)</option>
                        <option value="DOT">Polkadot (DOT)</option>
                        <option value="LINK">Chainlink (LINK)</option>
                        <option value="SOL">Solana (SOL)</option>
                        <option value="MATIC">Polygon (MATIC)</option>
                        <option value="AVAX">Avalanche (AVAX)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">Período</label>
                    <select id="period" class="control-input">
                        <option value="1m">1 Minuto</option>
                        <option value="3m" selected>3 Minutos</option>
                        <option value="5m">5 Minutos</option>
                        <option value="15m">15 Minutos</option>
                        <option value="30m">30 Minutos</option>
                        <option value="1h">1 Hora</option>
                        <option value="4h">4 Horas</option>
                        <option value="1d">1 Dia</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <button id="analyzeBtn" class="analyze-btn">
                        <i class="fas fa-chart-line"></i>
                        Analisar
                    </button>
                </div>
            </div>
        </section>

        <!-- Charts Section -->
        <section class="charts-section">
            <div class="chart-card">
                <h2 class="chart-title">
                    <i class="fas fa-chart-line"></i>
                    Gráfico Principal
                </h2>
                <div id="candlestick-chart" class="chart-container">
                    <div class="loading">
                        <i class="fas fa-chart-line" style="font-size: 3rem; color: var(--text-muted);"></i>
                        <p>Selecione uma criptomoeda e clique em "Analisar" para ver os gráficos</p>
                    </div>
                </div>
            </div>
            
            <div class="chart-card">
                <h2 class="chart-title">
                    <i class="fas fa-chart-bar"></i>
                    Volume de Negociação
                </h2>
                <div id="volume-chart" class="chart-container">
                    <div class="loading">
                        <i class="fas fa-chart-bar" style="font-size: 3rem; color: var(--text-muted);"></i>
                        <p>Volume será exibido após a análise</p>
                    </div>
                </div>
            </div>
            
            <div class="chart-card">
                <h2 class="chart-title">
                    <i class="fas fa-chart-area"></i>
                    Indicadores Técnicos
                </h2>
                <div id="indicators-chart" class="chart-container">
                    <div class="loading">
                        <i class="fas fa-chart-area" style="font-size: 3rem; color: var(--text-muted);"></i>
                        <p>Indicadores serão exibidos após a análise</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        let isAnalyzing = false;
        
        document.getElementById('analyzeBtn').addEventListener('click', analyzeSymbol);
        
        async function analyzeSymbol() {
            if (isAnalyzing) return;
            
            const symbol = document.getElementById('symbol').value;
            const period = document.getElementById('period').value;
            const btn = document.getElementById('analyzeBtn');
            
            // Show loading state
            isAnalyzing = true;
            btn.disabled = true;
            btn.innerHTML = '<div class="spinner" style="width: 20px; height: 20px; border-width: 2px;"></div> Analisando...';
            
            // Show loading in charts
            showLoadingInCharts();
            hideMessages();
            
            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ symbol, period })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage('Análise concluída com sucesso!', 'success');
                    displayCharts(result.analysis);
                } else {
                    showMessage(result.error || 'Erro na análise', 'error');
                    showErrorInCharts();
                }
            } catch (error) {
                console.error('Erro:', error);
                showMessage('Erro de conexão. Tente novamente.', 'error');
                showErrorInCharts();
            } finally {
                isAnalyzing = false;
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-chart-line"></i> Analisar';
            }
        }
        
        function showLoadingInCharts() {
            const charts = ['candlestick-chart', 'volume-chart', 'indicators-chart'];
            charts.forEach(chartId => {
                document.getElementById(chartId).innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Carregando análise...</p>
                    </div>
                `;
            });
        }
        
        function showErrorInCharts() {
            const charts = ['candlestick-chart', 'volume-chart', 'indicators-chart'];
            charts.forEach(chartId => {
                document.getElementById(chartId).innerHTML = `
                    <div class="loading">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #FCA5A5;"></i>
                        <p>Erro ao carregar dados</p>
                    </div>
                `;
            });
        }
        
        function displayCharts(analysis) {
            if (analysis.charts) {
                if (analysis.charts.candlestick) {
                    Plotly.newPlot('candlestick-chart', analysis.charts.candlestick.data, analysis.charts.candlestick.layout, {responsive: true});
                }
                if (analysis.charts.volume) {
                    Plotly.newPlot('volume-chart', analysis.charts.volume.data, analysis.charts.volume.layout, {responsive: true});
                }
                if (analysis.charts.indicators) {
                    Plotly.newPlot('indicators-chart', analysis.charts.indicators.data, analysis.charts.indicators.layout, {responsive: true});
                }
            }
        }
        
        function showMessage(message, type) {
            hideMessages();
            const messageEl = document.getElementById(type + '-message');
            messageEl.textContent = message;
            messageEl.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }
        
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }
        
        async function logout() {
            try {
                const response = await fetch('/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                if (result.success) {
                    window.location.href = result.redirect || '/';
                }
            } catch (error) {
                console.error('Erro no logout:', error);
                window.location.href = '/';
            }
        }
        
        // Check authentication status
        async function checkAuth() {
            try {
                const response = await fetch('/auth/status');
                const result = await response.json();
                
                if (!result.authenticated) {
                    window.location.href = '/login';
                }
            } catch (error) {
                console.error('Erro ao verificar autenticação:', error);
                window.location.href = '/login';
            }
        }
        
        // Check auth on page load
        checkAuth();
    </script>
</body>
</html>
