"""
Script de teste para o sistema completo de análise de criptomoedas.
Testa todas as funcionalidades implementadas.
"""

import sys
import os
import time
import requests
import json

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer

def test_data_manager():
    """Testa o gerenciador de dados."""
    print("🔧 Testando CryptoDataManager...")
    
    manager = CryptoDataManager()
    
    # Teste 1: Busca de símbolos
    print("  📍 Teste 1: Busca de símbolos")
    results = manager.search_symbols('BTC')
    print(f"    ✅ Encontrados {len(results)} resultados para 'BTC'")
    
    # Teste 2: Carregamento de dados
    print("  📍 Teste 2: Carregamento de dados")
    btc_data = manager.fetch_crypto_data('BTC', '1m')
    print(f"    ✅ Dados do BTC: {len(btc_data)} registros")
    print(f"    📊 Período: {btc_data.index.min()} até {btc_data.index.max()}")
    
    # Teste 3: Múltiplas criptomoedas
    print("  📍 Teste 3: Múltiplas criptomoedas")
    symbols = ['ETH', 'ADA', 'SOL']
    for symbol in symbols:
        data = manager.fetch_crypto_data(symbol, '1w')
        print(f"    ✅ {symbol}: {len(data)} registros")
    
    # Teste 4: Estatísticas do banco
    print("  📍 Teste 4: Estatísticas do banco")
    stats = manager.get_database_stats()
    print(f"    📊 Símbolos: {stats['symbols']}")
    print(f"    📊 Registros: {stats['data_records']}")
    print(f"    📊 Tamanho: {stats['file_size_mb']:.2f} MB")
    
    return manager, btc_data

def test_technical_analysis(data):
    """Testa a análise técnica."""
    print("\n🔍 Testando TechnicalAnalyzer...")
    
    analyzer = TechnicalAnalyzer(data)
    
    # Teste 1: Indicadores básicos
    print("  📍 Teste 1: Indicadores básicos")
    mas = analyzer.calculate_moving_averages()
    print(f"    ✅ Médias móveis calculadas: {len(mas)} indicadores")
    
    rsi = analyzer.calculate_rsi()
    print(f"    ✅ RSI atual: {rsi.iloc[-1]:.2f}")
    
    # Teste 2: Indicadores avançados
    print("  📍 Teste 2: Indicadores avançados")
    macd = analyzer.calculate_macd()
    print(f"    ✅ MACD calculado: {len(macd)} componentes")
    
    bb = analyzer.calculate_bollinger_bands()
    print(f"    ✅ Bollinger Bands: {len(bb)} bandas")
    
    # Teste 3: Análise completa
    print("  📍 Teste 3: Análise completa")
    indicators = analyzer.calculate_all_indicators()
    print(f"    ✅ Indicadores totais: {len(indicators.columns)} colunas")
    
    # Teste 4: Sinais de trading
    print("  📍 Teste 4: Sinais de trading")
    signals = analyzer.get_current_signals()
    print("    🎯 Sinais atuais:")
    for indicator, signal in signals.items():
        emoji = "🟢" if signal == "COMPRA" else "🔴" if signal == "VENDA" else "🟡"
        print(f"      {emoji} {indicator}: {signal}")
    
    # Teste 5: Resumo do mercado
    print("  📍 Teste 5: Resumo do mercado")
    summary = analyzer.get_market_summary()
    print(f"    💰 Preço atual: ${summary['current_price']:,.2f}")
    print(f"    📈 Tendência: {summary['overall_trend']}")
    print(f"    📊 RSI: {summary.get('rsi', 0):.2f}")
    
    return analyzer, summary

def test_web_api():
    """Testa a API web."""
    print("\n🌐 Testando API Web...")
    
    base_url = "http://localhost:5000"
    
    try:
        # Teste 1: Página principal
        print("  📍 Teste 1: Página principal")
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("    ✅ Página principal carregada")
        else:
            print(f"    ❌ Erro na página principal: {response.status_code}")
            return False
        
        # Teste 2: Análise de BTC
        print("  📍 Teste 2: Análise via API")
        data = {"symbol": "BTC", "period": "1m"}
        response = requests.post(f"{base_url}/analyze", 
                               json=data, 
                               headers={'Content-Type': 'application/json'},
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                analysis = result['analysis']
                print(f"    ✅ Análise BTC: ${analysis['summary']['current_price']:,.2f}")
                print(f"    📊 Tendência: {analysis['summary']['overall_trend']}")
            else:
                print(f"    ❌ Erro na análise: {result.get('error')}")
                return False
        else:
            print(f"    ❌ Erro HTTP: {response.status_code}")
            return False
        
        # Teste 3: Busca de símbolos
        print("  📍 Teste 3: Busca de símbolos")
        data = {"query": "ETH"}
        response = requests.post(f"{base_url}/search", 
                               json=data, 
                               headers={'Content-Type': 'application/json'},
                               timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"    ✅ Busca ETH: {len(result['results'])} resultados")
            else:
                print(f"    ❌ Erro na busca: {result.get('error')}")
        
        # Teste 4: Salvar análise
        print("  📍 Teste 4: Salvar análise")
        response = requests.post(f"{base_url}/save", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("    ✅ Análise salva com sucesso")
            else:
                print(f"    ❌ Erro ao salvar: {result.get('error')}")
        
        # Teste 5: Histórico
        print("  📍 Teste 5: Histórico de análises")
        response = requests.get(f"{base_url}/history", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"    ✅ Histórico: {len(result['analyses'])} análises")
            else:
                print(f"    ❌ Erro no histórico: {result.get('error')}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"    ❌ Erro de conexão: {e}")
        print("    💡 Certifique-se de que o servidor web está rodando")
        return False

def test_multiple_tokens():
    """Testa análise de múltiplos tokens."""
    print("\n🪙 Testando Múltiplos Tokens...")
    
    manager = CryptoDataManager()
    tokens = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT']
    
    results = {}
    
    for token in tokens:
        print(f"  📍 Analisando {token}...")
        try:
            # Buscar dados
            data = manager.fetch_crypto_data(token, '1w')
            
            # Análise técnica
            analyzer = TechnicalAnalyzer(data)
            summary = analyzer.get_market_summary()
            
            results[token] = {
                'price': summary['current_price'],
                'trend': summary['overall_trend'],
                'rsi': summary.get('rsi', 0),
                'data_count': len(data)
            }
            
            print(f"    ✅ {token}: ${summary['current_price']:,.2f} | {summary['overall_trend']}")
            
        except Exception as e:
            print(f"    ❌ Erro em {token}: {e}")
            results[token] = {'error': str(e)}
    
    # Resumo
    print("\n  📊 Resumo dos Tokens:")
    for token, data in results.items():
        if 'error' not in data:
            print(f"    {token}: ${data['price']:,.2f} | {data['trend']} | RSI: {data['rsi']:.1f}")
    
    return results

def main():
    """Função principal de teste."""
    print("🚀 Iniciando Testes do Sistema CryptoAnalytics")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # Teste 1: Gerenciador de dados
        manager, btc_data = test_data_manager()
        
        # Teste 2: Análise técnica
        analyzer, summary = test_technical_analysis(btc_data)
        
        # Teste 3: API Web (se disponível)
        web_success = test_web_api()
        
        # Teste 4: Múltiplos tokens
        multi_results = test_multiple_tokens()
        
        # Resumo final
        print("\n" + "=" * 60)
        print("📋 RESUMO DOS TESTES")
        print("=" * 60)
        
        print("✅ Gerenciador de Dados: OK")
        print("✅ Análise Técnica: OK")
        print(f"{'✅' if web_success else '⚠️'} API Web: {'OK' if web_success else 'Não disponível'}")
        print("✅ Múltiplos Tokens: OK")
        
        # Estatísticas finais
        stats = manager.get_database_stats()
        print(f"\n📊 Estatísticas Finais:")
        print(f"   • Símbolos no banco: {stats['symbols']}")
        print(f"   • Registros totais: {stats['data_records']:,}")
        print(f"   • Tamanho do banco: {stats['file_size_mb']:.2f} MB")
        print(f"   • Tokens testados: {len(multi_results)}")
        
        elapsed_time = time.time() - start_time
        print(f"   • Tempo total: {elapsed_time:.2f} segundos")
        
        print("\n🎉 TODOS OS TESTES CONCLUÍDOS COM SUCESSO!")
        
        if not web_success:
            print("\n💡 Para testar a API web, execute:")
            print("   python web_dashboard.py")
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE OS TESTES: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
