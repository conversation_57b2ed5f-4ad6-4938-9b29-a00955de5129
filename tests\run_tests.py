"""
Script para executar todos os testes do CryptoSignals
Inclui relatórios de cobertura e validação completa
"""

import unittest
import sys
import os
import time
from io import StringIO

# Adicionar diretório raiz ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_auth_tests():
    """Executa testes do sistema de autenticação"""
    print("🔐 Executando testes do sistema de autenticação...")
    print("=" * 60)

    # Importar módulo de testes corrigido
    from test_auth_system_fixed import TestSaaSAuthManagerFixed, TestAuthSystemIntegrationFixed

    # Criar suite de testes
    suite = unittest.TestSuite()

    # Adicionar testes unitários
    suite.addTest(unittest.makeSuite(TestSaaSAuthManagerFixed))
    suite.addTest(unittest.makeSuite(TestAuthSystemIntegrationFixed))

    # Executar testes
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)

    return result

def run_payment_tests():
    """Executa testes do sistema de pagamentos"""
    print("\n💳 Executando testes do sistema de pagamentos...")
    print("=" * 60)

    try:
        # Importar módulo de testes de pagamento corrigido
        from test_payment_system_fixed import TestTetherPaymentProcessorFixed, TestTANOSIntegrationFixed, TestPaymentSystemIntegrationFixed

        # Criar suite de testes
        suite = unittest.TestSuite()

        # Adicionar testes de pagamento
        suite.addTest(unittest.makeSuite(TestTetherPaymentProcessorFixed))
        suite.addTest(unittest.makeSuite(TestTANOSIntegrationFixed))
        suite.addTest(unittest.makeSuite(TestPaymentSystemIntegrationFixed))

        # Executar testes
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
        result = runner.run(suite)

        return result

    except ImportError as e:
        print(f"⚠️  Módulos de pagamento não encontrados: {e}")
        print("⚠️  Testes de pagamento serão implementados na próxima fase")
        # Retornar resultado mock para compatibilidade
        mock_result = type('MockResult', (), {
            'testsRun': 0,
            'failures': [],
            'errors': []
        })()
        return mock_result

def run_performance_tests():
    """Executa testes de performance"""
    print("\n⚡ Executando testes de performance...")
    print("=" * 60)

    try:
        # Importar módulo de testes de performance
        from test_performance import TestPerformanceAuth, TestPerformancePayments, TestStressTest

        # Criar suite de testes
        suite = unittest.TestSuite()

        # Adicionar testes de performance
        suite.addTest(unittest.makeSuite(TestPerformanceAuth))
        suite.addTest(unittest.makeSuite(TestPerformancePayments))
        suite.addTest(unittest.makeSuite(TestStressTest))

        # Executar testes
        runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
        result = runner.run(suite)

        return result

    except ImportError as e:
        print(f"⚠️  Módulos de performance não encontrados: {e}")
        print("⚠️  Testes de performance serão implementados na próxima fase")
        # Retornar resultado mock para compatibilidade
        mock_result = type('MockResult', (), {
            'testsRun': 0,
            'failures': [],
            'errors': []
        })()
        return mock_result

def generate_test_report(auth_result, payment_result=None, performance_result=None):
    """Gera relatório detalhado dos testes"""
    print("\n📊 RELATÓRIO DE TESTES")
    print("=" * 60)

    # Estatísticas dos testes de autenticação
    auth_tests = auth_result.testsRun
    auth_failures = len(auth_result.failures)
    auth_errors = len(auth_result.errors)

    # Estatísticas dos testes de pagamento
    payment_tests = payment_result.testsRun if payment_result else 0
    payment_failures = len(payment_result.failures) if payment_result else 0
    payment_errors = len(payment_result.errors) if payment_result else 0

    # Estatísticas dos testes de performance
    performance_tests = performance_result.testsRun if performance_result else 0
    performance_failures = len(performance_result.failures) if performance_result else 0
    performance_errors = len(performance_result.errors) if performance_result else 0

    # Totais
    total_tests = auth_tests + payment_tests + performance_tests
    total_failures = auth_failures + payment_failures + performance_failures
    total_errors = auth_errors + payment_errors + performance_errors
    success_rate = ((total_tests - total_failures - total_errors) / total_tests) * 100 if total_tests > 0 else 0

    print(f"🧪 Total de testes executados: {total_tests}")
    print(f"  🔐 Autenticação: {auth_tests} testes")
    print(f"  💳 Pagamentos: {payment_tests} testes")
    print(f"  ⚡ Performance: {performance_tests} testes")
    print(f"✅ Testes bem-sucedidos: {total_tests - total_failures - total_errors}")
    print(f"❌ Falhas: {total_failures}")
    print(f"🚨 Erros: {total_errors}")
    print(f"📈 Taxa de sucesso: {success_rate:.1f}%")

    # Detalhes das falhas
    if total_failures > 0:
        print("\n❌ FALHAS DETECTADAS:")
        for test, traceback in auth_result.failures:
            print(f"  • {test}: {traceback.split('AssertionError:')[-1].strip()}")
        if payment_result and payment_result.failures:
            for test, traceback in payment_result.failures:
                print(f"  • {test}: {traceback.split('AssertionError:')[-1].strip()}")

    # Detalhes dos erros
    if total_errors > 0:
        print("\n🚨 ERROS DETECTADOS:")
        for test, traceback in auth_result.errors:
            print(f"  • {test}: {traceback.split('Exception:')[-1].strip()}")
        if payment_result and payment_result.errors:
            for test, traceback in payment_result.errors:
                print(f"  • {test}: {traceback.split('Exception:')[-1].strip()}")

    # Status geral
    if success_rate >= 95:
        print(f"\n🎉 STATUS: EXCELENTE - Sistema pronto para produção!")
    elif success_rate >= 80:
        print(f"\n⚠️  STATUS: BOM - Algumas melhorias necessárias")
    else:
        print(f"\n🚨 STATUS: CRÍTICO - Correções urgentes necessárias")

    return success_rate >= 80

def run_security_validation():
    """Executa validações de segurança básicas"""
    print("\n🔒 Validações de segurança...")
    print("=" * 60)

    security_checks = []

    # Verificar se arquivos sensíveis existem
    sensitive_files = [
        'src/saas_auth.py',
        'src/saas_payments.py',
        'cryptosignals_app.py'
    ]

    for file_path in sensitive_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

                # Verificar se não há senhas hardcoded
                if 'password' in content.lower() and ('123' in content or 'admin' in content):
                    security_checks.append(f"⚠️  Possível senha hardcoded em {file_path}")
                else:
                    security_checks.append(f"✅ {file_path} - Sem senhas hardcoded")

                # Verificar uso de HTTPS
                if 'http://' in content and 'localhost' not in content:
                    security_checks.append(f"⚠️  Uso de HTTP não seguro em {file_path}")
                else:
                    security_checks.append(f"✅ {file_path} - Comunicação segura")

    # Exibir resultados
    for check in security_checks:
        print(f"  {check}")

    warnings = len([c for c in security_checks if '⚠️' in c])
    return warnings == 0

def main():
    """Função principal para executar todos os testes"""
    start_time = time.time()

    print("🚀 CRYPTOSIGNALS - SUITE DE TESTES COMPLETA")
    print("=" * 60)
    print(f"📅 Iniciado em: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Executar testes de autenticação
        auth_result = run_auth_tests()

        # Executar testes de pagamento
        payment_result = run_payment_tests()

        # Executar testes de performance
        performance_result = run_performance_tests()

        # Validações de segurança
        security_ok = run_security_validation()

        # Gerar relatório final
        tests_ok = generate_test_report(auth_result, payment_result, performance_result)

        # Status final
        end_time = time.time()
        duration = end_time - start_time

        print(f"\n⏱️  Tempo total de execução: {duration:.2f} segundos")

        if tests_ok and security_ok:
            print("\n🎉 TODOS OS TESTES PASSARAM! Sistema pronto para produção.")
            return 0
        else:
            print("\n⚠️  ALGUNS TESTES FALHARAM. Revisar antes do deploy.")
            return 1

    except Exception as e:
        print(f"\n🚨 ERRO CRÍTICO durante execução dos testes: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
