"""
Módulo para processamento e manipulação de dados do Bitcoin.
Responsável por carregar, limpar e preparar os dados para análise.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
from typing import Optional, Tuple, Dict, Any
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BitcoinDataProcessor:
    """Classe para processamento de dados do Bitcoin."""
    
    def __init__(self, data_path: str = "BTCUSDT.csv"):
        """
        Inicializa o processador de dados.
        
        Args:
            data_path: Caminho para o arquivo de dados CSV
        """
        self.data_path = data_path
        self.df = None
        self.processed_df = None
        
    def load_data(self) -> pd.DataFrame:
        """
        Carrega os dados do arquivo CSV ou baixa dados do Yahoo Finance.
        
        Returns:
            DataFrame com os dados carregados
        """
        try:
            # Tentar carregar do arquivo local primeiro
            self.df = pd.read_csv(self.data_path)
            logger.info(f"Dados carregados do arquivo: {self.data_path}")
            
            # Verificar se as colunas necessárias existem
            required_columns = ['Open time', 'Open', 'High', 'Low', 'Close', 'Volume']
            if not all(col in self.df.columns for col in required_columns):
                raise ValueError("Colunas necessárias não encontradas no arquivo")
                
        except (FileNotFoundError, ValueError) as e:
            logger.warning(f"Erro ao carregar arquivo local: {e}")
            logger.info("Baixando dados do Yahoo Finance...")
            self.df = self._download_yahoo_data()
            
        return self.df
    
    def _download_yahoo_data(self) -> pd.DataFrame:
        """
        Baixa dados do Bitcoin do Yahoo Finance.
        
        Returns:
            DataFrame com dados do Yahoo Finance
        """
        try:
            # Baixar dados dos últimos 2 anos
            end_date = datetime.now()
            start_date = end_date - timedelta(days=730)
            
            ticker = yf.Ticker("BTC-USD")
            data = ticker.history(
                start=start_date,
                end=end_date,
                interval="1h"  # Usar dados horários
            )
            
            # Converter para formato similar ao arquivo CSV
            df = pd.DataFrame({
                'Open time': data.index,
                'Open': data['Open'],
                'High': data['High'],
                'Low': data['Low'],
                'Close': data['Close'],
                'Volume': data['Volume'],
                'Quote volume': data['Volume'] * data['Close'],  # Aproximação
                'Trade count': np.nan,
                'Taker base volume': np.nan,
                'Taker quote volume': np.nan
            })
            
            df.reset_index(drop=True, inplace=True)
            logger.info(f"Dados baixados: {len(df)} registros")
            return df
            
        except Exception as e:
            logger.error(f"Erro ao baixar dados: {e}")
            # Criar dados de exemplo se tudo falhar
            return self._create_sample_data()
    
    def _create_sample_data(self) -> pd.DataFrame:
        """
        Cria dados de exemplo para demonstração.
        
        Returns:
            DataFrame com dados de exemplo
        """
        logger.info("Criando dados de exemplo...")
        
        # Gerar 1000 pontos de dados
        dates = pd.date_range(
            start=datetime.now() - timedelta(days=30),
            end=datetime.now(),
            freq='1H'
        )[:1000]
        
        # Simular preços do Bitcoin com random walk
        np.random.seed(42)
        initial_price = 45000
        returns = np.random.normal(0, 0.02, len(dates))
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Criar OHLC
        df_data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            high = close * (1 + abs(np.random.normal(0, 0.01)))
            low = close * (1 - abs(np.random.normal(0, 0.01)))
            open_price = prices[i-1] if i > 0 else close
            volume = np.random.uniform(100, 1000)
            
            df_data.append({
                'Open time': date,
                'Open': open_price,
                'High': high,
                'Low': low,
                'Close': close,
                'Volume': volume,
                'Quote volume': volume * close,
                'Trade count': np.random.randint(50, 200),
                'Taker base volume': volume * 0.6,
                'Taker quote volume': volume * close * 0.6
            })
        
        return pd.DataFrame(df_data)
    
    def clean_data(self) -> pd.DataFrame:
        """
        Limpa e prepara os dados para análise.
        
        Returns:
            DataFrame com dados limpos
        """
        if self.df is None:
            self.load_data()
        
        df = self.df.copy()
        
        # Converter coluna de tempo para datetime
        if 'Open time' in df.columns:
            df['Open time'] = pd.to_datetime(df['Open time'])
            df.set_index('Open time', inplace=True)
        
        # Garantir que colunas numéricas sejam do tipo correto
        numeric_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remover valores nulos
        df.dropna(subset=numeric_columns, inplace=True)
        
        # Remover outliers extremos (preços negativos ou zero)
        df = df[df['Close'] > 0]
        df = df[df['Volume'] >= 0]
        
        # Ordenar por data
        df.sort_index(inplace=True)
        
        self.processed_df = df
        logger.info(f"Dados limpos: {len(df)} registros")
        
        return df
    
    def add_technical_features(self) -> pd.DataFrame:
        """
        Adiciona features técnicas básicas aos dados.
        
        Returns:
            DataFrame com features técnicas
        """
        if self.processed_df is None:
            self.clean_data()
        
        df = self.processed_df.copy()
        
        # Retornos
        df['Returns'] = df['Close'].pct_change()
        df['Log_Returns'] = np.log(df['Close'] / df['Close'].shift(1))
        
        # Volatilidade (rolling)
        df['Volatility_24h'] = df['Returns'].rolling(window=24).std()
        
        # Médias móveis simples
        df['SMA_20'] = df['Close'].rolling(window=20).mean()
        df['SMA_50'] = df['Close'].rolling(window=50).mean()
        
        # Range verdadeiro
        df['True_Range'] = np.maximum(
            df['High'] - df['Low'],
            np.maximum(
                abs(df['High'] - df['Close'].shift(1)),
                abs(df['Low'] - df['Close'].shift(1))
            )
        )
        
        # Volume médio
        df['Volume_SMA_20'] = df['Volume'].rolling(window=20).mean()
        
        # Features de tempo
        df['Hour'] = df.index.hour
        df['Day_of_Week'] = df.index.dayofweek
        df['Month'] = df.index.month
        
        self.processed_df = df
        return df
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        Retorna um resumo dos dados carregados.
        
        Returns:
            Dicionário com estatísticas dos dados
        """
        if self.processed_df is None:
            self.clean_data()
        
        df = self.processed_df
        
        return {
            'total_records': len(df),
            'date_range': {
                'start': df.index.min().strftime('%Y-%m-%d %H:%M'),
                'end': df.index.max().strftime('%Y-%m-%d %H:%M')
            },
            'price_stats': {
                'min': df['Close'].min(),
                'max': df['Close'].max(),
                'mean': df['Close'].mean(),
                'current': df['Close'].iloc[-1]
            },
            'volume_stats': {
                'min': df['Volume'].min(),
                'max': df['Volume'].max(),
                'mean': df['Volume'].mean()
            }
        }
    
    def get_recent_data(self, hours: int = 24) -> pd.DataFrame:
        """
        Retorna dados das últimas N horas.
        
        Args:
            hours: Número de horas para retornar
            
        Returns:
            DataFrame com dados recentes
        """
        if self.processed_df is None:
            self.clean_data()
        
        cutoff_time = self.processed_df.index.max() - timedelta(hours=hours)
        return self.processed_df[self.processed_df.index >= cutoff_time]


# Função utilitária para uso rápido
def load_bitcoin_data(data_path: str = "BTCUSDT.csv") -> pd.DataFrame:
    """
    Função utilitária para carregar e processar dados do Bitcoin rapidamente.
    
    Args:
        data_path: Caminho para o arquivo de dados
        
    Returns:
        DataFrame processado com dados do Bitcoin
    """
    processor = BitcoinDataProcessor(data_path)
    return processor.add_technical_features()


if __name__ == "__main__":
    # Teste do módulo
    processor = BitcoinDataProcessor()
    data = processor.add_technical_features()
    summary = processor.get_data_summary()
    
    print("Resumo dos dados:")
    print(f"Total de registros: {summary['total_records']}")
    print(f"Período: {summary['date_range']['start']} até {summary['date_range']['end']}")
    print(f"Preço atual: ${summary['price_stats']['current']:.2f}")
