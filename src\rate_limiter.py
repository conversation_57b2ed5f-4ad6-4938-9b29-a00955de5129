"""
Sistema de Rate Limiting Avançado - CryptoSignals
Rate limiting inteligente baseado em planos e comportamento
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import json

from saas_auth import PlanType

class LimitType(Enum):
    """Tipos de limite"""
    REQUESTS_PER_MINUTE = "requests_per_minute"
    REQUESTS_PER_HOUR = "requests_per_hour"
    REQUESTS_PER_DAY = "requests_per_day"
    API_CALLS_PER_HOUR = "api_calls_per_hour"
    CONCURRENT_REQUESTS = "concurrent_requests"

@dataclass
class RateLimit:
    """Configuração de rate limit"""
    limit_type: LimitType
    max_requests: int
    window_seconds: int
    burst_allowance: int = 0  # Rajadas permitidas
    
@dataclass
class UserLimits:
    """Limites específicos do usuário"""
    user_id: str
    plan: PlanType
    limits: Dict[LimitType, RateLimit] = field(default_factory=dict)
    custom_limits: Dict[str, int] = field(default_factory=dict)
    
@dataclass
class RequestRecord:
    """Registro de requisição"""
    timestamp: datetime
    endpoint: str
    user_id: str
    ip_address: str
    success: bool = True

class TokenBucket:
    """Implementação de Token Bucket para rate limiting"""
    
    def __init__(self, capacity: int, refill_rate: float):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate  # tokens por segundo
        self.last_refill = time.time()
        self.lock = threading.Lock()
    
    def consume(self, tokens: int = 1) -> bool:
        """Tenta consumir tokens do bucket"""
        with self.lock:
            now = time.time()
            
            # Recarregar tokens
            time_passed = now - self.last_refill
            self.tokens = min(
                self.capacity,
                self.tokens + (time_passed * self.refill_rate)
            )
            self.last_refill = now
            
            # Verificar se há tokens suficientes
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            
            return False
    
    def get_status(self) -> Dict[str, float]:
        """Retorna status atual do bucket"""
        with self.lock:
            return {
                'tokens': self.tokens,
                'capacity': self.capacity,
                'refill_rate': self.refill_rate,
                'utilization': (self.capacity - self.tokens) / self.capacity
            }

class SlidingWindowCounter:
    """Contador de janela deslizante"""
    
    def __init__(self, window_seconds: int, max_requests: int):
        self.window_seconds = window_seconds
        self.max_requests = max_requests
        self.requests = deque()
        self.lock = threading.Lock()
    
    def is_allowed(self) -> Tuple[bool, int]:
        """Verifica se a requisição é permitida"""
        with self.lock:
            now = time.time()
            
            # Remover requisições antigas
            while self.requests and self.requests[0] < now - self.window_seconds:
                self.requests.popleft()
            
            # Verificar limite
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True, self.max_requests - len(self.requests)
            
            return False, 0
    
    def get_remaining_time(self) -> float:
        """Retorna tempo até próxima requisição permitida"""
        with self.lock:
            if not self.requests:
                return 0
            
            oldest_request = self.requests[0]
            return max(0, (oldest_request + self.window_seconds) - time.time())

class AdvancedRateLimiter:
    """Sistema de rate limiting avançado"""
    
    def __init__(self):
        self.user_buckets: Dict[str, Dict[str, TokenBucket]] = defaultdict(dict)
        self.user_counters: Dict[str, Dict[str, SlidingWindowCounter]] = defaultdict(dict)
        self.request_history: deque = deque(maxlen=10000)
        self.concurrent_requests: Dict[str, int] = defaultdict(int)
        self.blocked_ips: Dict[str, datetime] = {}
        self.lock = threading.Lock()
        
        # Configurações padrão por plano
        self.plan_limits = {
            PlanType.FREE: {
                LimitType.REQUESTS_PER_MINUTE: RateLimit(LimitType.REQUESTS_PER_MINUTE, 10, 60),
                LimitType.REQUESTS_PER_HOUR: RateLimit(LimitType.REQUESTS_PER_HOUR, 100, 3600),
                LimitType.API_CALLS_PER_HOUR: RateLimit(LimitType.API_CALLS_PER_HOUR, 50, 3600),
                LimitType.CONCURRENT_REQUESTS: RateLimit(LimitType.CONCURRENT_REQUESTS, 2, 1)
            },
            PlanType.STARTER: {
                LimitType.REQUESTS_PER_MINUTE: RateLimit(LimitType.REQUESTS_PER_MINUTE, 50, 60),
                LimitType.REQUESTS_PER_HOUR: RateLimit(LimitType.REQUESTS_PER_HOUR, 1000, 3600),
                LimitType.API_CALLS_PER_HOUR: RateLimit(LimitType.API_CALLS_PER_HOUR, 500, 3600),
                LimitType.CONCURRENT_REQUESTS: RateLimit(LimitType.CONCURRENT_REQUESTS, 5, 1)
            },
            PlanType.PROFESSIONAL: {
                LimitType.REQUESTS_PER_MINUTE: RateLimit(LimitType.REQUESTS_PER_MINUTE, 200, 60),
                LimitType.REQUESTS_PER_HOUR: RateLimit(LimitType.REQUESTS_PER_HOUR, 10000, 3600),
                LimitType.API_CALLS_PER_HOUR: RateLimit(LimitType.API_CALLS_PER_HOUR, 5000, 3600),
                LimitType.CONCURRENT_REQUESTS: RateLimit(LimitType.CONCURRENT_REQUESTS, 10, 1)
            },
            PlanType.ENTERPRISE: {
                LimitType.REQUESTS_PER_MINUTE: RateLimit(LimitType.REQUESTS_PER_MINUTE, 1000, 60),
                LimitType.REQUESTS_PER_HOUR: RateLimit(LimitType.REQUESTS_PER_HOUR, 50000, 3600),
                LimitType.API_CALLS_PER_HOUR: RateLimit(LimitType.API_CALLS_PER_HOUR, 25000, 3600),
                LimitType.CONCURRENT_REQUESTS: RateLimit(LimitType.CONCURRENT_REQUESTS, 25, 1)
            }
        }
        
        # Iniciar limpeza automática
        self._start_cleanup_thread()
    
    def check_rate_limit(self, user_id: str, plan: PlanType, endpoint: str, 
                        ip_address: str) -> Tuple[bool, Dict[str, any]]:
        """Verifica se a requisição está dentro dos limites"""
        
        # Verificar IP bloqueado
        if self._is_ip_blocked(ip_address):
            return False, {
                'error': 'IP temporarily blocked',
                'retry_after': 3600
            }
        
        # Obter limites do plano
        limits = self.plan_limits.get(plan, self.plan_limits[PlanType.FREE])
        
        # Verificar cada tipo de limite
        for limit_type, rate_limit in limits.items():
            allowed, info = self._check_specific_limit(
                user_id, limit_type, rate_limit, endpoint
            )
            
            if not allowed:
                # Registrar tentativa de violação
                self._record_violation(user_id, ip_address, endpoint, limit_type)
                
                return False, {
                    'error': f'Rate limit exceeded: {limit_type.value}',
                    'limit': rate_limit.max_requests,
                    'window': rate_limit.window_seconds,
                    'retry_after': info.get('retry_after', 60),
                    'remaining': info.get('remaining', 0)
                }
        
        # Registrar requisição bem-sucedida
        self._record_request(user_id, endpoint, ip_address, True)
        
        return True, {
            'allowed': True,
            'limits': self._get_user_limit_status(user_id, plan)
        }
    
    def _check_specific_limit(self, user_id: str, limit_type: LimitType, 
                            rate_limit: RateLimit, endpoint: str) -> Tuple[bool, Dict]:
        """Verifica um limite específico"""
        
        if limit_type == LimitType.CONCURRENT_REQUESTS:
            return self._check_concurrent_limit(user_id, rate_limit)
        
        # Usar sliding window counter para outros tipos
        counter_key = f"{user_id}_{limit_type.value}"
        
        if counter_key not in self.user_counters[user_id]:
            self.user_counters[user_id][counter_key] = SlidingWindowCounter(
                rate_limit.window_seconds, rate_limit.max_requests
            )
        
        counter = self.user_counters[user_id][counter_key]
        allowed, remaining = counter.is_allowed()
        
        if not allowed:
            retry_after = counter.get_remaining_time()
            return False, {'retry_after': retry_after, 'remaining': 0}
        
        return True, {'remaining': remaining}
    
    def _check_concurrent_limit(self, user_id: str, rate_limit: RateLimit) -> Tuple[bool, Dict]:
        """Verifica limite de requisições concorrentes"""
        with self.lock:
            current_concurrent = self.concurrent_requests.get(user_id, 0)
            
            if current_concurrent >= rate_limit.max_requests:
                return False, {
                    'retry_after': 1,
                    'remaining': 0,
                    'current_concurrent': current_concurrent
                }
            
            return True, {
                'remaining': rate_limit.max_requests - current_concurrent,
                'current_concurrent': current_concurrent
            }
    
    def start_request(self, user_id: str):
        """Marca início de uma requisição (para controle de concorrência)"""
        with self.lock:
            self.concurrent_requests[user_id] += 1
    
    def end_request(self, user_id: str):
        """Marca fim de uma requisição"""
        with self.lock:
            if self.concurrent_requests[user_id] > 0:
                self.concurrent_requests[user_id] -= 1
    
    def _record_request(self, user_id: str, endpoint: str, ip_address: str, success: bool):
        """Registra uma requisição"""
        record = RequestRecord(
            timestamp=datetime.now(),
            endpoint=endpoint,
            user_id=user_id,
            ip_address=ip_address,
            success=success
        )
        
        self.request_history.append(record)
    
    def _record_violation(self, user_id: str, ip_address: str, endpoint: str, limit_type: LimitType):
        """Registra uma violação de limite"""
        self._record_request(user_id, endpoint, ip_address, False)
        
        # Implementar lógica de bloqueio progressivo se necessário
        violations_in_hour = sum(
            1 for record in self.request_history
            if (record.user_id == user_id and 
                not record.success and
                (datetime.now() - record.timestamp).seconds < 3600)
        )
        
        # Bloquear IP temporariamente se muitas violações
        if violations_in_hour > 10:
            self.blocked_ips[ip_address] = datetime.now() + timedelta(hours=1)
    
    def _is_ip_blocked(self, ip_address: str) -> bool:
        """Verifica se IP está bloqueado"""
        if ip_address in self.blocked_ips:
            if datetime.now() > self.blocked_ips[ip_address]:
                del self.blocked_ips[ip_address]
                return False
            return True
        return False
    
    def _get_user_limit_status(self, user_id: str, plan: PlanType) -> Dict[str, any]:
        """Retorna status atual dos limites do usuário"""
        limits = self.plan_limits.get(plan, self.plan_limits[PlanType.FREE])
        status = {}
        
        for limit_type, rate_limit in limits.items():
            counter_key = f"{user_id}_{limit_type.value}"
            
            if limit_type == LimitType.CONCURRENT_REQUESTS:
                current = self.concurrent_requests.get(user_id, 0)
                status[limit_type.value] = {
                    'current': current,
                    'limit': rate_limit.max_requests,
                    'remaining': rate_limit.max_requests - current
                }
            elif counter_key in self.user_counters[user_id]:
                counter = self.user_counters[user_id][counter_key]
                # Simular verificação para obter status
                with counter.lock:
                    now = time.time()
                    while counter.requests and counter.requests[0] < now - counter.window_seconds:
                        counter.requests.popleft()
                    
                    current = len(counter.requests)
                    status[limit_type.value] = {
                        'current': current,
                        'limit': rate_limit.max_requests,
                        'remaining': rate_limit.max_requests - current,
                        'reset_time': counter.get_remaining_time()
                    }
        
        return status
    
    def get_analytics(self, user_id: Optional[str] = None) -> Dict[str, any]:
        """Retorna analytics de rate limiting"""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        
        # Filtrar requisições da última hora
        recent_requests = [
            r for r in self.request_history
            if r.timestamp > hour_ago and (not user_id or r.user_id == user_id)
        ]
        
        total_requests = len(recent_requests)
        successful_requests = len([r for r in recent_requests if r.success])
        failed_requests = total_requests - successful_requests
        
        # Agrupar por endpoint
        endpoint_stats = defaultdict(lambda: {'total': 0, 'success': 0, 'failed': 0})
        for request in recent_requests:
            endpoint_stats[request.endpoint]['total'] += 1
            if request.success:
                endpoint_stats[request.endpoint]['success'] += 1
            else:
                endpoint_stats[request.endpoint]['failed'] += 1
        
        return {
            'period': 'last_hour',
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'success_rate': (successful_requests / total_requests * 100) if total_requests > 0 else 0,
            'endpoint_stats': dict(endpoint_stats),
            'blocked_ips': len(self.blocked_ips),
            'active_users': len(set(r.user_id for r in recent_requests))
        }
    
    def _start_cleanup_thread(self):
        """Inicia thread de limpeza automática"""
        def cleanup():
            while True:
                try:
                    # Limpar IPs bloqueados expirados
                    now = datetime.now()
                    expired_ips = [
                        ip for ip, expiry in self.blocked_ips.items()
                        if now > expiry
                    ]
                    for ip in expired_ips:
                        del self.blocked_ips[ip]
                    
                    # Limpar contadores antigos
                    for user_counters in self.user_counters.values():
                        for counter in user_counters.values():
                            with counter.lock:
                                cutoff = time.time() - counter.window_seconds
                                while counter.requests and counter.requests[0] < cutoff:
                                    counter.requests.popleft()
                    
                    time.sleep(300)  # Limpeza a cada 5 minutos
                    
                except Exception as e:
                    print(f"Erro na limpeza do rate limiter: {e}")
                    time.sleep(300)
        
        cleanup_thread = threading.Thread(target=cleanup, daemon=True)
        cleanup_thread.start()

# Instância global do rate limiter
rate_limiter = AdvancedRateLimiter()

# Decorator para rate limiting automático
def rate_limited(endpoint_name: str = None):
    """Decorator para aplicar rate limiting automático"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            from flask import request, session, jsonify
            
            # Obter informações da requisição
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({'error': 'Authentication required'}), 401
            
            # Obter plano do usuário (implementar busca real)
            plan = PlanType.FREE  # Placeholder - buscar plano real
            ip_address = request.remote_addr
            endpoint = endpoint_name or func.__name__
            
            # Verificar rate limit
            allowed, info = rate_limiter.check_rate_limit(user_id, plan, endpoint, ip_address)
            
            if not allowed:
                return jsonify({
                    'error': 'Rate limit exceeded',
                    'details': info
                }), 429
            
            # Marcar início da requisição
            rate_limiter.start_request(user_id)
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # Marcar fim da requisição
                rate_limiter.end_request(user_id)
        
        return wrapper
    return decorator
