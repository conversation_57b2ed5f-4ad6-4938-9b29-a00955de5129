# 📚 Guia de Uso - CryptoSignals SaaS

## 🚀 Como Usar Sua Plataforma SaaS

### 1. **Inicialização do Sistema**

```bash
# Navegar para o diretório do projeto
cd BTC1MIN4YEARS

# Iniciar o servidor SaaS
python web_dashboard.py
```

**Saída esperada:**
```
🚀 Iniciando CryptoSignals - Dashboard SaaS...
🌐 Acesse: http://localhost:5000
📊 Recursos: Gráficos interativos, análise técnica avançada e interface premium
```

### 2. **Acessar a Plataforma**

- **URL**: http://localhost:5000
- **Interface**: Dashboard premium com sistema de login
- **Compatibilidade**: Todos os navegadores modernos

### 3. **Fluxo de Cadastro de Usuários**

#### 🔐 **Primeiro Acesso**
1. Clique em **"Cadastrar"** no header
2. Preencha:
   - **E-mail**: <PERSON>u endereço de e-mail
   - **Senha**: <PERSON><PERSON> segura
   - **Plano**: E<PERSON>l<PERSON> entre Free, Starter ou Professional
3. Clique em **"Criar Conta"**
4. **Resultado**: Login automático + trial de 14 dias (planos pagos)

#### 🎯 **Planos Disponíveis no Cadastro**
- **Free**: Acesso imediato e permanente
- **Starter**: 14 dias grátis, depois $29/mês
- **Professional**: 14 dias grátis, depois $79/mês

### 4. **Sistema de Pagamentos USDT**

#### 💳 **Quando o Pagamento é Necessário**
- Após 14 dias de trial (planos Starter/Pro)
- Upgrade voluntário do plano Free
- Renovação de assinatura

#### 🔗 **Dados de Pagamento**
```
Wallet: ******************************************
Rede: Ethereum
Token: USDT (Tether USD)
```

#### 📱 **Processo de Pagamento**
1. **Selecionar Upgrade**: Clique no badge do plano ou botão upgrade
2. **Escolher Período**: Mensal ou anual (2 meses grátis)
3. **Gerar Pagamento**: Sistema cria solicitação única
4. **Enviar USDT**: Para o endereço fornecido
5. **Aguardar Confirmação**: Automática em ~10-15 minutos
6. **Ativação**: Upgrade imediato após confirmação

### 5. **Funcionalidades por Plano**

#### 🆓 **Free Plan - Funcionalidades**
- ✅ Análise básica de criptomoedas
- ✅ Gráficos de candlestick simples
- ✅ 3 indicadores técnicos (SMA, EMA, RSI)
- ✅ Dados históricos de 6 meses
- ✅ 10 análises por hora
- ❌ Sem alertas
- ❌ Sem exportação completa

#### 🚀 **Starter Plan - Funcionalidades**
- ✅ Tudo do Free +
- ✅ Dados históricos de 1 ano
- ✅ 8 indicadores técnicos (MACD, Bollinger, VWAP)
- ✅ 100 análises por hora
- ✅ 5 alertas por e-mail
- ✅ Exportação CSV completa
- ✅ Backtesting simples (3 estratégias)
- ✅ Suporte via chat

#### 📈 **Professional Plan - Funcionalidades**
- ✅ Tudo do Starter +
- ✅ Dados históricos de 3 anos
- ✅ Todos os indicadores + custom scripts
- ✅ 1000 análises por hora
- ✅ 10k chamadas API por mês
- ✅ Alertas ilimitados (e-mail, SMS, webhook)
- ✅ Dashboard white-label
- ✅ Backtesting avançado ilimitado
- ✅ Equipe de 3 usuários
- ✅ Suporte prioritário 24h

### 6. **Como Usar as Análises**

#### 📊 **Análise Básica**
1. **Selecionar Criptomoeda**: 
   - Use o dropdown "Seleção Rápida" ou
   - Digite o símbolo manualmente (ex: BTC, ETH)
2. **Escolher Período**: 1 dia até 2 anos (conforme plano)
3. **Clicar "Analisar"**: Aguardar processamento
4. **Visualizar Resultados**: Gráficos e métricas

#### 📈 **Gráficos Interativos**
- **Candlestick**: Zoom, pan, tooltips
- **Volume**: Barras coloridas por tendência
- **Indicadores**: RSI, MACD, Estocástico
- **Volatilidade**: Análise de risco

#### 🎯 **Sinais de Trading**
- **🟢 Compra**: RSI < 30, MACD positivo
- **🔴 Venda**: RSI > 70, MACD negativo
- **⚪ Neutro**: Indicadores equilibrados

### 7. **Gestão de Conta**

#### 👤 **Informações do Usuário**
- **Badge do Plano**: Visível no header
- **E-mail**: Identificação da conta
- **Status**: Ativo, trial, expirado

#### 🔄 **Upgrade/Downgrade**
- **Upgrade**: A qualquer momento via pagamento
- **Downgrade**: Automático no fim da assinatura
- **Cancelamento**: Acesso mantido até expiração

#### 📊 **Monitoramento de Uso**
- **API Calls**: Contador em tempo real
- **Limites**: Alertas quando próximo do limite
- **Histórico**: Log de todas as análises

### 8. **Recursos Avançados**

#### 🔔 **Sistema de Alertas** (Starter+)
- **E-mail**: Notificações automáticas
- **SMS**: Alertas urgentes (Pro)
- **Webhook**: Integração com sistemas (Pro)

#### 📤 **Exportação de Dados**
- **CSV Limitado**: 100 linhas (Free)
- **CSV Completo**: Dados completos (Starter+)
- **API Access**: Integração programática (Pro)

#### 🤖 **Backtesting** (Starter+)
- **Simples**: 3 estratégias (Starter)
- **Avançado**: Ilimitado (Professional)
- **Relatórios**: PDF detalhados (Pro)

### 9. **Suporte e Ajuda**

#### 💬 **Canais de Suporte**
- **Free**: Fórum da comunidade
- **Starter**: Chat durante horário comercial
- **Professional**: Suporte prioritário 24h
- **Enterprise**: Gerente de conta dedicado

#### 📚 **Recursos de Aprendizado**
- **Documentação**: Guias completos
- **Tutoriais**: Vídeos explicativos
- **Webinars**: Sessões ao vivo
- **Blog**: Análises e insights

### 10. **Troubleshooting**

#### ❌ **Problemas Comuns**

**"Login required"**
- Faça login ou cadastre-se
- Verifique se a sessão não expirou

**"Plan limit exceeded"**
- Aguarde reset do limite (1 hora)
- Considere upgrade do plano

**"Payment not confirmed"**
- Verifique se enviou para wallet correta
- Aguarde confirmações na blockchain
- Entre em contato se demorar >30min

**"No data available"**
- Verifique símbolo da criptomoeda
- Tente período menor
- Verifique conexão com internet

#### 🔧 **Soluções Rápidas**
- **Refresh**: F5 para recarregar
- **Cache**: Ctrl+F5 para limpar cache
- **Logout/Login**: Renovar sessão
- **Suporte**: Contato via chat/e-mail

### 11. **Melhores Práticas**

#### 💡 **Dicas de Uso**
- **Combine indicadores**: Use múltiplas análises
- **Considere contexto**: Notícias e eventos
- **Gerencie risco**: Não invista tudo em uma análise
- **Mantenha-se atualizado**: Acompanhe mercado

#### 📈 **Estratégias Recomendadas**
- **Day Trading**: Use períodos curtos (1h, 4h)
- **Swing Trading**: Períodos médios (1d, 1w)
- **Investimento**: Períodos longos (1m, 3m)

### 12. **Roadmap e Atualizações**

#### 🔮 **Próximas Funcionalidades**
- **Tema escuro**: Toggle light/dark
- **Mobile app**: iOS e Android
- **Mais exchanges**: Binance, Coinbase, etc.
- **AI insights**: Análises com IA
- **Portfolio tracking**: Acompanhamento de carteira

#### 🚀 **Atualizações Automáticas**
- **Sem downtime**: Updates transparentes
- **Novos recursos**: Liberados gradualmente
- **Feedback**: Sugestões sempre bem-vindas

---

## 🎉 Parabéns!

Você agora tem uma **plataforma SaaS completa** de análise de criptomoedas!

**Próximos passos:**
1. ✅ Teste todas as funcionalidades
2. ✅ Configure pagamentos USDT
3. ✅ Customize conforme necessário
4. ✅ Deploy em produção
5. ✅ Comece a monetizar!

**CryptoSignals** - Sua fonte de receita recorrente! 💰🚀

---

*Para suporte técnico ou dúvidas, consulte a documentação completa ou entre em contato.*
