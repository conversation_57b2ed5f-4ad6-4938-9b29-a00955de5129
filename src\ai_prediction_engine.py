"""
Sistema de IA/ML para Predições Avançadas - CryptoSignals
Engine de machine learning para análise preditiva de criptomoedas
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json
import threading
import time
from collections import deque

try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LinearRegression
    from sklearn.preprocessing import StandardScaler, MinMaxScaler
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.model_selection import train_test_split
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    print("⚠️  Scikit-learn não disponível, usando predições básicas")

class PredictionModel(Enum):
    """Tipos de modelos de predição"""
    LINEAR_REGRESSION = "linear_regression"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    ENSEMBLE = "ensemble"
    TECHNICAL_ANALYSIS = "technical_analysis"

class PredictionTimeframe(Enum):
    """Timeframes para predições"""
    SHORT_TERM = "1h"      # 1 hora
    MEDIUM_TERM = "4h"     # 4 horas
    LONG_TERM = "1d"       # 1 dia
    EXTENDED = "1w"        # 1 semana

@dataclass
class PredictionResult:
    """Resultado de uma predição"""
    symbol: str
    model_type: PredictionModel
    timeframe: PredictionTimeframe
    predicted_price: float
    confidence: float
    direction: str  # 'up', 'down', 'sideways'
    percentage_change: float
    timestamp: datetime
    features_used: List[str]
    model_accuracy: float
    support_levels: List[float]
    resistance_levels: List[float]

@dataclass
class ModelMetrics:
    """Métricas de performance do modelo"""
    model_type: PredictionModel
    accuracy: float
    mse: float
    mae: float
    r2_score: float
    last_trained: datetime
    training_samples: int
    feature_importance: Dict[str, float]

class FeatureEngineer:
    """Engenharia de features para ML"""
    
    @staticmethod
    def create_technical_features(df: pd.DataFrame) -> pd.DataFrame:
        """Cria features técnicas avançadas"""
        df = df.copy()
        
        # Features básicas
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Médias móveis
        for period in [5, 10, 20, 50]:
            df[f'sma_{period}'] = df['close'].rolling(period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['close'] - df['bb_lower']) / df['bb_width']
        
        # Volatilidade
        df['volatility'] = df['returns'].rolling(20).std()
        df['volatility_ratio'] = df['volatility'] / df['volatility'].rolling(50).mean()
        
        # Volume features
        if 'volume' in df.columns:
            df['volume_sma'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            df['price_volume'] = df['close'] * df['volume']
        
        # Momentum
        df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['momentum_10'] = df['close'] / df['close'].shift(10) - 1
        
        # Support/Resistance levels
        df['local_max'] = df['high'].rolling(10, center=True).max() == df['high']
        df['local_min'] = df['low'].rolling(10, center=True).min() == df['low']
        
        # Time features
        df['hour'] = pd.to_datetime(df.index).hour
        df['day_of_week'] = pd.to_datetime(df.index).dayofweek
        df['month'] = pd.to_datetime(df.index).month
        
        return df
    
    @staticmethod
    def create_lag_features(df: pd.DataFrame, target_col: str = 'close', lags: List[int] = None) -> pd.DataFrame:
        """Cria features de lag temporal"""
        if lags is None:
            lags = [1, 2, 3, 5, 10]
        
        df = df.copy()
        for lag in lags:
            df[f'{target_col}_lag_{lag}'] = df[target_col].shift(lag)
        
        return df

class AIPredictionEngine:
    """Engine principal de predições com IA"""
    
    def __init__(self):
        self.models: Dict[PredictionModel, Any] = {}
        self.scalers: Dict[str, Any] = {}
        self.model_metrics: Dict[PredictionModel, ModelMetrics] = {}
        self.feature_engineer = FeatureEngineer()
        self.prediction_cache = {}
        self.training_data = deque(maxlen=10000)  # Manter últimos 10k pontos
        self.lock = threading.Lock()
        
        # Inicializar modelos se ML disponível
        if ML_AVAILABLE:
            self._initialize_models()
        
        # Iniciar thread de retreinamento automático
        self._start_retraining_thread()
    
    def _initialize_models(self):
        """Inicializa modelos de ML"""
        if not ML_AVAILABLE:
            return
        
        self.models[PredictionModel.LINEAR_REGRESSION] = LinearRegression()
        self.models[PredictionModel.RANDOM_FOREST] = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            random_state=42,
            n_jobs=-1
        )
        self.models[PredictionModel.GRADIENT_BOOSTING] = GradientBoostingRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
        
        # Scalers para normalização
        self.scalers['standard'] = StandardScaler()
        self.scalers['minmax'] = MinMaxScaler()
    
    def add_training_data(self, symbol: str, data: pd.DataFrame):
        """Adiciona dados para treinamento"""
        with self.lock:
            # Preparar dados com features
            processed_data = self.feature_engineer.create_technical_features(data)
            processed_data = self.feature_engineer.create_lag_features(processed_data)
            
            # Adicionar metadados
            for idx, row in processed_data.iterrows():
                self.training_data.append({
                    'symbol': symbol,
                    'timestamp': idx,
                    'data': row.to_dict()
                })
    
    def train_models(self, symbol: str = None) -> Dict[PredictionModel, ModelMetrics]:
        """Treina todos os modelos"""
        if not ML_AVAILABLE:
            return {}
        
        with self.lock:
            # Filtrar dados por símbolo se especificado
            training_samples = list(self.training_data)
            if symbol:
                training_samples = [d for d in training_samples if d['symbol'] == symbol]
            
            if len(training_samples) < 100:
                print(f"⚠️  Dados insuficientes para treinamento: {len(training_samples)}")
                return {}
            
            # Preparar dataset
            df = pd.DataFrame([d['data'] for d in training_samples])
            df = df.dropna()
            
            if len(df) < 50:
                print("⚠️  Dados insuficientes após limpeza")
                return {}
            
            # Features e target
            feature_cols = [col for col in df.columns if col not in ['close', 'open', 'high', 'low', 'volume']]
            X = df[feature_cols]
            y = df['close'].shift(-1).dropna()  # Predizer próximo preço
            X = X.iloc[:-1]  # Ajustar tamanho
            
            # Split treino/teste
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=False
            )
            
            # Normalizar features
            X_train_scaled = self.scalers['standard'].fit_transform(X_train)
            X_test_scaled = self.scalers['standard'].transform(X_test)
            
            # Treinar cada modelo
            results = {}
            for model_type, model in self.models.items():
                try:
                    # Treinar modelo
                    model.fit(X_train_scaled, y_train)
                    
                    # Predições
                    y_pred = model.predict(X_test_scaled)
                    
                    # Métricas
                    mse = mean_squared_error(y_test, y_pred)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                    accuracy = max(0, r2)  # R² como proxy para accuracy
                    
                    # Feature importance (se disponível)
                    feature_importance = {}
                    if hasattr(model, 'feature_importances_'):
                        feature_importance = dict(zip(feature_cols, model.feature_importances_))
                    elif hasattr(model, 'coef_'):
                        feature_importance = dict(zip(feature_cols, abs(model.coef_)))
                    
                    # Salvar métricas
                    metrics = ModelMetrics(
                        model_type=model_type,
                        accuracy=accuracy,
                        mse=mse,
                        mae=mae,
                        r2_score=r2,
                        last_trained=datetime.now(),
                        training_samples=len(X_train),
                        feature_importance=feature_importance
                    )
                    
                    self.model_metrics[model_type] = metrics
                    results[model_type] = metrics
                    
                    print(f"✅ Modelo {model_type.value} treinado - Accuracy: {accuracy:.3f}")
                    
                except Exception as e:
                    print(f"❌ Erro ao treinar {model_type.value}: {e}")
            
            return results
    
    def predict(self, symbol: str, data: pd.DataFrame, 
                model_type: PredictionModel = PredictionModel.ENSEMBLE,
                timeframe: PredictionTimeframe = PredictionTimeframe.SHORT_TERM) -> Optional[PredictionResult]:
        """Faz predição para um símbolo"""
        
        # Verificar cache
        cache_key = f"{symbol}_{model_type.value}_{timeframe.value}"
        if cache_key in self.prediction_cache:
            cached_result, timestamp = self.prediction_cache[cache_key]
            if (datetime.now() - timestamp).seconds < 300:  # Cache por 5 minutos
                return cached_result
        
        try:
            # Preparar dados
            processed_data = self.feature_engineer.create_technical_features(data)
            processed_data = self.feature_engineer.create_lag_features(processed_data)
            
            if len(processed_data) < 50:
                return self._fallback_prediction(symbol, data, timeframe)
            
            # Features para predição
            feature_cols = [col for col in processed_data.columns 
                          if col not in ['close', 'open', 'high', 'low', 'volume'] and 
                          not processed_data[col].isna().all()]
            
            if not feature_cols:
                return self._fallback_prediction(symbol, data, timeframe)
            
            latest_features = processed_data[feature_cols].iloc[-1:].fillna(0)
            
            if ML_AVAILABLE and model_type in self.models:
                # Predição com ML
                features_scaled = self.scalers['standard'].transform(latest_features)
                
                if model_type == PredictionModel.ENSEMBLE:
                    # Ensemble de modelos
                    predictions = []
                    weights = []
                    
                    for mt, model in self.models.items():
                        if mt in self.model_metrics:
                            pred = model.predict(features_scaled)[0]
                            weight = self.model_metrics[mt].accuracy
                            predictions.append(pred)
                            weights.append(weight)
                    
                    if predictions:
                        predicted_price = np.average(predictions, weights=weights)
                        confidence = np.mean(weights)
                    else:
                        return self._fallback_prediction(symbol, data, timeframe)
                else:
                    # Modelo específico
                    predicted_price = self.models[model_type].predict(features_scaled)[0]
                    confidence = self.model_metrics.get(model_type, ModelMetrics(
                        model_type, 0.5, 0, 0, 0, datetime.now(), 0, {}
                    )).accuracy
            else:
                # Fallback para análise técnica
                return self._fallback_prediction(symbol, data, timeframe)
            
            # Calcular direção e mudança percentual
            current_price = data['close'].iloc[-1]
            percentage_change = ((predicted_price - current_price) / current_price) * 100
            
            if percentage_change > 1:
                direction = 'up'
            elif percentage_change < -1:
                direction = 'down'
            else:
                direction = 'sideways'
            
            # Calcular suporte e resistência
            support_levels, resistance_levels = self._calculate_support_resistance(data)
            
            # Criar resultado
            result = PredictionResult(
                symbol=symbol,
                model_type=model_type,
                timeframe=timeframe,
                predicted_price=predicted_price,
                confidence=confidence,
                direction=direction,
                percentage_change=percentage_change,
                timestamp=datetime.now(),
                features_used=feature_cols,
                model_accuracy=confidence,
                support_levels=support_levels,
                resistance_levels=resistance_levels
            )
            
            # Cache resultado
            self.prediction_cache[cache_key] = (result, datetime.now())
            
            return result
            
        except Exception as e:
            print(f"❌ Erro na predição: {e}")
            return self._fallback_prediction(symbol, data, timeframe)
    
    def _fallback_prediction(self, symbol: str, data: pd.DataFrame, 
                           timeframe: PredictionTimeframe) -> PredictionResult:
        """Predição de fallback usando análise técnica"""
        try:
            # Análise técnica básica
            current_price = data['close'].iloc[-1]
            
            # Médias móveis
            sma_20 = data['close'].rolling(20).mean().iloc[-1]
            sma_50 = data['close'].rolling(50).mean().iloc[-1] if len(data) >= 50 else sma_20
            
            # RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean().iloc[-1]
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean().iloc[-1]
            rsi = 100 - (100 / (1 + gain / loss)) if loss != 0 else 50
            
            # Tendência baseada em médias móveis
            if current_price > sma_20 > sma_50:
                trend_factor = 1.02  # Tendência de alta
                direction = 'up'
            elif current_price < sma_20 < sma_50:
                trend_factor = 0.98  # Tendência de baixa
                direction = 'down'
            else:
                trend_factor = 1.0   # Lateral
                direction = 'sideways'
            
            # Ajuste baseado em RSI
            if rsi > 70:  # Sobrecomprado
                trend_factor *= 0.99
            elif rsi < 30:  # Sobrevendido
                trend_factor *= 1.01
            
            predicted_price = current_price * trend_factor
            percentage_change = ((predicted_price - current_price) / current_price) * 100
            
            # Confiança baseada na consistência dos indicadores
            confidence = 0.6  # Confiança moderada para análise técnica
            
            support_levels, resistance_levels = self._calculate_support_resistance(data)
            
            return PredictionResult(
                symbol=symbol,
                model_type=PredictionModel.TECHNICAL_ANALYSIS,
                timeframe=timeframe,
                predicted_price=predicted_price,
                confidence=confidence,
                direction=direction,
                percentage_change=percentage_change,
                timestamp=datetime.now(),
                features_used=['sma_20', 'sma_50', 'rsi'],
                model_accuracy=confidence,
                support_levels=support_levels,
                resistance_levels=resistance_levels
            )
            
        except Exception as e:
            print(f"❌ Erro na predição de fallback: {e}")
            # Predição neutra como último recurso
            current_price = data['close'].iloc[-1]
            return PredictionResult(
                symbol=symbol,
                model_type=PredictionModel.TECHNICAL_ANALYSIS,
                timeframe=timeframe,
                predicted_price=current_price,
                confidence=0.3,
                direction='sideways',
                percentage_change=0.0,
                timestamp=datetime.now(),
                features_used=[],
                model_accuracy=0.3,
                support_levels=[],
                resistance_levels=[]
            )
    
    def _calculate_support_resistance(self, data: pd.DataFrame) -> Tuple[List[float], List[float]]:
        """Calcula níveis de suporte e resistência"""
        try:
            # Encontrar máximos e mínimos locais
            highs = data['high'].rolling(10, center=True).max()
            lows = data['low'].rolling(10, center=True).min()
            
            # Níveis de resistência (máximos locais)
            resistance_levels = []
            for i in range(len(data)):
                if data['high'].iloc[i] == highs.iloc[i]:
                    resistance_levels.append(data['high'].iloc[i])
            
            # Níveis de suporte (mínimos locais)
            support_levels = []
            for i in range(len(data)):
                if data['low'].iloc[i] == lows.iloc[i]:
                    support_levels.append(data['low'].iloc[i])
            
            # Manter apenas os mais relevantes (últimos 5)
            resistance_levels = sorted(set(resistance_levels), reverse=True)[:5]
            support_levels = sorted(set(support_levels), reverse=True)[:5]
            
            return support_levels, resistance_levels
            
        except Exception:
            return [], []
    
    def _start_retraining_thread(self):
        """Inicia thread para retreinamento automático"""
        def retrain_loop():
            while True:
                try:
                    # Retreinar a cada 6 horas
                    time.sleep(6 * 3600)
                    
                    if len(self.training_data) > 200:
                        print("🔄 Iniciando retreinamento automático...")
                        self.train_models()
                        print("✅ Retreinamento concluído")
                    
                except Exception as e:
                    print(f"❌ Erro no retreinamento automático: {e}")
                    time.sleep(3600)  # Tentar novamente em 1 hora
        
        retrain_thread = threading.Thread(target=retrain_loop, daemon=True)
        retrain_thread.start()
    
    def get_model_status(self) -> Dict[str, Any]:
        """Retorna status dos modelos"""
        status = {
            'ml_available': ML_AVAILABLE,
            'models_trained': len(self.model_metrics),
            'training_samples': len(self.training_data),
            'cache_size': len(self.prediction_cache),
            'models': {}
        }
        
        for model_type, metrics in self.model_metrics.items():
            status['models'][model_type.value] = {
                'accuracy': metrics.accuracy,
                'last_trained': metrics.last_trained.isoformat(),
                'training_samples': metrics.training_samples,
                'top_features': sorted(
                    metrics.feature_importance.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:5]
            }
        
        return status

# Instância global do engine de predição
ai_engine = AIPredictionEngine()
