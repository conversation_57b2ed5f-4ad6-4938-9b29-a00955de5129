"""
Testes para o Sistema de Pagamentos Multi-Rede
Testa BEP20, Polygon, Ethereum e métodos tradicionais
"""

import unittest
import tempfile
import os
import json
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Importar sistema de pagamentos
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from saas_payments import (
    MultiNetworkPaymentProcessor, PaymentMethod, PaymentNetwork, 
    PaymentStatus, Payment
)

class TestMultiNetworkPaymentProcessor(unittest.TestCase):
    """Testes para o processador de pagamentos multi-rede"""
    
    def setUp(self):
        """Configuração inicial para cada teste"""
        # Criar banco temporário
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Configurar wallets de teste
        self.test_wallets = {
            'ethereum': '******************************************',
            'bsc': '******************************************',
            'polygon': '******************************************'
        }
        
        self.processor = MultiNetworkPaymentProcessor(
            wallet_addresses=self.test_wallets,
            db_path=self.temp_db.name
        )
        
        # Dados de teste
        self.test_payment_data = {
            'user_id': 'test_user_123',
            'plan': 'starter',
            'amount': 29.0,
            'annual': False
        }
    
    def tearDown(self):
        """Limpeza após cada teste"""
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_initialization(self):
        """Testa inicialização do processador"""
        self.assertEqual(self.processor.wallet_addresses, self.test_wallets)
        self.assertIsNotNone(self.processor.api_keys)
        self.assertEqual(len(self.processor.api_keys), 3)
    
    def test_create_payment_request_ethereum(self):
        """Testa criação de pagamento Ethereum USDT"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_ETH,
            annual=self.test_payment_data['annual']
        )
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.user_id, self.test_payment_data['user_id'])
        self.assertEqual(payment.plan, self.test_payment_data['plan'])
        self.assertEqual(payment.amount, self.test_payment_data['amount'])
        self.assertEqual(payment.currency, "USDT")
        self.assertEqual(payment.network, PaymentNetwork.ETHEREUM)
        self.assertEqual(payment.payment_method, PaymentMethod.USDT_ETH)
        self.assertEqual(payment.status, PaymentStatus.PENDING)
        self.assertIsNotNone(payment.id)
    
    def test_create_payment_request_bsc(self):
        """Testa criação de pagamento BSC USDT"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_BSC
        )
        
        self.assertEqual(payment.currency, "USDT")
        self.assertEqual(payment.network, PaymentNetwork.BSC)
        self.assertEqual(payment.payment_method, PaymentMethod.USDT_BSC)
        self.assertEqual(payment.wallet_address, self.test_wallets['bsc'])
    
    def test_create_payment_request_polygon(self):
        """Testa criação de pagamento Polygon USDT"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_POLYGON
        )
        
        self.assertEqual(payment.currency, "USDT")
        self.assertEqual(payment.network, PaymentNetwork.POLYGON)
        self.assertEqual(payment.payment_method, PaymentMethod.USDT_POLYGON)
        self.assertEqual(payment.wallet_address, self.test_wallets['polygon'])
    
    def test_create_payment_request_bnb(self):
        """Testa criação de pagamento BNB"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=0.1,  # Valor em BNB
            payment_method=PaymentMethod.BNB_BSC
        )
        
        self.assertEqual(payment.currency, "BNB")
        self.assertEqual(payment.network, PaymentNetwork.BSC)
        self.assertEqual(payment.payment_method, PaymentMethod.BNB_BSC)
    
    def test_create_payment_request_matic(self):
        """Testa criação de pagamento MATIC"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=50.0,  # Valor em MATIC
            payment_method=PaymentMethod.MATIC_POLYGON
        )
        
        self.assertEqual(payment.currency, "MATIC")
        self.assertEqual(payment.network, PaymentNetwork.POLYGON)
        self.assertEqual(payment.payment_method, PaymentMethod.MATIC_POLYGON)
    
    def test_save_and_get_payment(self):
        """Testa salvamento e recuperação de pagamento"""
        # Criar pagamento
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_BSC
        )
        
        # Recuperar pagamento
        retrieved_payment = self.processor.get_payment(payment.id)
        
        self.assertIsNotNone(retrieved_payment)
        self.assertEqual(retrieved_payment.id, payment.id)
        self.assertEqual(retrieved_payment.user_id, payment.user_id)
        self.assertEqual(retrieved_payment.amount, payment.amount)
        self.assertEqual(retrieved_payment.network, payment.network)
        self.assertEqual(retrieved_payment.payment_method, payment.payment_method)
    
    def test_get_nonexistent_payment(self):
        """Testa recuperação de pagamento inexistente"""
        payment = self.processor.get_payment('nonexistent_id')
        self.assertIsNone(payment)
    
    def test_update_payment_status(self):
        """Testa atualização de status de pagamento"""
        # Criar pagamento
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_ETH
        )
        
        # Atualizar status
        tx_hash = '0xabcdef1234567890'
        self.processor.update_payment_status(
            payment.id, PaymentStatus.CONFIRMED, tx_hash
        )
        
        # Verificar atualização
        updated_payment = self.processor.get_payment(payment.id)
        self.assertEqual(updated_payment.status, PaymentStatus.CONFIRMED)
        self.assertEqual(updated_payment.tx_hash, tx_hash)
        self.assertIsNotNone(updated_payment.confirmed_at)
    
    def test_get_supported_payment_methods(self):
        """Testa obtenção de métodos de pagamento suportados"""
        methods = self.processor.get_supported_payment_methods()
        
        self.assertIsInstance(methods, dict)
        self.assertIn('crypto', methods)
        self.assertIn('traditional', methods)
        
        # Verificar métodos crypto
        crypto_methods = methods['crypto']
        self.assertIsInstance(crypto_methods, list)
        self.assertTrue(len(crypto_methods) > 0)
        
        # Verificar se contém métodos BEP20 e Polygon
        method_names = [method['method'] for method in crypto_methods]
        self.assertIn('usdt_bsc', method_names)
        self.assertIn('usdt_polygon', method_names)
        self.assertIn('bnb_bsc', method_names)
        self.assertIn('matic_polygon', method_names)
    
    def test_generate_qr_code_crypto(self):
        """Testa geração de QR code para pagamento crypto"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_BSC
        )
        
        qr_data = self.processor.generate_payment_qr_code(payment)
        qr_dict = json.loads(qr_data)
        
        self.assertIn('address', qr_dict)
        self.assertIn('amount', qr_dict)
        self.assertIn('currency', qr_dict)
        self.assertIn('network', qr_dict)
        self.assertIn('method', qr_dict)
        self.assertEqual(qr_dict['network'], 'bsc')
        self.assertEqual(qr_dict['currency'], 'USDT')
    
    def test_generate_qr_code_traditional(self):
        """Testa geração de QR code para pagamento tradicional"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.CREDIT_CARD
        )
        
        qr_data = self.processor.generate_payment_qr_code(payment)
        qr_dict = json.loads(qr_data)
        
        self.assertIn('type', qr_dict)
        self.assertIn('payment_id', qr_dict)
        self.assertEqual(qr_dict['type'], 'traditional_payment')
    
    @patch('saas_payments.requests.get')
    def test_verify_crypto_transaction_success(self, mock_get):
        """Testa verificação bem-sucedida de transação crypto"""
        # Mock da resposta da API
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'result': {
                'to': '******************************************',  # USDT BSC
                'status': '0x1'
            }
        }
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Criar pagamento
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            payment_method=PaymentMethod.USDT_BSC
        )
        
        # Verificar pagamento
        tx_hash = '0x1234567890abcdef'
        result = self.processor.verify_payment(payment.id, tx_hash)
        
        self.assertTrue(result)
        
        # Verificar se status foi atualizado
        updated_payment = self.processor.get_payment(payment.id)
        self.assertEqual(updated_payment.status, PaymentStatus.CONFIRMED)

if __name__ == '__main__':
    unittest.main(verbosity=2)
