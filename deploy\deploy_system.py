"""
Sistema de Deploy Automatizado - CryptoSignals
Deploy completo com Docker, CI/CD, monitoramento e rollback
"""

import os
import subprocess
import yaml
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import requests
import shutil

class DeployEnvironment(Enum):
    """Ambientes de deploy"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class DeployStatus(Enum):
    """Status do deploy"""
    PENDING = "pending"
    BUILDING = "building"
    DEPLOYING = "deploying"
    TESTING = "testing"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"

@dataclass
class DeployConfig:
    """Configuração de deploy"""
    environment: DeployEnvironment
    version: str
    docker_registry: str
    kubernetes_namespace: str
    domain: str
    ssl_enabled: bool = True
    auto_migrate_db: bool = True
    run_tests: bool = True
    backup_before_deploy: bool = True
    rollback_on_failure: bool = True

@dataclass
class DeployResult:
    """Resultado do deploy"""
    deploy_id: str
    environment: DeployEnvironment
    version: str
    status: DeployStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration_seconds: Optional[float]
    logs: List[str]
    health_check_url: Optional[str]
    rollback_version: Optional[str]
    error_message: Optional[str] = None

class DockerManager:
    """Gerenciador Docker"""
    
    def __init__(self, registry: str):
        self.registry = registry
    
    def build_image(self, version: str, dockerfile_path: str = "Dockerfile") -> bool:
        """Constrói imagem Docker"""
        try:
            image_tag = f"{self.registry}/cryptosignals:{version}"
            
            cmd = [
                "docker", "build",
                "-t", image_tag,
                "-f", dockerfile_path,
                "."
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Imagem construída: {image_tag}")
                return True
            else:
                print(f"❌ Erro ao construir imagem: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erro no build Docker: {e}")
            return False
    
    def push_image(self, version: str) -> bool:
        """Envia imagem para registry"""
        try:
            image_tag = f"{self.registry}/cryptosignals:{version}"
            
            cmd = ["docker", "push", image_tag]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Imagem enviada: {image_tag}")
                return True
            else:
                print(f"❌ Erro ao enviar imagem: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erro no push Docker: {e}")
            return False
    
    def create_dockerfile(self) -> str:
        """Cria Dockerfile otimizado"""
        dockerfile_content = """
# Multi-stage build para otimização
FROM python:3.11-slim as builder

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    libpq-dev \\
    && rm -rf /var/lib/apt/lists/*

# Criar diretório de trabalho
WORKDIR /app

# Copiar requirements
COPY requirements.txt .

# Instalar dependências Python
RUN pip install --no-cache-dir --user -r requirements.txt

# Stage final
FROM python:3.11-slim

# Instalar dependências runtime
RUN apt-get update && apt-get install -y \\
    libpq5 \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Criar usuário não-root
RUN useradd --create-home --shell /bin/bash cryptosignals

# Copiar dependências do builder
COPY --from=builder /root/.local /home/<USER>/.local

# Configurar PATH
ENV PATH=/home/<USER>/.local/bin:$PATH

# Criar diretório de trabalho
WORKDIR /app

# Copiar código da aplicação
COPY --chown=cryptosignals:cryptosignals . .

# Mudar para usuário não-root
USER cryptosignals

# Expor porta
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:5000/health || exit 1

# Comando padrão
CMD ["python", "cryptosignals_app.py"]
"""
        
        with open("Dockerfile", "w") as f:
            f.write(dockerfile_content.strip())
        
        return "Dockerfile"

class KubernetesManager:
    """Gerenciador Kubernetes"""
    
    def __init__(self, namespace: str):
        self.namespace = namespace
    
    def create_manifests(self, config: DeployConfig) -> Dict[str, str]:
        """Cria manifestos Kubernetes"""
        manifests = {}
        
        # Deployment
        manifests["deployment.yaml"] = self._create_deployment_manifest(config)
        
        # Service
        manifests["service.yaml"] = self._create_service_manifest(config)
        
        # Ingress
        if config.ssl_enabled:
            manifests["ingress.yaml"] = self._create_ingress_manifest(config)
        
        # ConfigMap
        manifests["configmap.yaml"] = self._create_configmap_manifest(config)
        
        # Secret
        manifests["secret.yaml"] = self._create_secret_manifest(config)
        
        # HPA (Horizontal Pod Autoscaler)
        manifests["hpa.yaml"] = self._create_hpa_manifest(config)
        
        return manifests
    
    def _create_deployment_manifest(self, config: DeployConfig) -> str:
        """Cria manifest de deployment"""
        return f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cryptosignals
  namespace: {config.kubernetes_namespace}
  labels:
    app: cryptosignals
    version: {config.version}
    environment: {config.environment.value}
spec:
  replicas: {3 if config.environment == DeployEnvironment.PRODUCTION else 2}
  selector:
    matchLabels:
      app: cryptosignals
  template:
    metadata:
      labels:
        app: cryptosignals
        version: {config.version}
    spec:
      containers:
      - name: cryptosignals
        image: {config.docker_registry}/cryptosignals:{config.version}
        ports:
        - containerPort: 5000
        env:
        - name: ENVIRONMENT
          value: {config.environment.value}
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cryptosignals-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: cryptosignals-secrets
              key: redis-url
        envFrom:
        - configMapRef:
            name: cryptosignals-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
      volumes:
      - name: app-logs
        emptyDir: {{}}
"""
    
    def _create_service_manifest(self, config: DeployConfig) -> str:
        """Cria manifest de service"""
        return f"""
apiVersion: v1
kind: Service
metadata:
  name: cryptosignals-service
  namespace: {config.kubernetes_namespace}
  labels:
    app: cryptosignals
spec:
  selector:
    app: cryptosignals
  ports:
  - port: 80
    targetPort: 5000
    protocol: TCP
  type: ClusterIP
"""
    
    def _create_ingress_manifest(self, config: DeployConfig) -> str:
        """Cria manifest de ingress"""
        return f"""
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cryptosignals-ingress
  namespace: {config.kubernetes_namespace}
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - {config.domain}
    secretName: cryptosignals-tls
  rules:
  - host: {config.domain}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cryptosignals-service
            port:
              number: 80
"""
    
    def _create_configmap_manifest(self, config: DeployConfig) -> str:
        """Cria manifest de configmap"""
        return f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: cryptosignals-config
  namespace: {config.kubernetes_namespace}
data:
  FLASK_ENV: {config.environment.value}
  LOG_LEVEL: {"INFO" if config.environment == DeployEnvironment.PRODUCTION else "DEBUG"}
  CACHE_TYPE: "redis"
  RATE_LIMITING_ENABLED: "true"
  MONITORING_ENABLED: "true"
  BACKUP_ENABLED: "true"
"""
    
    def _create_secret_manifest(self, config: DeployConfig) -> str:
        """Cria manifest de secret (template)"""
        return f"""
apiVersion: v1
kind: Secret
metadata:
  name: cryptosignals-secrets
  namespace: {config.kubernetes_namespace}
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  database-url: <BASE64_ENCODED_DATABASE_URL>
  redis-url: <BASE64_ENCODED_REDIS_URL>
  jwt-secret: <BASE64_ENCODED_JWT_SECRET>
  encryption-key: <BASE64_ENCODED_ENCRYPTION_KEY>
"""
    
    def _create_hpa_manifest(self, config: DeployConfig) -> str:
        """Cria manifest de HPA"""
        return f"""
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cryptosignals-hpa
  namespace: {config.kubernetes_namespace}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cryptosignals
  minReplicas: {2 if config.environment == DeployEnvironment.PRODUCTION else 1}
  maxReplicas: {10 if config.environment == DeployEnvironment.PRODUCTION else 3}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
"""
    
    def apply_manifests(self, manifests: Dict[str, str]) -> bool:
        """Aplica manifestos no cluster"""
        try:
            # Criar diretório temporário
            os.makedirs("k8s-manifests", exist_ok=True)
            
            # Escrever manifestos
            for filename, content in manifests.items():
                with open(f"k8s-manifests/{filename}", "w") as f:
                    f.write(content.strip())
            
            # Aplicar manifestos
            cmd = ["kubectl", "apply", "-f", "k8s-manifests/"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Manifestos aplicados com sucesso")
                return True
            else:
                print(f"❌ Erro ao aplicar manifestos: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Erro na aplicação de manifestos: {e}")
            return False
        finally:
            # Limpar arquivos temporários
            if os.path.exists("k8s-manifests"):
                shutil.rmtree("k8s-manifests")

class DeploymentManager:
    """Gerenciador principal de deployment"""
    
    def __init__(self):
        self.docker_manager = None
        self.k8s_manager = None
        self.deploy_history: List[DeployResult] = []
    
    def deploy(self, config: DeployConfig) -> DeployResult:
        """Executa deploy completo"""
        import uuid
        
        deploy_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        result = DeployResult(
            deploy_id=deploy_id,
            environment=config.environment,
            version=config.version,
            status=DeployStatus.PENDING,
            start_time=start_time,
            end_time=None,
            duration_seconds=None,
            logs=[],
            health_check_url=f"https://{config.domain}/health" if config.ssl_enabled else f"http://{config.domain}/health",
            rollback_version=None
        )
        
        try:
            # Inicializar managers
            self.docker_manager = DockerManager(config.docker_registry)
            self.k8s_manager = KubernetesManager(config.kubernetes_namespace)
            
            # Fase 1: Backup (se habilitado)
            if config.backup_before_deploy:
                result.logs.append("📦 Criando backup...")
                if not self._create_backup(config):
                    raise Exception("Falha no backup")
            
            # Fase 2: Build da imagem
            result.status = DeployStatus.BUILDING
            result.logs.append("🔨 Construindo imagem Docker...")
            
            # Criar Dockerfile se não existir
            if not os.path.exists("Dockerfile"):
                self.docker_manager.create_dockerfile()
            
            if not self.docker_manager.build_image(config.version):
                raise Exception("Falha no build da imagem")
            
            if not self.docker_manager.push_image(config.version):
                raise Exception("Falha no push da imagem")
            
            # Fase 3: Deploy
            result.status = DeployStatus.DEPLOYING
            result.logs.append("🚀 Fazendo deploy no Kubernetes...")
            
            manifests = self.k8s_manager.create_manifests(config)
            if not self.k8s_manager.apply_manifests(manifests):
                raise Exception("Falha na aplicação dos manifestos")
            
            # Fase 4: Migração de banco (se habilitado)
            if config.auto_migrate_db:
                result.logs.append("🗄️  Executando migrações de banco...")
                if not self._run_migrations(config):
                    raise Exception("Falha nas migrações")
            
            # Fase 5: Testes (se habilitado)
            if config.run_tests:
                result.status = DeployStatus.TESTING
                result.logs.append("🧪 Executando testes...")
                if not self._run_health_checks(config):
                    raise Exception("Falha nos testes de saúde")
            
            # Sucesso
            result.status = DeployStatus.COMPLETED
            result.logs.append("✅ Deploy concluído com sucesso!")
            
        except Exception as e:
            result.status = DeployStatus.FAILED
            result.error_message = str(e)
            result.logs.append(f"❌ Deploy falhou: {e}")
            
            # Rollback se habilitado
            if config.rollback_on_failure:
                result.logs.append("🔄 Iniciando rollback...")
                if self._rollback(config):
                    result.status = DeployStatus.ROLLED_BACK
                    result.logs.append("✅ Rollback concluído")
                else:
                    result.logs.append("❌ Falha no rollback")
        
        finally:
            result.end_time = datetime.now()
            result.duration_seconds = (result.end_time - result.start_time).total_seconds()
            self.deploy_history.append(result)
        
        return result
    
    def _create_backup(self, config: DeployConfig) -> bool:
        """Cria backup antes do deploy"""
        try:
            # Implementar backup específico do ambiente
            backup_cmd = [
                "kubectl", "exec", "-n", config.kubernetes_namespace,
                "deployment/cryptosignals", "--",
                "python", "-c", "from src.backup_manager import backup_manager; backup_manager.create_backup('pre_deploy')"
            ]
            
            result = subprocess.run(backup_cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Erro no backup: {e}")
            return False
    
    def _run_migrations(self, config: DeployConfig) -> bool:
        """Executa migrações de banco"""
        try:
            migration_cmd = [
                "kubectl", "exec", "-n", config.kubernetes_namespace,
                "deployment/cryptosignals", "--",
                "python", "src/postgresql_migration.py"
            ]
            
            result = subprocess.run(migration_cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Erro nas migrações: {e}")
            return False
    
    def _run_health_checks(self, config: DeployConfig) -> bool:
        """Executa verificações de saúde"""
        try:
            health_url = f"https://{config.domain}/health" if config.ssl_enabled else f"http://{config.domain}/health"
            
            # Aguardar deploy estar pronto
            for attempt in range(30):  # 5 minutos
                try:
                    response = requests.get(health_url, timeout=10)
                    if response.status_code == 200:
                        health_data = response.json()
                        if health_data.get('status') == 'healthy':
                            return True
                except:
                    pass
                
                time.sleep(10)
            
            return False
            
        except Exception as e:
            print(f"❌ Erro nos health checks: {e}")
            return False
    
    def _rollback(self, config: DeployConfig) -> bool:
        """Executa rollback para versão anterior"""
        try:
            # Obter versão anterior
            rollback_cmd = [
                "kubectl", "rollout", "undo",
                f"deployment/cryptosignals",
                "-n", config.kubernetes_namespace
            ]
            
            result = subprocess.run(rollback_cmd, capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"❌ Erro no rollback: {e}")
            return False
    
    def get_deploy_status(self, deploy_id: str) -> Optional[DeployResult]:
        """Obtém status de um deploy"""
        for deploy in self.deploy_history:
            if deploy.deploy_id == deploy_id:
                return deploy
        return None

# Instância global
deployment_manager = DeploymentManager()

if __name__ == "__main__":
    # Exemplo de uso
    config = DeployConfig(
        environment=DeployEnvironment.STAGING,
        version="v1.0.0",
        docker_registry="registry.example.com",
        kubernetes_namespace="cryptosignals-staging",
        domain="staging.cryptosignals.com"
    )
    
    result = deployment_manager.deploy(config)
    
    print(f"\nDeploy ID: {result.deploy_id}")
    print(f"Status: {result.status.value}")
    print(f"Duração: {result.duration_seconds:.2f}s")
    print("\nLogs:")
    for log in result.logs:
        print(f"  {log}")
