#!/usr/bin/env python3
"""
CryptoSignals - Sistema Completo Simplificado
Todas as páginas funcionando sem dependências problemáticas
"""

import sys
import os
import json
import sqlite3
from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for
import threading
import webbrowser
from datetime import datetime, timedelta
from functools import wraps
import hashlib
import uuid

# Inicializar Flask
app = Flask(__name__)
app.secret_key = "cryptosignals_premium_2024"

# Simulação de banco de dados em memória
users_db = {}
sessions_db = {}

# Planos disponíveis
PLANS = {
    'free': {'name': 'Free', 'price': 0, 'features': ['Análise básica', '5 consultas/dia']},
    'starter': {'name': 'Starter', 'price': 29, 'features': ['Análise completa', '100 consultas/dia', 'Alertas básicos']},
    'professional': {'name': 'Professional', 'price': 79, 'features': ['IA/ML completa', 'Backtesting', '500 consultas/dia']},
    'enterprise': {'name': 'Enterprise', 'price': 199, 'features': ['Consultas ilimitadas', 'API completa', 'Suporte prioritário']}
}

# Funções auxiliares
def hash_password(password):
    return hashlib.sha256(password.encode()).hexdigest()

def create_user(email, password, plan='free'):
    user_id = str(uuid.uuid4())
    users_db[user_id] = {
        'id': user_id,
        'email': email,
        'password': hash_password(password),
        'plan': plan,
        'created_at': datetime.now(),
        'subscription_end': datetime.now() + timedelta(days=30)
    }
    return users_db[user_id]

def authenticate_user(email, password):
    for user in users_db.values():
        if user['email'] == email and user['password'] == hash_password(password):
            return user
    return None

def get_user_by_id(user_id):
    return users_db.get(user_id)

# Decoradores
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect('/login')
        return f(*args, **kwargs)
    return decorated_function

# Rotas principais
@app.route('/')
def landing_page():
    """Landing Page Principal"""
    if 'user_id' in session:
        return redirect('/dashboard')
    
    with open('templates/landing.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template)

@app.route('/login')
def login_page():
    """Página de Login"""
    if 'user_id' in session:
        return redirect('/dashboard')
    
    with open('templates/login.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template)

@app.route('/register')
def register_page():
    """Página de Registro"""
    if 'user_id' in session:
        return redirect('/dashboard')
    
    with open('templates/register.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template)

@app.route('/dashboard')
@login_required
def dashboard():
    """Dashboard Principal"""
    user = get_user_by_id(session['user_id'])
    if not user:
        return redirect('/')
    
    with open('templates/dashboard.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template, user=user)

@app.route('/admin')
@login_required
def admin_dashboard():
    """Dashboard Administrativo"""
    user = get_user_by_id(session['user_id'])
    if not user or user['plan'] not in ['professional', 'enterprise']:
        return redirect('/dashboard')
    
    with open('templates/admin_dashboard.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template, user=user)

@app.route('/payment')
@login_required
def payment_methods():
    """Métodos de Pagamento"""
    user = get_user_by_id(session['user_id'])
    
    with open('templates/payment_methods.html', 'r', encoding='utf-8') as f:
        template = f.read()
    return render_template_string(template, user=user, plans=PLANS)

# Rotas de autenticação
@app.route('/auth/login', methods=['POST'])
def login():
    """Login do usuário"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        user = authenticate_user(email, password)
        if user:
            session['user_id'] = user['id']
            return jsonify({
                'success': True,
                'redirect': '/dashboard',
                'user': {
                    'id': user['id'],
                    'email': user['email'],
                    'plan': user['plan']
                }
            })
        else:
            return jsonify({'success': False, 'error': 'Credenciais inválidas'})
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth/register', methods=['POST'])
def register():
    """Registro de novo usuário"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        plan = data.get('plan', 'free')
        
        # Verificar se usuário já existe
        for user in users_db.values():
            if user['email'] == email:
                return jsonify({'success': False, 'error': 'E-mail já cadastrado'})
        
        user = create_user(email, password, plan)
        session['user_id'] = user['id']
        
        return jsonify({
            'success': True,
            'redirect': '/dashboard',
            'user': {
                'id': user['id'],
                'email': user['email'],
                'plan': user['plan']
            }
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/auth/logout', methods=['POST'])
def logout():
    """Logout do usuário"""
    session.pop('user_id', None)
    return jsonify({'success': True, 'redirect': '/'})

# APIs simuladas
@app.route('/analyze', methods=['POST'])
@login_required
def analyze():
    """Análise simulada de criptomoeda"""
    try:
        data = request.get_json()
        symbol = data.get('symbol', 'BTC').upper()
        period = data.get('period', '3m')
        
        # Dados simulados
        fake_analysis = {
            'symbol': symbol,
            'period': period,
            'summary': {
                'current_price': 43250.00 if symbol == 'BTC' else 2850.00,
                'change_24h': 2.5,
                'volume': 28500000000,
                'rsi': 52.3,
                'macd': 'COMPRA',
                'bollinger': 'NEUTRO'
            },
            'timestamp': datetime.now().isoformat(),
            'charts': {
                'candlestick': {'data': 'simulated'},
                'volume': {'data': 'simulated'},
                'indicators': {'data': 'simulated'}
            }
        }
        
        return jsonify({
            'success': True,
            'analysis': fake_analysis
        })
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/status')
def api_status():
    """Status da API"""
    return jsonify({
        'status': 'ok',
        'message': 'CryptoSignals funcionando!',
        'timestamp': datetime.now().isoformat(),
        'users_online': len(sessions_db),
        'total_users': len(users_db)
    })

if __name__ == "__main__":
    print("🚀 Iniciando CryptoSignals - Sistema Completo...")
    print("🌐 Acesse: http://localhost:5000")
    print("📋 Páginas disponíveis:")
    print("   • Landing Page: http://localhost:5000")
    print("   • Login: http://localhost:5000/login")
    print("   • Registro: http://localhost:5000/register")
    print("   • Dashboard: http://localhost:5000/dashboard (após login)")
    print("   • Admin: http://localhost:5000/admin (planos Pro/Enterprise)")
    print("   • Pagamentos: http://localhost:5000/payment (após login)")
    print("⏹️  Pressione Ctrl+C para parar")
    print("-" * 60)
    
    # Criar usuários de teste
    create_user('<EMAIL>', 'admin123', 'enterprise')
    create_user('<EMAIL>', 'user123', 'professional')
    create_user('<EMAIL>', 'demo123', 'free')
    
    print("👥 Usuários de teste criados:")
    print("   • <EMAIL> / admin123 (Enterprise)")
    print("   • <EMAIL> / user123 (Professional)")
    print("   • <EMAIL> / demo123 (Free)")
    print("-" * 60)
    
    # Abrir navegador automaticamente
    threading.Timer(2.0, lambda: webbrowser.open('http://localhost:5000')).start()
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 CryptoSignals encerrado!")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
