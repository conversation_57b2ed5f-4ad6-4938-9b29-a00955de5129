"""
Sistema de Autenticação e Planos SaaS para CryptoSignals
Integração com pagamentos TETHER USD via TANOS
"""

import hashlib
import secrets
import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum
import sqlite3
import json

class PlanType(Enum):
    FREE = "free"
    STARTER = "starter"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

@dataclass
class Plan:
    name: str
    price_monthly: float
    price_annual: float
    features: List[str]
    limits: Dict[str, int]

@dataclass
class User:
    id: str
    email: str
    password_hash: str
    plan: PlanType
    subscription_end: datetime
    api_key: str
    wallet_address: str
    created_at: datetime
    last_login: datetime
    usage_stats: Dict[str, int]

class SaaSAuthManager:
    """Gerenciador de autenticação e planos SaaS"""

    def __init__(self, db_path: str = "saas_users.db", secret_key: str = None):
        self.db_path = db_path
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.tether_wallet = "******************************************"

        # Definir planos
        self.plans = {
            PlanType.FREE: Plan(
                name="Free",
                price_monthly=0.0,
                price_annual=0.0,
                features=[
                    "Dados históricos básicos (6 meses)",
                    "Indicadores limitados (SMA, EMA, RSI)",
                    "Dashboard público",
                    "Exportação CSV (100 linhas)",
                    "Acesso ao fórum"
                ],
                limits={
                    "api_calls_per_hour": 10,
                    "historical_months": 6,
                    "indicators": 3,
                    "csv_export_lines": 100,
                    "alerts": 0
                }
            ),
            PlanType.STARTER: Plan(
                name="Starter",
                price_monthly=29.0,
                price_annual=290.0,
                features=[
                    "Tudo do Free",
                    "Dados até 1 ano",
                    "Mais indicadores (MACD, Bollinger, VWAP)",
                    "Backtesting simples (3 estratégias)",
                    "Alertas por e-mail (5 alertas)",
                    "Exportação CSV completa",
                    "Suporte via chat"
                ],
                limits={
                    "api_calls_per_hour": 100,
                    "historical_months": 12,
                    "indicators": 8,
                    "csv_export_lines": -1,  # ilimitado
                    "alerts": 5,
                    "backtesting_strategies": 3
                }
            ),
            PlanType.PROFESSIONAL: Plan(
                name="Professional",
                price_monthly=79.0,
                price_annual=790.0,
                features=[
                    "Tudo do Starter",
                    "Histórico completo (3 anos)",
                    "Todos os indicadores + custom scripts",
                    "Backtesting avançado ilimitado",
                    "Alertas SMS e webhook",
                    "Dashboard white-label",
                    "API pública (10k calls/mês)",
                    "Equipe (3 usuários)",
                    "Suporte prioritário 24h"
                ],
                limits={
                    "api_calls_per_hour": 1000,
                    "api_calls_per_month": 10000,
                    "historical_months": 36,
                    "indicators": -1,  # ilimitado
                    "csv_export_lines": -1,
                    "alerts": -1,
                    "backtesting_strategies": -1,
                    "team_users": 3
                }
            ),
            PlanType.ENTERPRISE: Plan(
                name="Enterprise",
                price_monthly=0.0,  # sob consulta
                price_annual=0.0,   # sob consulta
                features=[
                    "Tudo do Professional",
                    "Dados em tempo real (<5s)",
                    "Consultoria de integração",
                    "SLA 99.9% uptime",
                    "API calls ilimitadas",
                    "Usuários ilimitados",
                    "Relatórios agendados",
                    "Onboarding dedicado"
                ],
                limits={
                    "api_calls_per_hour": -1,
                    "api_calls_per_month": -1,
                    "historical_months": -1,
                    "indicators": -1,
                    "csv_export_lines": -1,
                    "alerts": -1,
                    "backtesting_strategies": -1,
                    "team_users": -1
                }
            )
        }

        self.init_database()

    def init_database(self):
        """Inicializa o banco de dados SaaS"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Tabela de usuários
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                plan TEXT NOT NULL,
                subscription_end TEXT,
                api_key TEXT UNIQUE,
                wallet_address TEXT,
                created_at TEXT,
                last_login TEXT,
                usage_stats TEXT
            )
        """)

        # Tabela de pagamentos
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                plan TEXT,
                amount REAL,
                currency TEXT,
                tx_hash TEXT,
                status TEXT,
                created_at TEXT,
                confirmed_at TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # Tabela de uso da API
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS api_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT,
                endpoint TEXT,
                timestamp TEXT,
                ip_address TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        conn.commit()
        conn.close()

    def hash_password(self, password: str) -> str:
        """Hash da senha usando SHA-256 + salt"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"

    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verifica se a senha está correta"""
        try:
            salt, hash_value = password_hash.split(":")
            return hashlib.sha256((password + salt).encode()).hexdigest() == hash_value
        except:
            return False

    def generate_api_key(self) -> str:
        """Gera uma chave API única"""
        return f"ca_{''.join(secrets.choice('abcdefghijklmnopqrstuvwxyz0123456789') for _ in range(32))}"

    def create_user(self, email: str, password: str, plan: PlanType = PlanType.FREE) -> Optional[User]:
        """Cria um novo usuário"""
        try:
            user_id = secrets.token_urlsafe(16)
            password_hash = self.hash_password(password)
            api_key = self.generate_api_key()
            now = datetime.now().isoformat()

            # Calcular data de expiração da assinatura
            if plan == PlanType.FREE:
                subscription_end = datetime(2099, 12, 31)  # "Nunca" expira
            else:
                subscription_end = datetime.now() + timedelta(days=14)  # Trial de 14 dias

            user = User(
                id=user_id,
                email=email,
                password_hash=password_hash,
                plan=plan,
                subscription_end=subscription_end,
                api_key=api_key,
                wallet_address="",
                created_at=datetime.fromisoformat(now),
                last_login=datetime.fromisoformat(now),
                usage_stats={}
            )

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO users (id, email, password_hash, plan, subscription_end,
                                 api_key, wallet_address, created_at, last_login, usage_stats)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user.id, user.email, user.password_hash, user.plan.value,
                user.subscription_end.isoformat(), user.api_key, user.wallet_address,
                user.created_at.isoformat(), user.last_login.isoformat(),
                json.dumps(user.usage_stats)
            ))

            conn.commit()
            conn.close()

            return user

        except Exception as e:
            print(f"Erro ao criar usuário: {e}")
            return None

    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Autentica um usuário"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM users WHERE email = ?", (email,))
        row = cursor.fetchone()
        conn.close()

        if row and self.verify_password(password, row[2]):
            # Atualizar último login
            self.update_last_login(row[0])

            return User(
                id=row[0],
                email=row[1],
                password_hash=row[2],
                plan=PlanType(row[3]),
                subscription_end=datetime.fromisoformat(row[4]),
                api_key=row[5],
                wallet_address=row[6] or "",
                created_at=datetime.fromisoformat(row[7]),
                last_login=datetime.fromisoformat(row[8]),
                usage_stats=json.loads(row[9]) if row[9] else {}
            )

        return None

    def generate_jwt_token(self, user: User) -> str:
        """Gera um token JWT para o usuário"""
        payload = {
            "user_id": user.id,
            "email": user.email,
            "plan": user.plan.value,
            "exp": datetime.utcnow() + timedelta(hours=24)
        }
        return jwt.encode(payload, self.secret_key, algorithm="HS256")

    def verify_jwt_token(self, token: str) -> Optional[Dict]:
        """Verifica e decodifica um token JWT"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

    def check_user_limits(self, user: User, action: str) -> bool:
        """Verifica se o usuário pode executar uma ação baseado nos limites do plano"""
        plan = self.plans[user.plan]

        # Verificar se a assinatura está ativa
        if user.subscription_end < datetime.now() and user.plan != PlanType.FREE:
            return False

        # Verificar limites específicos
        if action == "api_call":
            limit = plan.limits.get("api_calls_per_hour", 0)
            if limit == -1:  # ilimitado
                return True

            # Contar chamadas da última hora
            current_usage = self.get_hourly_api_usage(user.id)
            return current_usage < limit

        return True

    def get_hourly_api_usage(self, user_id: str) -> int:
        """Conta o uso da API na última hora"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        one_hour_ago = (datetime.now() - timedelta(hours=1)).isoformat()
        cursor.execute("""
            SELECT COUNT(*) FROM api_usage
            WHERE user_id = ? AND timestamp > ?
        """, (user_id, one_hour_ago))

        count = cursor.fetchone()[0]
        conn.close()
        return count

    def log_api_usage(self, user_id: str, endpoint: str, ip_address: str):
        """Registra o uso da API"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO api_usage (user_id, endpoint, timestamp, ip_address)
            VALUES (?, ?, ?, ?)
        """, (user_id, endpoint, datetime.now().isoformat(), ip_address))

        conn.commit()
        conn.close()

    def update_last_login(self, user_id: str):
        """Atualiza o último login do usuário"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE users SET last_login = ? WHERE id = ?
        """, (datetime.now().isoformat(), user_id))

        conn.commit()
        conn.close()

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Busca um usuário pelo ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
        row = cursor.fetchone()
        conn.close()

        if row:
            return User(
                id=row[0],
                email=row[1],
                password_hash=row[2],
                plan=PlanType(row[3]),
                subscription_end=datetime.fromisoformat(row[4]),
                api_key=row[5],
                wallet_address=row[6] or "",
                created_at=datetime.fromisoformat(row[7]),
                last_login=datetime.fromisoformat(row[8]),
                usage_stats=json.loads(row[9]) if row[9] else {}
            )

        return None

    def get_payment_info(self, plan: PlanType, annual: bool = False) -> Dict:
        """Retorna informações de pagamento para um plano"""
        plan_info = self.plans[plan]
        amount = plan_info.price_annual if annual else plan_info.price_monthly

        return {
            "plan": plan.value,
            "amount": amount,
            "currency": "USDT",
            "wallet_address": self.tether_wallet,
            "network": "Ethereum",
            "annual": annual,
            "savings": plan_info.price_monthly * 12 - plan_info.price_annual if annual else 0
        }
