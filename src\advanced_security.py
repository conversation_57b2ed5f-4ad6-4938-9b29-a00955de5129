"""
Sistema de Segurança Avançada - CryptoSignals
2FA, OAuth, CAPTCHA, Rate Limiting Avançado, Audit Logs
"""

import pyotp
import qrcode
import io
import base64
import secrets
import hashlib
import sqlite3
import json
import requests
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tu<PERSON>
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time
from collections import defaultdict, deque
import re

class SecurityEventType(Enum):
    """Tipos de eventos de segurança"""
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGIN_2FA_FAILED = "login_2fa_failed"
    PASSWORD_CHANGED = "password_changed"
    EMAIL_CHANGED = "email_changed"
    API_KEY_CREATED = "api_key_created"
    API_KEY_DELETED = "api_key_deleted"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    OAUTH_LOGIN = "oauth_login"
    CAPTCHA_FAILED = "captcha_failed"

class TwoFactorType(Enum):
    """Tipos de 2FA"""
    TOTP = "totp"  # Time-based One-Time Password
    SMS = "sms"
    EMAIL = "email"
    BACKUP_CODES = "backup_codes"

class OAuthProvider(Enum):
    """Provedores OAuth"""
    GOOGLE = "google"
    GITHUB = "github"
    MICROSOFT = "microsoft"
    DISCORD = "discord"

@dataclass
class SecurityEvent:
    """Evento de segurança"""
    id: str
    user_id: str
    event_type: SecurityEventType
    ip_address: str
    user_agent: str
    details: Dict[str, Any]
    timestamp: datetime
    risk_score: int  # 0-100
    blocked: bool = False

@dataclass
class TwoFactorAuth:
    """Configuração 2FA"""
    user_id: str
    type: TwoFactorType
    secret: str
    enabled: bool
    backup_codes: List[str]
    created_at: datetime
    last_used: Optional[datetime] = None

@dataclass
class OAuthConnection:
    """Conexão OAuth"""
    user_id: str
    provider: OAuthProvider
    provider_id: str
    email: str
    name: str
    avatar_url: Optional[str]
    access_token: str
    refresh_token: Optional[str]
    created_at: datetime
    last_used: Optional[datetime] = None

class CaptchaValidator:
    """Validador de CAPTCHA"""
    
    def __init__(self, recaptcha_secret: Optional[str] = None):
        self.recaptcha_secret = recaptcha_secret
        self.failed_attempts = defaultdict(int)
        self.lockout_time = defaultdict(datetime)
    
    def validate_recaptcha(self, response: str, ip_address: str) -> bool:
        """Valida reCAPTCHA do Google"""
        if not self.recaptcha_secret:
            return True  # Bypass se não configurado
        
        try:
            data = {
                'secret': self.recaptcha_secret,
                'response': response,
                'remoteip': ip_address
            }
            
            resp = requests.post(
                'https://www.google.com/recaptcha/api/siteverify',
                data=data,
                timeout=10
            )
            
            result = resp.json()
            return result.get('success', False)
            
        except Exception as e:
            print(f"❌ Erro na validação reCAPTCHA: {e}")
            return False
    
    def is_captcha_required(self, ip_address: str) -> bool:
        """Verifica se CAPTCHA é necessário"""
        # Verificar se IP está em lockout
        if ip_address in self.lockout_time:
            if datetime.now() < self.lockout_time[ip_address]:
                return True
            else:
                del self.lockout_time[ip_address]
        
        # Verificar tentativas falhadas
        return self.failed_attempts.get(ip_address, 0) >= 3
    
    def record_failed_attempt(self, ip_address: str):
        """Registra tentativa falhada"""
        self.failed_attempts[ip_address] += 1
        
        # Lockout após 5 tentativas
        if self.failed_attempts[ip_address] >= 5:
            self.lockout_time[ip_address] = datetime.now() + timedelta(hours=1)
    
    def reset_attempts(self, ip_address: str):
        """Reseta tentativas para IP"""
        self.failed_attempts.pop(ip_address, None)
        self.lockout_time.pop(ip_address, None)

class TwoFactorManager:
    """Gerenciador de 2FA"""
    
    def __init__(self, db_path: str = "security.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Inicializa banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS two_factor_auth (
                user_id TEXT PRIMARY KEY,
                type TEXT NOT NULL,
                secret TEXT NOT NULL,
                enabled BOOLEAN DEFAULT FALSE,
                backup_codes TEXT,
                created_at TIMESTAMP NOT NULL,
                last_used TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def setup_totp(self, user_id: str, user_email: str) -> Tuple[str, str]:
        """Configura TOTP para usuário"""
        # Gerar secret
        secret = pyotp.random_base32()
        
        # Criar URI para QR code
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user_email,
            issuer_name="CryptoSignals"
        )
        
        # Gerar códigos de backup
        backup_codes = [secrets.token_hex(4).upper() for _ in range(10)]
        
        # Salvar no banco (ainda não habilitado)
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO two_factor_auth 
            (user_id, type, secret, enabled, backup_codes, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            user_id, TwoFactorType.TOTP.value, secret, False,
            json.dumps(backup_codes), datetime.now()
        ))
        
        conn.commit()
        conn.close()
        
        # Gerar QR code
        qr_code = self._generate_qr_code(provisioning_uri)
        
        return qr_code, json.dumps(backup_codes)
    
    def _generate_qr_code(self, data: str) -> str:
        """Gera QR code em base64"""
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Converter para base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    def verify_totp_setup(self, user_id: str, token: str) -> bool:
        """Verifica token TOTP durante setup"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT secret FROM two_factor_auth WHERE user_id = ? AND type = ?',
            (user_id, TwoFactorType.TOTP.value)
        )
        
        row = cursor.fetchone()
        if not row:
            conn.close()
            return False
        
        secret = row[0]
        totp = pyotp.TOTP(secret)
        
        if totp.verify(token):
            # Habilitar 2FA
            cursor.execute(
                'UPDATE two_factor_auth SET enabled = TRUE WHERE user_id = ?',
                (user_id,)
            )
            conn.commit()
            conn.close()
            return True
        
        conn.close()
        return False
    
    def verify_totp(self, user_id: str, token: str) -> bool:
        """Verifica token TOTP"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT secret, backup_codes FROM two_factor_auth 
            WHERE user_id = ? AND type = ? AND enabled = TRUE
        ''', (user_id, TwoFactorType.TOTP.value))
        
        row = cursor.fetchone()
        if not row:
            conn.close()
            return False
        
        secret, backup_codes_json = row
        
        # Verificar TOTP
        totp = pyotp.TOTP(secret)
        if totp.verify(token):
            # Atualizar último uso
            cursor.execute(
                'UPDATE two_factor_auth SET last_used = ? WHERE user_id = ?',
                (datetime.now(), user_id)
            )
            conn.commit()
            conn.close()
            return True
        
        # Verificar códigos de backup
        if backup_codes_json:
            backup_codes = json.loads(backup_codes_json)
            if token.upper() in backup_codes:
                # Remover código usado
                backup_codes.remove(token.upper())
                cursor.execute(
                    'UPDATE two_factor_auth SET backup_codes = ?, last_used = ? WHERE user_id = ?',
                    (json.dumps(backup_codes), datetime.now(), user_id)
                )
                conn.commit()
                conn.close()
                return True
        
        conn.close()
        return False
    
    def is_2fa_enabled(self, user_id: str) -> bool:
        """Verifica se 2FA está habilitado"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT enabled FROM two_factor_auth WHERE user_id = ? AND enabled = TRUE',
            (user_id,)
        )
        
        result = cursor.fetchone() is not None
        conn.close()
        return result
    
    def disable_2fa(self, user_id: str) -> bool:
        """Desabilita 2FA"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'UPDATE two_factor_auth SET enabled = FALSE WHERE user_id = ?',
            (user_id,)
        )
        
        success = cursor.rowcount > 0
        conn.commit()
        conn.close()
        return success

class OAuthManager:
    """Gerenciador de OAuth"""
    
    def __init__(self, db_path: str = "security.db"):
        self.db_path = db_path
        self.providers_config = {}
        self._init_database()
    
    def _init_database(self):
        """Inicializa banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS oauth_connections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                provider TEXT NOT NULL,
                provider_id TEXT NOT NULL,
                email TEXT NOT NULL,
                name TEXT NOT NULL,
                avatar_url TEXT,
                access_token TEXT NOT NULL,
                refresh_token TEXT,
                created_at TIMESTAMP NOT NULL,
                last_used TIMESTAMP,
                UNIQUE(user_id, provider)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def configure_provider(self, provider: OAuthProvider, client_id: str, 
                          client_secret: str, redirect_uri: str):
        """Configura provedor OAuth"""
        self.providers_config[provider] = {
            'client_id': client_id,
            'client_secret': client_secret,
            'redirect_uri': redirect_uri
        }
    
    def get_authorization_url(self, provider: OAuthProvider, state: str) -> Optional[str]:
        """Obtém URL de autorização"""
        config = self.providers_config.get(provider)
        if not config:
            return None
        
        if provider == OAuthProvider.GOOGLE:
            return (
                f"https://accounts.google.com/o/oauth2/auth?"
                f"client_id={config['client_id']}&"
                f"redirect_uri={config['redirect_uri']}&"
                f"scope=openid email profile&"
                f"response_type=code&"
                f"state={state}"
            )
        elif provider == OAuthProvider.GITHUB:
            return (
                f"https://github.com/login/oauth/authorize?"
                f"client_id={config['client_id']}&"
                f"redirect_uri={config['redirect_uri']}&"
                f"scope=user:email&"
                f"state={state}"
            )
        
        return None
    
    def exchange_code_for_token(self, provider: OAuthProvider, code: str) -> Optional[Dict[str, Any]]:
        """Troca código por token"""
        config = self.providers_config.get(provider)
        if not config:
            return None
        
        try:
            if provider == OAuthProvider.GOOGLE:
                token_url = "https://oauth2.googleapis.com/token"
                data = {
                    'client_id': config['client_id'],
                    'client_secret': config['client_secret'],
                    'code': code,
                    'grant_type': 'authorization_code',
                    'redirect_uri': config['redirect_uri']
                }
                
                response = requests.post(token_url, data=data, timeout=30)
                return response.json() if response.status_code == 200 else None
            
            elif provider == OAuthProvider.GITHUB:
                token_url = "https://github.com/login/oauth/access_token"
                headers = {'Accept': 'application/json'}
                data = {
                    'client_id': config['client_id'],
                    'client_secret': config['client_secret'],
                    'code': code
                }
                
                response = requests.post(token_url, data=data, headers=headers, timeout=30)
                return response.json() if response.status_code == 200 else None
        
        except Exception as e:
            print(f"❌ Erro na troca de token OAuth: {e}")
            return None
    
    def get_user_info(self, provider: OAuthProvider, access_token: str) -> Optional[Dict[str, Any]]:
        """Obtém informações do usuário"""
        try:
            headers = {'Authorization': f'Bearer {access_token}'}
            
            if provider == OAuthProvider.GOOGLE:
                response = requests.get(
                    'https://www.googleapis.com/oauth2/v2/userinfo',
                    headers=headers,
                    timeout=30
                )
            elif provider == OAuthProvider.GITHUB:
                response = requests.get(
                    'https://api.github.com/user',
                    headers=headers,
                    timeout=30
                )
            else:
                return None
            
            return response.json() if response.status_code == 200 else None
            
        except Exception as e:
            print(f"❌ Erro ao obter info do usuário OAuth: {e}")
            return None

class SecurityAuditLogger:
    """Logger de auditoria de segurança"""
    
    def __init__(self, db_path: str = "security.db"):
        self.db_path = db_path
        self.event_queue = deque()
        self.risk_patterns = {
            'multiple_failed_logins': 50,
            'login_from_new_location': 30,
            'api_key_creation': 20,
            'password_change': 25,
            'suspicious_user_agent': 40
        }
        self._init_database()
        self._start_processing_thread()
    
    def _init_database(self):
        """Inicializa banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_events (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                details TEXT,
                risk_score INTEGER NOT NULL,
                blocked BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP NOT NULL
            )
        ''')
        
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_security_events_user ON security_events(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp)')
        
        conn.commit()
        conn.close()
    
    def log_event(self, user_id: str, event_type: SecurityEventType, 
                  ip_address: str, user_agent: str = "", 
                  details: Dict[str, Any] = None, custom_risk_score: Optional[int] = None) -> str:
        """Registra evento de segurança"""
        import uuid
        
        event_id = str(uuid.uuid4())
        risk_score = custom_risk_score or self._calculate_risk_score(event_type, user_id, ip_address)
        
        event = SecurityEvent(
            id=event_id,
            user_id=user_id,
            event_type=event_type,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {},
            timestamp=datetime.now(),
            risk_score=risk_score,
            blocked=risk_score >= 80
        )
        
        self.event_queue.append(event)
        return event_id
    
    def _calculate_risk_score(self, event_type: SecurityEventType, user_id: str, ip_address: str) -> int:
        """Calcula score de risco"""
        base_score = 10
        
        # Score baseado no tipo de evento
        if event_type == SecurityEventType.LOGIN_FAILED:
            base_score = 30
        elif event_type == SecurityEventType.LOGIN_2FA_FAILED:
            base_score = 50
        elif event_type == SecurityEventType.SUSPICIOUS_ACTIVITY:
            base_score = 70
        elif event_type == SecurityEventType.PASSWORD_CHANGED:
            base_score = 25
        
        # Verificar padrões suspeitos
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Múltiplas tentativas falhadas na última hora
        cursor.execute('''
            SELECT COUNT(*) FROM security_events 
            WHERE user_id = ? AND event_type = ? AND timestamp > ?
        ''', (user_id, SecurityEventType.LOGIN_FAILED.value, datetime.now() - timedelta(hours=1)))
        
        failed_attempts = cursor.fetchone()[0]
        if failed_attempts > 3:
            base_score += 30
        
        # Login de novo IP
        cursor.execute('''
            SELECT COUNT(*) FROM security_events 
            WHERE user_id = ? AND ip_address = ? AND event_type = ?
        ''', (user_id, ip_address, SecurityEventType.LOGIN_SUCCESS.value))
        
        known_ip = cursor.fetchone()[0] > 0
        if not known_ip and event_type == SecurityEventType.LOGIN_SUCCESS:
            base_score += 25
        
        conn.close()
        
        return min(base_score, 100)
    
    def _start_processing_thread(self):
        """Inicia thread de processamento de eventos"""
        def process_events():
            while True:
                try:
                    if self.event_queue:
                        events_to_process = []
                        
                        # Processar até 100 eventos por vez
                        for _ in range(min(100, len(self.event_queue))):
                            if self.event_queue:
                                events_to_process.append(self.event_queue.popleft())
                        
                        if events_to_process:
                            self._save_events(events_to_process)
                    
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"❌ Erro no processamento de eventos de segurança: {e}")
                    time.sleep(5)
        
        thread = threading.Thread(target=process_events, daemon=True)
        thread.start()
    
    def _save_events(self, events: List[SecurityEvent]):
        """Salva eventos no banco"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for event in events:
            cursor.execute('''
                INSERT INTO security_events 
                (id, user_id, event_type, ip_address, user_agent, details, risk_score, blocked, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                event.id, event.user_id, event.event_type.value,
                event.ip_address, event.user_agent, json.dumps(event.details),
                event.risk_score, event.blocked, event.timestamp
            ))
        
        conn.commit()
        conn.close()
    
    def get_user_security_summary(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Obtém resumo de segurança do usuário"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        start_date = datetime.now() - timedelta(days=days)
        
        # Eventos por tipo
        cursor.execute('''
            SELECT event_type, COUNT(*), AVG(risk_score)
            FROM security_events 
            WHERE user_id = ? AND timestamp > ?
            GROUP BY event_type
        ''', (user_id, start_date))
        
        events_by_type = {row[0]: {'count': row[1], 'avg_risk': row[2]} for row in cursor.fetchall()}
        
        # IPs únicos
        cursor.execute('''
            SELECT COUNT(DISTINCT ip_address) FROM security_events 
            WHERE user_id = ? AND timestamp > ?
        ''', (user_id, start_date))
        
        unique_ips = cursor.fetchone()[0]
        
        # Eventos de alto risco
        cursor.execute('''
            SELECT COUNT(*) FROM security_events 
            WHERE user_id = ? AND timestamp > ? AND risk_score >= 70
        ''', (user_id, start_date))
        
        high_risk_events = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            'period_days': days,
            'events_by_type': events_by_type,
            'unique_ips': unique_ips,
            'high_risk_events': high_risk_events,
            'security_score': max(0, 100 - (high_risk_events * 10))
        }

# Instâncias globais
captcha_validator = CaptchaValidator()
two_factor_manager = TwoFactorManager()
oauth_manager = OAuthManager()
security_logger = SecurityAuditLogger()
