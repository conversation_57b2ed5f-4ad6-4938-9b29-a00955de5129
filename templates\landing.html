<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals - Sinais Premium de Criptomoedas</title>
    <meta name="description" content="Plataforma premium de sinais de trading para criptomoedas. Análise técnica avançada, gráficos interativos e sinais de alta precisão.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">
    
    <style>
        /* Dark Mode Landing Page */
        :root {
            --primary-gold: #FFD700;
            --dark-bg: #0a0a0a;
            --dark-surface: #1a1a1a;
            --text-light: #ffffff;
            --text-muted: #b0b0b0;
            --gradient-gold: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--dark-bg);
            color: var(--text-light);
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
        }
        
        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 215, 0, 0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .logo-nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo-nav img {
            height: 40px;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        }
        
        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .nav-buttons {
            display: flex;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-primary {
            background: var(--gradient-gold);
            color: var(--dark-bg);
        }
        
        .btn-secondary {
            background: transparent;
            color: var(--text-light);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary:hover {
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
        }
        
        .btn-secondary:hover {
            border-color: var(--primary-gold);
            background: rgba(255, 215, 0, 0.1);
        }
        
        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 100px 20px 50px;
            background: var(--gradient-dark);
            position: relative;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .hero-content {
            max-width: 800px;
            z-index: 2;
            position: relative;
        }
        
        .hero-title {
            font-size: clamp(3rem, 8vw, 5rem);
            font-weight: 800;
            margin-bottom: 20px;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 1.1;
        }
        
        .hero-subtitle {
            font-size: clamp(1.2rem, 4vw, 1.8rem);
            color: var(--text-muted);
            margin-bottom: 40px;
            line-height: 1.5;
        }
        
        .hero-cta {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 60px;
        }
        
        .hero-cta .btn {
            padding: 20px 40px;
            font-size: 1.2rem;
        }
        
        /* Stats */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-top: 80px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: var(--text-muted);
            font-size: 1.1rem;
        }
        
        /* Features Section */
        .features {
            padding: 100px 20px;
            background: var(--dark-surface);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .section-title {
            text-align: center;
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 800;
            margin-bottom: 20px;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .section-subtitle {
            text-align: center;
            color: var(--text-muted);
            font-size: 1.3rem;
            margin-bottom: 80px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }
        
        .feature-card {
            background: var(--dark-bg);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient-gold);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover::before {
            transform: scaleX(1);
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-gold);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-gold);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            font-size: 2rem;
            color: var(--dark-bg);
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: var(--text-light);
        }
        
        .feature-description {
            color: var(--text-muted);
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            color: var(--text-muted);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list li::before {
            content: '✓';
            color: var(--primary-gold);
            font-weight: bold;
        }
        
        /* Pricing */
        .pricing {
            padding: 100px 20px;
            background: var(--dark-bg);
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }
        
        .pricing-card {
            background: var(--dark-surface);
            padding: 40px;
            border-radius: 20px;
            border: 1px solid rgba(255, 215, 0, 0.1);
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .pricing-card.featured {
            border-color: var(--primary-gold);
            transform: scale(1.05);
        }
        
        .pricing-card.featured::before {
            content: 'MAIS POPULAR';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--gradient-gold);
            color: var(--dark-bg);
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
        }
        
        .pricing-card:hover {
            transform: translateY(-10px);
            border-color: var(--primary-gold);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }
        
        .pricing-card.featured:hover {
            transform: translateY(-10px) scale(1.05);
        }
        
        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-light);
        }
        
        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 5px;
        }
        
        .plan-period {
            color: var(--text-muted);
            margin-bottom: 30px;
        }
        
        .plan-features {
            list-style: none;
            padding: 0;
            margin-bottom: 40px;
        }
        
        .plan-features li {
            color: var(--text-muted);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .plan-features li::before {
            content: '✓';
            color: var(--primary-gold);
            font-weight: bold;
        }
        
        /* CTA Section */
        .cta-section {
            padding: 100px 20px;
            background: var(--gradient-dark);
            text-align: center;
        }
        
        .cta-title {
            font-size: clamp(2rem, 5vw, 3rem);
            font-weight: 800;
            margin-bottom: 20px;
            background: var(--gradient-gold);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .cta-subtitle {
            color: var(--text-muted);
            font-size: 1.2rem;
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Footer */
        .footer {
            padding: 40px 20px;
            background: var(--dark-surface);
            text-align: center;
            border-top: 1px solid rgba(255, 215, 0, 0.1);
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            color: var(--text-muted);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 20px;
            }
            
            .hero-cta {
                flex-direction: column;
                align-items: center;
            }
            
            .hero-cta .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .pricing-grid {
                grid-template-columns: 1fr;
            }
            
            .pricing-card.featured {
                transform: none;
            }
            
            .pricing-card.featured:hover {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-nav">
                <img src="/logo.png" alt="CryptoSignals Logo">
                <span class="logo-text">CryptoSignals</span>
            </div>
            <div class="nav-buttons">
                <a href="/login" class="btn btn-secondary">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </a>
                <a href="/register" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Começar Grátis
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">CryptoSignals</h1>
            <p class="hero-subtitle">
                Sinais premium de trading para criptomoedas com análise técnica avançada, 
                gráficos interativos e precisão de mercado profissional.
            </p>
            
            <div class="hero-cta">
                <a href="/register" class="btn btn-primary">
                    <i class="fas fa-rocket"></i>
                    Começar Trial Grátis
                </a>
                <a href="#features" class="btn btn-secondary">
                    <i class="fas fa-play"></i>
                    Ver Demonstração
                </a>
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">Precisão dos Sinais</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10k+</div>
                    <div class="stat-label">Traders Ativos</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Monitoramento</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Criptomoedas</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Recursos Premium</h2>
            <p class="section-subtitle">
                Tecnologia de ponta para maximizar seus resultados no trading de criptomoedas
            </p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Análise Técnica Avançada</h3>
                    <p class="feature-description">
                        Algoritmos proprietários que analisam mais de 50 indicadores técnicos em tempo real.
                    </p>
                    <ul class="feature-list">
                        <li>RSI, MACD, Bollinger Bands</li>
                        <li>Padrões de candlestick</li>
                        <li>Suporte e resistência</li>
                        <li>Análise de volume</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3 class="feature-title">Sinais em Tempo Real</h3>
                    <p class="feature-description">
                        Receba alertas instantâneos de oportunidades de trading com alta probabilidade de sucesso.
                    </p>
                    <ul class="feature-list">
                        <li>Notificações push</li>
                        <li>Alertas por email</li>
                        <li>Integração Telegram</li>
                        <li>API para bots</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Dashboard Interativo</h3>
                    <p class="feature-description">
                        Interface moderna e intuitiva com gráficos interativos e métricas em tempo real.
                    </p>
                    <ul class="feature-list">
                        <li>Gráficos Plotly avançados</li>
                        <li>Modo escuro premium</li>
                        <li>Responsivo mobile</li>
                        <li>Customização completa</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Planos e Preços</h2>
            <p class="section-subtitle">
                Escolha o plano ideal para seu nível de trading
            </p>
            
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3 class="plan-name">Starter</h3>
                    <div class="plan-price">$29</div>
                    <div class="plan-period">por mês</div>
                    <ul class="plan-features">
                        <li>Sinais básicos</li>
                        <li>5 alertas por dia</li>
                        <li>Suporte por email</li>
                        <li>Dashboard básico</li>
                        <li>14 dias grátis</li>
                    </ul>
                    <a href="/register?plan=starter" class="btn btn-primary">Começar Trial</a>
                </div>
                
                <div class="pricing-card featured">
                    <h3 class="plan-name">Professional</h3>
                    <div class="plan-price">$79</div>
                    <div class="plan-period">por mês</div>
                    <ul class="plan-features">
                        <li>Sinais premium</li>
                        <li>Alertas ilimitados</li>
                        <li>Suporte prioritário</li>
                        <li>Dashboard completo</li>
                        <li>API access</li>
                        <li>Análise personalizada</li>
                        <li>14 dias grátis</li>
                    </ul>
                    <a href="/register?plan=professional" class="btn btn-primary">Mais Popular</a>
                </div>
                
                <div class="pricing-card">
                    <h3 class="plan-name">Enterprise</h3>
                    <div class="plan-price">Custom</div>
                    <div class="plan-period">sob consulta</div>
                    <ul class="plan-features">
                        <li>Tudo do Professional</li>
                        <li>White-label</li>
                        <li>Integração customizada</li>
                        <li>Suporte dedicado</li>
                        <li>SLA garantido</li>
                        <li>Onboarding completo</li>
                    </ul>
                    <a href="mailto:<EMAIL>" class="btn btn-secondary">Contatar</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h2 class="cta-title">Pronto para Começar?</h2>
            <p class="cta-subtitle">
                Junte-se a milhares de traders que já estão maximizando seus lucros com CryptoSignals
            </p>
            <a href="/register" class="btn btn-primary" style="font-size: 1.2rem; padding: 20px 40px;">
                <i class="fas fa-rocket"></i>
                Começar Agora - 14 Dias Grátis
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <p>&copy; 2024 CryptoSignals. Todos os direitos reservados.</p>
            <p>Plataforma premium de sinais de trading para criptomoedas.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.feature-card, .pricing-card, .stat-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'all 0.6s ease';
            observer.observe(el);
        });
        
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(10, 10, 10, 0.98)';
            } else {
                navbar.style.background = 'rgba(10, 10, 10, 0.95)';
            }
        });
    </script>
</body>
</html>
