"""
Testes Unitários Corrigidos - Sistema de Pagamentos CryptoSignals
Validação dos métodos reais do TetherPaymentProcessor e TANOSIntegration
"""

import unittest
import sys
import os
import tempfile
import sqlite3
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from saas_payments import TetherPaymentProcessor, TANOSIntegration, PaymentStatus
from saas_auth import PlanType

class TestTetherPaymentProcessorFixed(unittest.TestCase):
    """Testes corrigidos para o processador de pagamentos USDT"""
    
    def setUp(self):
        """Configuração inicial para cada teste"""
        # Criar banco de dados temporário
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        
        # Inicializar tabelas necessárias
        self._init_test_database()
        
        self.test_wallet = "******************************************"
        self.processor = TetherPaymentProcessor(self.test_wallet, db_path=self.test_db.name)
        
        # Dados de teste
        self.test_payment_data = {
            'user_id': 'test_user_123',
            'plan': 'starter',
            'amount': 29.0,
            'annual': False
        }
    
    def _init_test_database(self):
        """Inicializa banco de dados de teste"""
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        # Criar tabela de pagamentos
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                plan TEXT,
                amount REAL,
                currency TEXT,
                tx_hash TEXT,
                status TEXT,
                created_at TEXT,
                confirmed_at TEXT
            )
        """)
        
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Limpeza após cada teste"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)
    
    def test_wallet_initialization(self):
        """Testa inicialização com wallet address"""
        self.assertEqual(self.processor.wallet_address, self.test_wallet)
        self.assertIsNotNone(self.processor.usdt_contract)
        self.assertEqual(self.processor.usdt_contract, "******************************************")
    
    def test_create_payment_request(self):
        """Testa criação de solicitação de pagamento"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            annual=self.test_payment_data['annual']
        )
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.user_id, self.test_payment_data['user_id'])
        self.assertEqual(payment.plan, self.test_payment_data['plan'])
        self.assertEqual(payment.amount, self.test_payment_data['amount'])
        self.assertEqual(payment.currency, "USDT")
        self.assertEqual(payment.status, PaymentStatus.PENDING)
        self.assertIsNotNone(payment.id)
    
    def test_save_and_get_payment(self):
        """Testa salvamento e recuperação de pagamento"""
        # Criar pagamento
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount']
        )
        
        # Recuperar pagamento
        retrieved_payment = self.processor.get_payment(payment.id)
        
        self.assertIsNotNone(retrieved_payment)
        self.assertEqual(retrieved_payment.id, payment.id)
        self.assertEqual(retrieved_payment.user_id, payment.user_id)
        self.assertEqual(retrieved_payment.amount, payment.amount)
    
    def test_get_nonexistent_payment(self):
        """Testa recuperação de pagamento inexistente"""
        payment = self.processor.get_payment('nonexistent_id')
        self.assertIsNone(payment)
    
    def test_update_payment_status(self):
        """Testa atualização de status de pagamento"""
        # Criar pagamento
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount']
        )
        
        # Atualizar status
        test_tx_hash = "0x123456789abcdef"
        self.processor.update_payment_status(payment.id, PaymentStatus.CONFIRMED, test_tx_hash)
        
        # Verificar atualização
        updated_payment = self.processor.get_payment(payment.id)
        self.assertEqual(updated_payment.status, PaymentStatus.CONFIRMED)
        self.assertEqual(updated_payment.tx_hash, test_tx_hash)
    
    def test_get_user_payments(self):
        """Testa recuperação de pagamentos de usuário"""
        user_id = 'test_user_payments'
        
        # Criar múltiplos pagamentos
        payment1 = self.processor.create_payment_request(user_id, 'starter', 29.0)
        payment2 = self.processor.create_payment_request(user_id, 'professional', 79.0)
        
        # Recuperar pagamentos do usuário
        user_payments = self.processor.get_user_payments(user_id)
        
        self.assertIsInstance(user_payments, list)
        self.assertGreaterEqual(len(user_payments), 2)
        
        # Verificar se os pagamentos estão na lista
        payment_ids = [p.id for p in user_payments]
        self.assertIn(payment1.id, payment_ids)
        self.assertIn(payment2.id, payment_ids)
    
    def test_generate_payment_qr_code(self):
        """Testa geração de dados para QR code"""
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount']
        )
        
        qr_data = self.processor.generate_payment_qr_code(payment)
        
        self.assertIsNotNone(qr_data)
        self.assertIsInstance(qr_data, str)
        
        # Verificar se é JSON válido
        import json
        parsed_data = json.loads(qr_data)
        self.assertIn('address', parsed_data)
        self.assertIn('amount', parsed_data)
        self.assertIn('currency', parsed_data)
        self.assertEqual(parsed_data['address'], self.test_wallet)
        self.assertEqual(parsed_data['amount'], self.test_payment_data['amount'])
    
    @patch('saas_payments.requests.get')
    def test_verify_usdt_transaction_mock(self, mock_get):
        """Testa verificação de transação USDT com mock"""
        # Mock da resposta da API Etherscan
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'result': {
                'to': self.processor.usdt_contract,
                'status': '0x1'
            }
        }
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Testar verificação
        result = self.processor.verify_usdt_transaction('0x123456789', 29.0)
        
        # Como é um mock simplificado, esperamos True
        self.assertTrue(result)
    
    def test_check_expired_payments(self):
        """Testa verificação de pagamentos expirados"""
        # Criar pagamento
        payment = self.processor.create_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount']
        )
        
        # Simular pagamento antigo (modificar diretamente no banco)
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        old_date = (datetime.now() - timedelta(hours=25)).isoformat()
        cursor.execute("UPDATE payments SET created_at = ? WHERE id = ?", (old_date, payment.id))
        conn.commit()
        conn.close()
        
        # Verificar pagamentos expirados
        expired_count = self.processor.check_expired_payments()
        
        self.assertGreaterEqual(expired_count, 1)
        
        # Verificar se o status foi atualizado
        updated_payment = self.processor.get_payment(payment.id)
        self.assertEqual(updated_payment.status, PaymentStatus.EXPIRED)

class TestTANOSIntegrationFixed(unittest.TestCase):
    """Testes corrigidos para integração TANOS"""
    
    def setUp(self):
        """Configuração inicial para cada teste"""
        self.tanos = TANOSIntegration()
    
    def test_tanos_initialization(self):
        """Testa inicialização do TANOS"""
        self.assertIsNotNone(self.tanos.tanos_endpoint)
        self.assertEqual(self.tanos.tanos_endpoint, "http://localhost:3000")
    
    @patch('saas_payments.requests.post')
    def test_verify_atomic_swap_mock(self, mock_post):
        """Testa verificação de swap atômico com mock"""
        # Mock da resposta TANOS
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'success': True,
            'result': True
        }
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # Testar verificação
        test_nostr_event = {
            'kind': 1,
            'content': 'Test payment',
            'tags': [['amount', '29.0']],
            'created_at': int(datetime.now().timestamp())
        }
        
        result = self.tanos.verify_atomic_swap('0x123456789', test_nostr_event)
        self.assertTrue(result)
    
    @patch('saas_payments.requests.post')
    def test_verify_atomic_swap_failure(self, mock_post):
        """Testa falha na verificação de swap atômico"""
        # Mock de resposta de falha
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'success': False,
            'result': False
        }
        mock_response.status_code = 400
        mock_post.return_value = mock_response
        
        # Testar verificação
        test_nostr_event = {'kind': 1, 'content': 'Test'}
        result = self.tanos.verify_atomic_swap('0x123456789', test_nostr_event)
        self.assertFalse(result)
    
    def test_create_payment_proof(self):
        """Testa criação de prova de pagamento"""
        # Criar pagamento mock
        from saas_payments import Payment
        payment = Payment(
            id='test_payment_123',
            user_id='test_user',
            plan='starter',
            amount=29.0,
            currency='USDT',
            wallet_address='0x123',
            tx_hash='0x456789',
            status=PaymentStatus.CONFIRMED,
            created_at=datetime.now(),
            confirmed_at=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=24)
        )
        
        # Criar prova
        proof = self.tanos.create_payment_proof(payment)
        
        self.assertIsNotNone(proof)
        self.assertIn('payment_id', proof)
        self.assertEqual(proof['payment_id'], payment.id)
        self.assertIn('proof', proof)
        self.assertIn('tx_hash', proof)
    
    def test_create_payment_proof_no_tx_hash(self):
        """Testa criação de prova sem hash de transação"""
        from saas_payments import Payment
        payment = Payment(
            id='test_payment_no_tx',
            user_id='test_user',
            plan='starter',
            amount=29.0,
            currency='USDT',
            wallet_address='0x123',
            tx_hash=None,  # Sem hash de transação
            status=PaymentStatus.PENDING,
            created_at=datetime.now(),
            confirmed_at=None,
            expires_at=datetime.now() + timedelta(hours=24)
        )
        
        # Criar prova
        proof = self.tanos.create_payment_proof(payment)
        
        self.assertIsNotNone(proof)
        self.assertIn('error', proof)

class TestPaymentSystemIntegrationFixed(unittest.TestCase):
    """Testes de integração corrigidos para o sistema de pagamentos"""
    
    def setUp(self):
        """Configuração inicial para testes de integração"""
        # Criar banco de dados temporário
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        
        # Inicializar tabelas
        self._init_test_database()
        
        self.test_wallet = "******************************************"
        self.processor = TetherPaymentProcessor(self.test_wallet, db_path=self.test_db.name)
        self.tanos = TANOSIntegration()
    
    def _init_test_database(self):
        """Inicializa banco de dados de teste"""
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id TEXT PRIMARY KEY,
                user_id TEXT,
                plan TEXT,
                amount REAL,
                currency TEXT,
                tx_hash TEXT,
                status TEXT,
                created_at TEXT,
                confirmed_at TEXT
            )
        """)
        
        conn.commit()
        conn.close()
    
    def tearDown(self):
        """Limpeza após testes de integração"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)
    
    def test_complete_payment_workflow(self):
        """Testa fluxo completo de pagamento"""
        # 1. Criar solicitação de pagamento
        payment = self.processor.create_payment_request(
            user_id='integration_test_user',
            plan='starter',
            amount=29.0,
            annual=False
        )
        
        self.assertIsNotNone(payment)
        self.assertEqual(payment.status, PaymentStatus.PENDING)
        
        # 2. Gerar QR code
        qr_data = self.processor.generate_payment_qr_code(payment)
        self.assertIsNotNone(qr_data)
        
        # 3. Simular confirmação de pagamento
        test_tx_hash = "0x123456789abcdef"
        self.processor.update_payment_status(payment.id, PaymentStatus.CONFIRMED, test_tx_hash)
        
        # 4. Verificar estado final
        final_payment = self.processor.get_payment(payment.id)
        self.assertEqual(final_payment.status, PaymentStatus.CONFIRMED)
        self.assertEqual(final_payment.tx_hash, test_tx_hash)
        
        # 5. Criar prova TANOS
        proof = self.tanos.create_payment_proof(final_payment)
        self.assertIsNotNone(proof)
        self.assertIn('payment_id', proof)
    
    def test_payment_expiration_workflow(self):
        """Testa fluxo de expiração de pagamento"""
        # 1. Criar pagamento
        payment = self.processor.create_payment_request(
            user_id='expiration_test_user',
            plan='professional',
            amount=79.0
        )
        
        # 2. Simular passagem do tempo (modificar data no banco)
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        old_date = (datetime.now() - timedelta(hours=25)).isoformat()
        cursor.execute("UPDATE payments SET created_at = ? WHERE id = ?", (old_date, payment.id))
        conn.commit()
        conn.close()
        
        # 3. Verificar expiração
        expired_count = self.processor.check_expired_payments()
        self.assertGreater(expired_count, 0)
        
        # 4. Verificar status atualizado
        expired_payment = self.processor.get_payment(payment.id)
        self.assertEqual(expired_payment.status, PaymentStatus.EXPIRED)

if __name__ == '__main__':
    # Configurar suite de testes
    unittest.main(verbosity=2)
