"""
Dashboard de Monitoramento CryptoSignals
Interface web para visualizar métricas em tempo real
"""

from flask import Flask, render_template, jsonify
import json
from datetime import datetime, timedelta
import sqlite3
import sys
import os

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from monitoring import monitor

app = Flask(__name__)

@app.route('/')
def dashboard():
    """Página principal do dashboard"""
    return render_template('monitoring_dashboard.html')

@app.route('/api/metrics')
def get_metrics():
    """API para obter métricas atuais"""
    try:
        metrics = monitor.get_current_metrics()
        health = monitor.get_health_status()
        
        return jsonify({
            'success': True,
            'timestamp': datetime.now().isoformat(),
            'metrics': metrics,
            'health': health
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/history')
def get_history():
    """API para obter histórico de métricas"""
    try:
        # Obter dados das últimas 24 horas
        cutoff_time = (datetime.now() - timedelta(hours=24)).isoformat()
        
        conn = sqlite3.connect(monitor.db_path)
        cursor = conn.cursor()
        
        # Métricas de performance
        cursor.execute("""
            SELECT timestamp, operation, AVG(duration_ms) as avg_duration,
                   COUNT(*) as total_ops,
                   SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_ops
            FROM performance_metrics 
            WHERE timestamp > ?
            GROUP BY timestamp, operation
            ORDER BY timestamp DESC
            LIMIT 100
        """, (cutoff_time,))
        
        performance_data = []
        for row in cursor.fetchall():
            performance_data.append({
                'timestamp': row[0],
                'operation': row[1],
                'avg_duration': row[2],
                'total_ops': row[3],
                'successful_ops': row[4],
                'success_rate': (row[4] / row[3] * 100) if row[3] > 0 else 0
            })
        
        # Saúde do sistema
        cursor.execute("""
            SELECT timestamp, cpu_usage, memory_usage, response_time_avg, error_rate, status
            FROM system_health 
            WHERE timestamp > ?
            ORDER BY timestamp DESC
            LIMIT 50
        """, (cutoff_time,))
        
        health_data = []
        for row in cursor.fetchall():
            health_data.append({
                'timestamp': row[0],
                'cpu_usage': row[1],
                'memory_usage': row[2],
                'response_time_avg': row[3],
                'error_rate': row[4],
                'status': row[5]
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'performance': performance_data,
            'health': health_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/alerts')
def get_alerts():
    """API para obter alertas ativos"""
    try:
        conn = sqlite3.connect(monitor.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT timestamp, alert_type, severity, message
            FROM alerts 
            WHERE resolved = FALSE
            ORDER BY timestamp DESC
            LIMIT 20
        """)
        
        alerts = []
        for row in cursor.fetchall():
            alerts.append({
                'timestamp': row[0],
                'type': row[1],
                'severity': row[2],
                'message': row[3]
            })
        
        conn.close()
        
        return jsonify({
            'success': True,
            'alerts': alerts
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    # Criar diretório de templates se não existir
    os.makedirs('templates', exist_ok=True)
    
    # Criar template HTML
    html_template = """
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals - Monitoramento</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0B1B33 0%, #1a2332 100%);
            color: #F8F7F2;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(212, 160, 23, 0.1);
            padding: 1rem 2rem;
            border-bottom: 2px solid #D4A017;
        }
        
        .header h1 {
            color: #D4A017;
            font-size: 2rem;
            font-weight: 700;
        }
        
        .dashboard {
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .card {
            background: rgba(248, 247, 242, 0.05);
            border: 1px solid rgba(212, 160, 23, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
        }
        
        .card h3 {
            color: #D4A017;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(212, 160, 23, 0.1);
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #E0E0E0;
        }
        
        .metric-value {
            color: #D4A017;
            font-weight: 600;
        }
        
        .status-healthy { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-critical { color: #F44336; }
        
        .alert {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid #F44336;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .alert-critical { border-color: #F44336; }
        .alert-warning { border-color: #FF9800; }
        
        .timestamp {
            font-size: 0.8rem;
            color: #E0E0E0;
            opacity: 0.7;
        }
        
        .refresh-btn {
            background: #D4A017;
            color: #0B1B33;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin-top: 1rem;
        }
        
        .refresh-btn:hover {
            background: #E5B028;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 CryptoSignals - Monitoramento</h1>
        <p>Dashboard de Performance e Saúde do Sistema</p>
    </div>
    
    <div class="dashboard">
        <div class="card">
            <h3>📊 Métricas Atuais</h3>
            <div id="current-metrics">
                <div class="metric">
                    <span class="metric-label">Carregando...</span>
                    <span class="metric-value">⏳</span>
                </div>
            </div>
            <button class="refresh-btn" onclick="loadMetrics()">🔄 Atualizar</button>
        </div>
        
        <div class="card">
            <h3>🏥 Saúde do Sistema</h3>
            <div id="system-health">
                <div class="metric">
                    <span class="metric-label">Carregando...</span>
                    <span class="metric-value">⏳</span>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>🚨 Alertas Ativos</h3>
            <div id="alerts">
                <div class="alert">
                    <p>Carregando alertas...</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <h3>📈 Estatísticas</h3>
            <div id="statistics">
                <div class="metric">
                    <span class="metric-label">Última atualização</span>
                    <span class="metric-value" id="last-update">-</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function loadMetrics() {
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateCurrentMetrics(data.metrics);
                        updateSystemHealth(data.health);
                        document.getElementById('last-update').textContent = 
                            new Date(data.timestamp).toLocaleTimeString();
                    }
                })
                .catch(error => console.error('Erro ao carregar métricas:', error));
        }
        
        function updateCurrentMetrics(metrics) {
            const container = document.getElementById('current-metrics');
            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Operações Totais</span>
                    <span class="metric-value">${metrics.total_operations}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Taxa de Sucesso</span>
                    <span class="metric-value">${metrics.success_rate.toFixed(1)}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Tempo Médio</span>
                    <span class="metric-value">${metrics.avg_response_time.toFixed(2)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Ops/Minuto</span>
                    <span class="metric-value">${metrics.operations_per_minute.toFixed(1)}</span>
                </div>
            `;
        }
        
        function updateSystemHealth(health) {
            const container = document.getElementById('system-health');
            const statusClass = `status-${health.status}`;
            
            container.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Status</span>
                    <span class="metric-value ${statusClass}">${health.status?.toUpperCase() || 'UNKNOWN'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">CPU</span>
                    <span class="metric-value">${health.cpu_usage?.toFixed(1) || 0}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memória</span>
                    <span class="metric-value">${health.memory_usage?.toFixed(1) || 0}%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Conexões</span>
                    <span class="metric-value">${health.active_connections || 0}</span>
                </div>
            `;
        }
        
        function loadAlerts() {
            fetch('/api/alerts')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAlerts(data.alerts);
                    }
                })
                .catch(error => console.error('Erro ao carregar alertas:', error));
        }
        
        function updateAlerts(alerts) {
            const container = document.getElementById('alerts');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p style="color: #4CAF50;">✅ Nenhum alerta ativo</p>';
                return;
            }
            
            container.innerHTML = alerts.map(alert => `
                <div class="alert alert-${alert.severity}">
                    <strong>${alert.type.toUpperCase()}</strong>
                    <p>${alert.message}</p>
                    <div class="timestamp">${new Date(alert.timestamp).toLocaleString()}</div>
                </div>
            `).join('');
        }
        
        // Carregar dados iniciais
        loadMetrics();
        loadAlerts();
        
        // Atualizar automaticamente a cada 30 segundos
        setInterval(() => {
            loadMetrics();
            loadAlerts();
        }, 30000);
    </script>
</body>
</html>
    """
    
    # Salvar template
    with open('templates/monitoring_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    print("🚀 Iniciando Dashboard de Monitoramento...")
    print("📊 Acesse: http://localhost:5001")
    print("⚡ Atualizações automáticas a cada 30 segundos")
    
    app.run(host='0.0.0.0', port=5001, debug=True)
