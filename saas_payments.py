"""
Sistema de Pagamentos SaaS Multi-Rede
Suporte para Ethereum, BEP20 (Binance Smart Chain), Polygon e métodos tradicionais
Integração com TANOS para verificação de transações
"""

import requests
import json
import time
import secrets
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum
import sqlite3
import hashlib

class PaymentStatus(Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    FAILED = "failed"
    EXPIRED = "expired"

class PaymentNetwork(Enum):
    ETHEREUM = "ethereum"
    BSC = "bsc"  # Binance Smart Chain
    POLYGON = "polygon"
    TRADITIONAL = "traditional"

class PaymentMethod(Enum):
    # Crypto
    USDT_ETH = "usdt_eth"
    USDC_ETH = "usdc_eth"
    DAI_ETH = "dai_eth"
    USDT_BSC = "usdt_bsc"
    BUSD_BSC = "busd_bsc"
    BNB_BSC = "bnb_bsc"
    USDT_POLYGON = "usdt_polygon"
    USDC_POLYGON = "usdc_polygon"
    MATIC_POLYGON = "matic_polygon"
    # Traditional
    CREDIT_CARD = "credit_card"
    PAYPAL = "paypal"
    BANK_TRANSFER = "bank_transfer"

@dataclass
class Payment:
    id: str
    user_id: str
    plan: str
    amount: float
    currency: str
    network: PaymentNetwork
    payment_method: PaymentMethod
    wallet_address: str
    tx_hash: Optional[str]
    status: PaymentStatus
    created_at: datetime
    confirmed_at: Optional[datetime]
    expires_at: datetime
    metadata: Optional[Dict] = None

# Network configurations
NETWORK_CONFIG = {
    PaymentNetwork.ETHEREUM: {
        "rpc_url": "https://mainnet.infura.io/v3/YOUR_INFURA_KEY",
        "explorer_api": "https://api.etherscan.io/api",
        "api_key_param": "apikey",
        "contracts": {
            "USDT": "******************************************",
            "USDC": "******************************************",
            "DAI": "******************************************"
        }
    },
    PaymentNetwork.BSC: {
        "rpc_url": "https://bsc-dataseed1.binance.org/",
        "explorer_api": "https://api.bscscan.com/api",
        "api_key_param": "apikey",
        "contracts": {
            "USDT": "******************************************",
            "BUSD": "******************************************",
            "BNB": "******************************************"  # Native token
        }
    },
    PaymentNetwork.POLYGON: {
        "rpc_url": "https://polygon-rpc.com/",
        "explorer_api": "https://api.polygonscan.com/api",
        "api_key_param": "apikey",
        "contracts": {
            "USDT": "******************************************",
            "USDC": "******************************************",
            "MATIC": "******************************************"  # Native token
        }
    }
}

class MultiNetworkPaymentProcessor:
    """Processador de pagamentos multi-rede avançado"""

    def __init__(self, wallet_addresses: Dict[str, str], db_path: str = "saas_users.db"):
        """
        wallet_addresses: {
            'ethereum': '0x...',
            'bsc': '0x...',
            'polygon': '0x...'
        }
        """
        self.wallet_addresses = wallet_addresses
        self.db_path = db_path
        self.api_keys = {
            'etherscan': 'YOUR_ETHERSCAN_API_KEY',
            'bscscan': 'YOUR_BSCSCAN_API_KEY',
            'polygonscan': 'YOUR_POLYGONSCAN_API_KEY'
        }
        self.init_database()

    def init_database(self):
        """Inicializa tabelas do banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Atualizar tabela de pagamentos para suportar múltiplas redes
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments_v2 (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                plan TEXT NOT NULL,
                amount REAL NOT NULL,
                currency TEXT NOT NULL,
                network TEXT NOT NULL,
                payment_method TEXT NOT NULL,
                wallet_address TEXT NOT NULL,
                tx_hash TEXT,
                status TEXT NOT NULL,
                created_at TEXT NOT NULL,
                confirmed_at TEXT,
                expires_at TEXT NOT NULL,
                metadata TEXT
            )
        """)

        conn.commit()
        conn.close()

    def create_payment_request(self, user_id: str, plan: str, amount: float,
                             payment_method: PaymentMethod, annual: bool = False) -> Payment:
        """Cria uma solicitação de pagamento para qualquer rede"""
        payment_id = secrets.token_urlsafe(16)

        # Determinar rede e moeda baseado no método de pagamento
        network, currency = self._get_network_and_currency(payment_method)
        wallet_address = self.wallet_addresses.get(network.value, "")

        payment = Payment(
            id=payment_id,
            user_id=user_id,
            plan=plan,
            amount=amount,
            currency=currency,
            network=network,
            payment_method=payment_method,
            wallet_address=wallet_address,
            tx_hash=None,
            status=PaymentStatus.PENDING,
            created_at=datetime.now(),
            confirmed_at=None,
            expires_at=datetime.now() + timedelta(hours=24),
            metadata={"annual": annual}
        )

        # Salvar no banco
        self.save_payment(payment)
        return payment

    def _get_network_and_currency(self, payment_method: PaymentMethod) -> tuple:
        """Determina rede e moeda baseado no método de pagamento"""
        method_mapping = {
            PaymentMethod.USDT_ETH: (PaymentNetwork.ETHEREUM, "USDT"),
            PaymentMethod.USDC_ETH: (PaymentNetwork.ETHEREUM, "USDC"),
            PaymentMethod.DAI_ETH: (PaymentNetwork.ETHEREUM, "DAI"),
            PaymentMethod.USDT_BSC: (PaymentNetwork.BSC, "USDT"),
            PaymentMethod.BUSD_BSC: (PaymentNetwork.BSC, "BUSD"),
            PaymentMethod.BNB_BSC: (PaymentNetwork.BSC, "BNB"),
            PaymentMethod.USDT_POLYGON: (PaymentNetwork.POLYGON, "USDT"),
            PaymentMethod.USDC_POLYGON: (PaymentNetwork.POLYGON, "USDC"),
            PaymentMethod.MATIC_POLYGON: (PaymentNetwork.POLYGON, "MATIC"),
            PaymentMethod.CREDIT_CARD: (PaymentNetwork.TRADITIONAL, "USD"),
            PaymentMethod.PAYPAL: (PaymentNetwork.TRADITIONAL, "USD"),
            PaymentMethod.BANK_TRANSFER: (PaymentNetwork.TRADITIONAL, "USD"),
        }
        return method_mapping.get(payment_method, (PaymentNetwork.ETHEREUM, "USDT"))

    def save_payment(self, payment: Payment):
        """Salva o pagamento no banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO payments_v2 (
                id, user_id, plan, amount, currency, network, payment_method,
                wallet_address, tx_hash, status, created_at, confirmed_at, expires_at, metadata
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            payment.id, payment.user_id, payment.plan, payment.amount,
            payment.currency, payment.network.value, payment.payment_method.value,
            payment.wallet_address, payment.tx_hash, payment.status.value,
            payment.created_at.isoformat(),
            payment.confirmed_at.isoformat() if payment.confirmed_at else None,
            payment.expires_at.isoformat(),
            json.dumps(payment.metadata) if payment.metadata else None
        ))

        conn.commit()
        conn.close()

    def verify_payment(self, payment_id: str, tx_hash: str) -> bool:
        """Verifica se um pagamento foi realizado em qualquer rede"""
        try:
            payment = self.get_payment(payment_id)
            if not payment:
                return False

            if payment.status == PaymentStatus.CONFIRMED:
                return True

            if datetime.now() > payment.expires_at:
                self.update_payment_status(payment_id, PaymentStatus.EXPIRED)
                return False

            # Verificar transação baseado na rede
            if payment.network == PaymentNetwork.TRADITIONAL:
                return self._verify_traditional_payment(payment, tx_hash)
            else:
                return self._verify_crypto_transaction(payment, tx_hash)

        except Exception as e:
            print(f"Erro ao verificar pagamento: {e}")
            return False

    def _verify_crypto_transaction(self, payment: Payment, tx_hash: str) -> bool:
        """Verifica transação em redes blockchain"""
        try:
            network_config = NETWORK_CONFIG.get(payment.network)
            if not network_config:
                return False

            api_url = network_config["explorer_api"]
            api_key = self._get_api_key_for_network(payment.network)

            # Verificar transação
            params = {
                "module": "proxy",
                "action": "eth_getTransactionByHash",
                "txhash": tx_hash,
                network_config["api_key_param"]: api_key
            }

            response = requests.get(api_url, params=params)
            data = response.json()

            if "result" not in data or not data["result"]:
                return False

            tx = data["result"]

            # Verificar se é para o contrato correto (para tokens ERC20/BEP20)
            if payment.currency in network_config["contracts"]:
                contract_address = network_config["contracts"][payment.currency]
                if contract_address != "******************************************":
                    if tx["to"].lower() != contract_address.lower():
                        return False

            # Verificar receipt da transação
            receipt_params = {
                "module": "proxy",
                "action": "eth_getTransactionReceipt",
                "txhash": tx_hash,
                network_config["api_key_param"]: api_key
            }

            receipt_response = requests.get(api_url, params=receipt_params)
            receipt_data = receipt_response.json()

            if "result" not in receipt_data or not receipt_data["result"]:
                return False

            receipt = receipt_data["result"]

            # Verificar se a transação foi bem-sucedida
            if receipt["status"] != "0x1":
                return False

            # Se chegou até aqui, a transação é válida
            self.update_payment_status(payment.id, PaymentStatus.CONFIRMED, tx_hash)
            return True

        except Exception as e:
            print(f"Erro ao verificar transação crypto: {e}")
            return False

    def _verify_traditional_payment(self, payment: Payment, reference: str) -> bool:
        """Verifica pagamento tradicional (cartão, PayPal, etc.)"""
        # Implementar integração com processadores de pagamento tradicionais
        # Por enquanto, retorna True para demonstração
        self.update_payment_status(payment.id, PaymentStatus.CONFIRMED, reference)
        return True

    def _get_api_key_for_network(self, network: PaymentNetwork) -> str:
        """Retorna a chave API apropriada para a rede"""
        key_mapping = {
            PaymentNetwork.ETHEREUM: self.api_keys.get('etherscan', ''),
            PaymentNetwork.BSC: self.api_keys.get('bscscan', ''),
            PaymentNetwork.POLYGON: self.api_keys.get('polygonscan', '')
        }
        return key_mapping.get(network, '')

    def get_payment(self, payment_id: str) -> Optional[Payment]:
        """Busca um pagamento pelo ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM payments_v2 WHERE id = ?", (payment_id,))
        row = cursor.fetchone()
        conn.close()

        if row:
            metadata = json.loads(row[13]) if row[13] else None
            return Payment(
                id=row[0],
                user_id=row[1],
                plan=row[2],
                amount=row[3],
                currency=row[4],
                network=PaymentNetwork(row[5]),
                payment_method=PaymentMethod(row[6]),
                wallet_address=row[7],
                tx_hash=row[8],
                status=PaymentStatus(row[9]),
                created_at=datetime.fromisoformat(row[10]),
                confirmed_at=datetime.fromisoformat(row[11]) if row[11] else None,
                expires_at=datetime.fromisoformat(row[12]),
                metadata=metadata
            )

        return None

    def update_payment_status(self, payment_id: str, status: PaymentStatus, tx_hash: str = None):
        """Atualiza o status de um pagamento"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        confirmed_at = datetime.now().isoformat() if status == PaymentStatus.CONFIRMED else None

        cursor.execute("""
            UPDATE payments_v2
            SET status = ?, tx_hash = ?, confirmed_at = ?
            WHERE id = ?
        """, (status.value, tx_hash, confirmed_at, payment_id))

        conn.commit()
        conn.close()

    def get_supported_payment_methods(self) -> Dict[str, List[Dict]]:
        """Retorna métodos de pagamento suportados organizados por categoria"""
        return {
            "crypto": [
                {
                    "method": PaymentMethod.USDT_ETH.value,
                    "name": "USDT (Ethereum)",
                    "network": "Ethereum",
                    "currency": "USDT",
                    "fees": "Baixas a médias",
                    "confirmation_time": "1-5 minutos"
                },
                {
                    "method": PaymentMethod.USDT_BSC.value,
                    "name": "USDT (BEP20)",
                    "network": "Binance Smart Chain",
                    "currency": "USDT",
                    "fees": "Muito baixas",
                    "confirmation_time": "1-3 minutos"
                },
                {
                    "method": PaymentMethod.USDT_POLYGON.value,
                    "name": "USDT (Polygon)",
                    "network": "Polygon",
                    "currency": "USDT",
                    "fees": "Muito baixas",
                    "confirmation_time": "1-2 minutos"
                },
                {
                    "method": PaymentMethod.BUSD_BSC.value,
                    "name": "BUSD (BEP20)",
                    "network": "Binance Smart Chain",
                    "currency": "BUSD",
                    "fees": "Muito baixas",
                    "confirmation_time": "1-3 minutos"
                },
                {
                    "method": PaymentMethod.BNB_BSC.value,
                    "name": "BNB",
                    "network": "Binance Smart Chain",
                    "currency": "BNB",
                    "fees": "Muito baixas",
                    "confirmation_time": "1-3 minutos"
                },
                {
                    "method": PaymentMethod.MATIC_POLYGON.value,
                    "name": "MATIC",
                    "network": "Polygon",
                    "currency": "MATIC",
                    "fees": "Muito baixas",
                    "confirmation_time": "1-2 minutos"
                }
            ],
            "traditional": [
                {
                    "method": PaymentMethod.CREDIT_CARD.value,
                    "name": "Cartão de Crédito",
                    "network": "Traditional",
                    "currency": "USD",
                    "fees": "3-5%",
                    "confirmation_time": "Instantâneo"
                },
                {
                    "method": PaymentMethod.PAYPAL.value,
                    "name": "PayPal",
                    "network": "Traditional",
                    "currency": "USD",
                    "fees": "2.9% + $0.30",
                    "confirmation_time": "Instantâneo"
                }
            ]
        }

    def generate_payment_qr_code(self, payment: Payment) -> str:
        """Gera dados para QR code de pagamento"""
        if payment.network == PaymentNetwork.TRADITIONAL:
            # Para pagamentos tradicionais, retornar link de pagamento
            return json.dumps({
                "type": "traditional_payment",
                "payment_id": payment.id,
                "amount": payment.amount,
                "currency": payment.currency,
                "method": payment.payment_method.value
            })

        # Para crypto, formato padrão
        qr_data = {
            "address": payment.wallet_address,
            "amount": payment.amount,
            "currency": payment.currency,
            "memo": f"CryptoSignals - {payment.plan} - {payment.id}",
            "network": payment.network.value,
            "method": payment.payment_method.value
        }

        return json.dumps(qr_data)


# Manter classe original para compatibilidade
class TetherPaymentProcessor:
    """Processador de pagamentos TETHER USD (compatibilidade)"""

    def __init__(self, wallet_address: str, db_path: str = "saas_users.db"):
        self.wallet_address = wallet_address
        self.db_path = db_path
        self.etherscan_api_key = "YourEtherscanAPIKey"
        self.usdt_contract = "******************************************"

    def create_payment_request(self, user_id: str, plan: str, amount: float, annual: bool = False) -> Payment:
        """Cria uma solicitação de pagamento USDT"""
        payment_id = secrets.token_urlsafe(16)

        payment = Payment(
            id=payment_id,
            user_id=user_id,
            plan=plan,
            amount=amount,
            currency="USDT",
            network=PaymentNetwork.ETHEREUM,
            payment_method=PaymentMethod.USDT_ETH,
            wallet_address=self.wallet_address,
            tx_hash=None,
            status=PaymentStatus.PENDING,
            created_at=datetime.now(),
            confirmed_at=None,
            expires_at=datetime.now() + timedelta(hours=24),
            metadata={"annual": annual}
        )

        self.save_payment(payment)
        return payment

    def save_payment(self, payment: Payment):
        """Salva o pagamento no banco de dados (formato antigo)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("""
            INSERT OR IGNORE INTO payments (id, user_id, plan, amount, currency, tx_hash, status, created_at, confirmed_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            payment.id, payment.user_id, payment.plan, payment.amount,
            payment.currency, payment.tx_hash, payment.status.value,
            payment.created_at.isoformat(),
            payment.confirmed_at.isoformat() if payment.confirmed_at else None
        ))

        conn.commit()
        conn.close()

    def update_payment_status(self, payment_id: str, status: PaymentStatus, tx_hash: str = None):
        """Atualiza o status de um pagamento"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        confirmed_at = datetime.now().isoformat() if status == PaymentStatus.CONFIRMED else None

        cursor.execute("""
            UPDATE payments
            SET status = ?, tx_hash = ?, confirmed_at = ?
            WHERE id = ?
        """, (status.value, tx_hash, confirmed_at, payment_id))

        conn.commit()
        conn.close()

    def verify_payment(self, payment_id: str, tx_hash: str) -> bool:
        """Verifica se um pagamento foi realizado"""
        try:
            # Buscar informações do pagamento
            payment = self.get_payment(payment_id)
            if not payment:
                return False

            # Verificar se o pagamento já foi confirmado
            if payment.status == PaymentStatus.CONFIRMED:
                return True

            # Verificar se o pagamento expirou
            if datetime.now() > payment.expires_at:
                self.update_payment_status(payment_id, PaymentStatus.EXPIRED)
                return False

            # Verificar a transação na blockchain
            if self.verify_usdt_transaction(tx_hash, payment.amount):
                self.update_payment_status(payment_id, PaymentStatus.CONFIRMED, tx_hash)
                return True

            return False

        except Exception as e:
            print(f"Erro ao verificar pagamento: {e}")
            return False

    def verify_usdt_transaction(self, tx_hash: str, expected_amount: float) -> bool:
        """Verifica uma transação USDT na blockchain Ethereum"""
        try:
            # URL da API Etherscan para verificar transação
            url = f"https://api.etherscan.io/api"
            params = {
                "module": "proxy",
                "action": "eth_getTransactionByHash",
                "txhash": tx_hash,
                "apikey": self.etherscan_api_key
            }

            response = requests.get(url, params=params)
            data = response.json()

            if "result" not in data or not data["result"]:
                return False

            tx = data["result"]

            # Verificar se a transação é para o contrato USDT
            if tx["to"].lower() != self.usdt_contract.lower():
                return False

            # Decodificar os dados da transação para verificar o destinatário e valor
            # Isso requer análise dos dados de input da transação
            # Por simplicidade, vamos assumir que a verificação passou

            # Verificar se a transação foi confirmada
            receipt_url = f"https://api.etherscan.io/api"
            receipt_params = {
                "module": "proxy",
                "action": "eth_getTransactionReceipt",
                "txhash": tx_hash,
                "apikey": self.etherscan_api_key
            }

            receipt_response = requests.get(receipt_url, params=receipt_params)
            receipt_data = receipt_response.json()

            if "result" not in receipt_data or not receipt_data["result"]:
                return False

            receipt = receipt_data["result"]

            # Verificar se a transação foi bem-sucedida
            if receipt["status"] != "0x1":
                return False

            # Aqui você implementaria a verificação detalhada do valor e destinatário
            # Por enquanto, retornamos True se a transação existe e foi confirmada
            return True

        except Exception as e:
            print(f"Erro ao verificar transação USDT: {e}")
            return False

    def get_payment(self, payment_id: str) -> Optional[Payment]:
        """Busca um pagamento pelo ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM payments WHERE id = ?", (payment_id,))
        row = cursor.fetchone()
        conn.close()

        if row:
            return Payment(
                id=row[0],
                user_id=row[1],
                plan=row[2],
                amount=row[3],
                currency=row[4],
                network=PaymentNetwork.ETHEREUM,
                payment_method=PaymentMethod.USDT_ETH,
                wallet_address=self.wallet_address,
                tx_hash=row[5],
                status=PaymentStatus(row[6]),
                created_at=datetime.fromisoformat(row[7]),
                confirmed_at=datetime.fromisoformat(row[8]) if row[8] else None,
                expires_at=datetime.fromisoformat(row[7]) + timedelta(hours=24),
                metadata=None
            )

        return None

    def get_user_payments(self, user_id: str) -> List[Payment]:
        """Busca todos os pagamentos de um usuário"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM payments WHERE user_id = ? ORDER BY created_at DESC", (user_id,))
        rows = cursor.fetchall()
        conn.close()

        payments = []
        for row in rows:
            payments.append(Payment(
                id=row[0],
                user_id=row[1],
                plan=row[2],
                amount=row[3],
                currency=row[4],
                network=PaymentNetwork.ETHEREUM,
                payment_method=PaymentMethod.USDT_ETH,
                wallet_address=self.wallet_address,
                tx_hash=row[5],
                status=PaymentStatus(row[6]),
                created_at=datetime.fromisoformat(row[7]),
                confirmed_at=datetime.fromisoformat(row[8]) if row[8] else None,
                expires_at=datetime.fromisoformat(row[7]) + timedelta(hours=24),
                metadata=None
            ))

        return payments

    def process_subscription_upgrade(self, user_id: str, new_plan: str, payment_id: str) -> bool:
        """Processa o upgrade de assinatura após pagamento confirmado"""
        try:
            payment = self.get_payment(payment_id)
            if not payment or payment.status != PaymentStatus.CONFIRMED:
                return False

            # Atualizar plano do usuário
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Calcular nova data de expiração
            if new_plan == "starter" or new_plan == "professional":
                # 1 mês para pagamento mensal, 12 meses para anual
                months = 12 if payment.amount > 100 else 1  # Assumindo que valores > 100 são anuais
                new_expiry = datetime.now() + timedelta(days=30 * months)
            else:
                new_expiry = datetime.now() + timedelta(days=30)

            cursor.execute("""
                UPDATE users
                SET plan = ?, subscription_end = ?
                WHERE id = ?
            """, (new_plan, new_expiry.isoformat(), user_id))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            print(f"Erro ao processar upgrade: {e}")
            return False

    def generate_payment_qr_code(self, payment: Payment) -> str:
        """Gera dados para QR code de pagamento"""
        # Formato para QR code de pagamento crypto
        qr_data = {
            "address": payment.wallet_address,
            "amount": payment.amount,
            "currency": payment.currency,
            "memo": f"CryptoSignals - {payment.plan} - {payment.id}",
            "network": "ethereum"
        }

        return json.dumps(qr_data)

    def check_expired_payments(self):
        """Verifica e marca pagamentos expirados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Buscar pagamentos pendentes que expiraram
        now = datetime.now()
        cursor.execute("""
            SELECT id FROM payments
            WHERE status = 'pending' AND created_at < ?
        """, ((now - timedelta(hours=24)).isoformat(),))

        expired_payments = cursor.fetchall()

        # Marcar como expirados
        for payment_id in expired_payments:
            cursor.execute("""
                UPDATE payments SET status = 'expired' WHERE id = ?
            """, (payment_id[0],))

        conn.commit()
        conn.close()

        return len(expired_payments)

class TANOSIntegration:
    """Integração com TANOS para verificação de pagamentos"""

    def __init__(self, tanos_endpoint: str = "http://localhost:3000"):
        self.tanos_endpoint = tanos_endpoint

    def verify_atomic_swap(self, tx_hash: str, nostr_event: Dict) -> bool:
        """Verifica uma troca atômica usando TANOS"""
        try:
            url = f"{self.tanos_endpoint}/v1/audit"

            payload = {
                "txHex": tx_hash,
                "nostrEvent": nostr_event
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer YOUR_TANOS_API_KEY"
            }

            response = requests.post(url, json=payload, headers=headers)

            if response.status_code == 200:
                result = response.json()
                return result.get("success", False) and result.get("result", False)

            return False

        except Exception as e:
            print(f"Erro na verificação TANOS: {e}")
            return False

    def create_payment_proof(self, payment: Payment) -> Dict:
        """Cria uma prova de pagamento usando TANOS"""
        try:
            # Criar evento Nostr para o pagamento
            nostr_event = {
                "kind": 1,
                "content": f"Payment for CryptoSignals {payment.plan} plan",
                "tags": [
                    ["amount", str(payment.amount)],
                    ["currency", payment.currency],
                    ["plan", payment.plan],
                    ["payment_id", payment.id]
                ],
                "created_at": int(payment.created_at.timestamp())
            }

            # Verificar com TANOS
            if payment.tx_hash:
                verified = self.verify_atomic_swap(payment.tx_hash, nostr_event)

                return {
                    "payment_id": payment.id,
                    "verified": verified,
                    "proof": nostr_event,
                    "tx_hash": payment.tx_hash
                }

            return {"error": "No transaction hash available"}

        except Exception as e:
            return {"error": str(e)}
