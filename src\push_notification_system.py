"""
Sistema de Notificações Push Avançado - CryptoSignals
Notificações multi-canal: Email, SMS, Push, Webhook
"""

import smtplib
import json
import requests
import threading
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import sqlite3
from collections import deque
import uuid

class NotificationChannel(Enum):
    """Canais de notificação"""
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"
    WEBHOOK = "webhook"
    IN_APP = "in_app"
    TELEGRAM = "telegram"
    DISCORD = "discord"
    SLACK = "slack"

class NotificationPriority(Enum):
    """Prioridades de notificação"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"

class NotificationStatus(Enum):
    """Status de notificação"""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRY = "retry"

@dataclass
class NotificationTemplate:
    """Template de notificação"""
    id: str
    name: str
    channel: NotificationChannel
    subject_template: str
    body_template: str
    variables: List[str]
    is_html: bool = False

@dataclass
class NotificationRequest:
    """Solicitação de notificação"""
    id: str
    user_id: str
    channel: NotificationChannel
    priority: NotificationPriority
    template_id: Optional[str]
    subject: str
    message: str
    data: Dict[str, Any]
    recipient: str
    scheduled_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class NotificationResult:
    """Resultado de envio de notificação"""
    notification_id: str
    status: NotificationStatus
    sent_at: Optional[datetime]
    delivered_at: Optional[datetime]
    error_message: Optional[str]
    provider_response: Optional[Dict[str, Any]]

class EmailProvider:
    """Provedor de email"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
    
    def send_email(self, to_email: str, subject: str, body: str, is_html: bool = False) -> bool:
        """Envia email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = to_email
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html' if is_html else 'plain'))
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            
            text = msg.as_string()
            server.sendmail(self.username, to_email, text)
            server.quit()
            
            return True
            
        except Exception as e:
            print(f"❌ Erro ao enviar email: {e}")
            return False

class WebhookProvider:
    """Provedor de webhook"""
    
    def __init__(self, default_timeout: int = 30):
        self.default_timeout = default_timeout
    
    def send_webhook(self, url: str, data: Dict[str, Any], headers: Dict[str, str] = None) -> bool:
        """Envia webhook"""
        try:
            headers = headers or {'Content-Type': 'application/json'}
            
            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=self.default_timeout
            )
            
            return response.status_code < 400
            
        except Exception as e:
            print(f"❌ Erro ao enviar webhook: {e}")
            return False

class TelegramProvider:
    """Provedor do Telegram"""
    
    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.api_url = f"https://api.telegram.org/bot{bot_token}"
    
    def send_message(self, chat_id: str, message: str, parse_mode: str = "HTML") -> bool:
        """Envia mensagem no Telegram"""
        try:
            url = f"{self.api_url}/sendMessage"
            data = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, json=data, timeout=30)
            return response.status_code == 200
            
        except Exception as e:
            print(f"❌ Erro ao enviar mensagem Telegram: {e}")
            return False

class DiscordProvider:
    """Provedor do Discord"""
    
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
    
    def send_message(self, message: str, username: str = "CryptoSignals", avatar_url: str = None) -> bool:
        """Envia mensagem no Discord"""
        try:
            data = {
                'content': message,
                'username': username
            }
            
            if avatar_url:
                data['avatar_url'] = avatar_url
            
            response = requests.post(self.webhook_url, json=data, timeout=30)
            return response.status_code == 204
            
        except Exception as e:
            print(f"❌ Erro ao enviar mensagem Discord: {e}")
            return False

class PushNotificationSystem:
    """Sistema principal de notificações push"""
    
    def __init__(self, db_path: str = "notifications.db"):
        self.db_path = db_path
        self.providers = {}
        self.templates = {}
        self.notification_queue = deque()
        self.processing_queue = deque()
        self.results = {}
        
        # Configurações padrão
        self.config = {
            'max_queue_size': 10000,
            'batch_size': 50,
            'retry_delay': 300,  # 5 minutos
            'max_retries': 3,
            'cleanup_days': 30
        }
        
        # Inicializar banco de dados
        self._init_database()
        
        # Carregar templates padrão
        self._load_default_templates()
        
        # Iniciar workers
        self._start_workers()
    
    def _init_database(self):
        """Inicializa banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabela de notificações
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                channel TEXT NOT NULL,
                priority TEXT NOT NULL,
                subject TEXT NOT NULL,
                message TEXT NOT NULL,
                recipient TEXT NOT NULL,
                status TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL,
                scheduled_at TIMESTAMP,
                sent_at TIMESTAMP,
                delivered_at TIMESTAMP,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                data TEXT
            )
        ''')
        
        # Tabela de templates
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS templates (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                channel TEXT NOT NULL,
                subject_template TEXT NOT NULL,
                body_template TEXT NOT NULL,
                variables TEXT NOT NULL,
                is_html BOOLEAN DEFAULT FALSE
            )
        ''')
        
        # Índices
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_scheduled ON notifications(scheduled_at)')
        
        conn.commit()
        conn.close()
    
    def _load_default_templates(self):
        """Carrega templates padrão"""
        default_templates = [
            NotificationTemplate(
                id="price_alert",
                name="Alerta de Preço",
                channel=NotificationChannel.EMAIL,
                subject_template="🚨 Alerta de Preço: {symbol}",
                body_template="""
                <h2>Alerta de Preço Ativado</h2>
                <p>O preço de <strong>{symbol}</strong> atingiu <strong>${price}</strong></p>
                <p>Condição: {condition}</p>
                <p>Variação: {change}%</p>
                <p>Horário: {timestamp}</p>
                """,
                variables=["symbol", "price", "condition", "change", "timestamp"],
                is_html=True
            ),
            NotificationTemplate(
                id="prediction_ready",
                name="Predição Pronta",
                channel=NotificationChannel.IN_APP,
                subject_template="🔮 Nova Predição: {symbol}",
                body_template="Predição para {symbol}: {direction} ({confidence}% confiança)",
                variables=["symbol", "direction", "confidence"]
            ),
            NotificationTemplate(
                id="system_alert",
                name="Alerta do Sistema",
                channel=NotificationChannel.EMAIL,
                subject_template="⚠️ Alerta do Sistema",
                body_template="""
                <h2>Alerta do Sistema</h2>
                <p><strong>Tipo:</strong> {alert_type}</p>
                <p><strong>Mensagem:</strong> {message}</p>
                <p><strong>Severidade:</strong> {severity}</p>
                <p><strong>Horário:</strong> {timestamp}</p>
                """,
                variables=["alert_type", "message", "severity", "timestamp"],
                is_html=True
            )
        ]
        
        for template in default_templates:
            self.templates[template.id] = template
    
    def configure_provider(self, channel: NotificationChannel, provider_config: Dict[str, Any]):
        """Configura provedor de notificação"""
        if channel == NotificationChannel.EMAIL:
            self.providers[channel] = EmailProvider(
                smtp_server=provider_config['smtp_server'],
                smtp_port=provider_config['smtp_port'],
                username=provider_config['username'],
                password=provider_config['password']
            )
        elif channel == NotificationChannel.WEBHOOK:
            self.providers[channel] = WebhookProvider(
                default_timeout=provider_config.get('timeout', 30)
            )
        elif channel == NotificationChannel.TELEGRAM:
            self.providers[channel] = TelegramProvider(
                bot_token=provider_config['bot_token']
            )
        elif channel == NotificationChannel.DISCORD:
            self.providers[channel] = DiscordProvider(
                webhook_url=provider_config['webhook_url']
            )
    
    def send_notification(self, user_id: str, channel: NotificationChannel,
                         subject: str, message: str, recipient: str,
                         priority: NotificationPriority = NotificationPriority.NORMAL,
                         template_id: Optional[str] = None,
                         template_data: Dict[str, Any] = None,
                         scheduled_at: Optional[datetime] = None) -> str:
        """Envia notificação"""
        
        notification_id = str(uuid.uuid4())
        
        # Aplicar template se especificado
        if template_id and template_id in self.templates:
            template = self.templates[template_id]
            template_data = template_data or {}
            
            try:
                subject = template.subject_template.format(**template_data)
                message = template.body_template.format(**template_data)
            except KeyError as e:
                print(f"❌ Variável de template não encontrada: {e}")
        
        # Criar solicitação de notificação
        notification = NotificationRequest(
            id=notification_id,
            user_id=user_id,
            channel=channel,
            priority=priority,
            template_id=template_id,
            subject=subject,
            message=message,
            data=template_data or {},
            recipient=recipient,
            scheduled_at=scheduled_at
        )
        
        # Adicionar à fila
        if len(self.notification_queue) < self.config['max_queue_size']:
            self.notification_queue.append(notification)
            
            # Salvar no banco
            self._save_notification(notification)
            
            print(f"📧 Notificação enfileirada: {notification_id}")
            return notification_id
        else:
            print(f"❌ Fila de notificações cheia")
            return None
    
    def _save_notification(self, notification: NotificationRequest):
        """Salva notificação no banco"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO notifications 
            (id, user_id, channel, priority, subject, message, recipient, status, 
             created_at, scheduled_at, data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            notification.id,
            notification.user_id,
            notification.channel.value,
            notification.priority.value,
            notification.subject,
            notification.message,
            notification.recipient,
            NotificationStatus.PENDING.value,
            notification.created_at,
            notification.scheduled_at,
            json.dumps(notification.data)
        ))
        
        conn.commit()
        conn.close()
    
    def _process_notification(self, notification: NotificationRequest) -> NotificationResult:
        """Processa uma notificação"""
        try:
            # Verificar se é hora de enviar
            if notification.scheduled_at and datetime.now() < notification.scheduled_at:
                return NotificationResult(
                    notification_id=notification.id,
                    status=NotificationStatus.PENDING,
                    sent_at=None,
                    delivered_at=None,
                    error_message="Agendado para o futuro",
                    provider_response=None
                )
            
            # Verificar se expirou
            if notification.expires_at and datetime.now() > notification.expires_at:
                return NotificationResult(
                    notification_id=notification.id,
                    status=NotificationStatus.FAILED,
                    sent_at=None,
                    delivered_at=None,
                    error_message="Notificação expirada",
                    provider_response=None
                )
            
            # Obter provedor
            provider = self.providers.get(notification.channel)
            if not provider:
                return NotificationResult(
                    notification_id=notification.id,
                    status=NotificationStatus.FAILED,
                    sent_at=None,
                    delivered_at=None,
                    error_message=f"Provedor não configurado para {notification.channel.value}",
                    provider_response=None
                )
            
            # Enviar notificação
            success = False
            sent_at = datetime.now()
            
            if notification.channel == NotificationChannel.EMAIL:
                template = self.templates.get(notification.template_id)
                is_html = template.is_html if template else False
                success = provider.send_email(
                    notification.recipient,
                    notification.subject,
                    notification.message,
                    is_html
                )
            elif notification.channel == NotificationChannel.WEBHOOK:
                webhook_data = {
                    'notification_id': notification.id,
                    'user_id': notification.user_id,
                    'subject': notification.subject,
                    'message': notification.message,
                    'data': notification.data,
                    'timestamp': sent_at.isoformat()
                }
                success = provider.send_webhook(notification.recipient, webhook_data)
            elif notification.channel == NotificationChannel.TELEGRAM:
                success = provider.send_message(notification.recipient, notification.message)
            elif notification.channel == NotificationChannel.DISCORD:
                success = provider.send_message(notification.message)
            
            # Criar resultado
            if success:
                status = NotificationStatus.SENT
                error_message = None
            else:
                status = NotificationStatus.FAILED
                error_message = "Falha no envio"
            
            result = NotificationResult(
                notification_id=notification.id,
                status=status,
                sent_at=sent_at if success else None,
                delivered_at=sent_at if success else None,  # Assumir entrega imediata
                error_message=error_message,
                provider_response=None
            )
            
            # Atualizar banco
            self._update_notification_status(notification.id, result)
            
            return result
            
        except Exception as e:
            error_result = NotificationResult(
                notification_id=notification.id,
                status=NotificationStatus.FAILED,
                sent_at=None,
                delivered_at=None,
                error_message=str(e),
                provider_response=None
            )
            
            self._update_notification_status(notification.id, error_result)
            return error_result
    
    def _update_notification_status(self, notification_id: str, result: NotificationResult):
        """Atualiza status da notificação no banco"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE notifications 
            SET status = ?, sent_at = ?, delivered_at = ?, error_message = ?
            WHERE id = ?
        ''', (
            result.status.value,
            result.sent_at,
            result.delivered_at,
            result.error_message,
            notification_id
        ))
        
        conn.commit()
        conn.close()
    
    def _start_workers(self):
        """Inicia workers de processamento"""
        # Worker principal
        main_worker = threading.Thread(target=self._main_worker, daemon=True)
        main_worker.start()
        
        # Worker de retry
        retry_worker = threading.Thread(target=self._retry_worker, daemon=True)
        retry_worker.start()
        
        # Worker de limpeza
        cleanup_worker = threading.Thread(target=self._cleanup_worker, daemon=True)
        cleanup_worker.start()
    
    def _main_worker(self):
        """Worker principal de processamento"""
        while True:
            try:
                # Processar notificações em lote
                batch = []
                for _ in range(min(self.config['batch_size'], len(self.notification_queue))):
                    if self.notification_queue:
                        batch.append(self.notification_queue.popleft())
                
                # Processar cada notificação do lote
                for notification in batch:
                    result = self._process_notification(notification)
                    self.results[notification.id] = result
                    
                    # Se falhou e pode tentar novamente, adicionar à fila de retry
                    if (result.status == NotificationStatus.FAILED and 
                        notification.retry_count < notification.max_retries):
                        notification.retry_count += 1
                        self.processing_queue.append(notification)
                
                # Aguardar se não há notificações
                if not batch:
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ Erro no worker principal: {e}")
                time.sleep(5)
    
    def _retry_worker(self):
        """Worker de retry"""
        while True:
            try:
                # Processar notificações para retry
                retry_notifications = []
                while self.processing_queue:
                    retry_notifications.append(self.processing_queue.popleft())
                
                for notification in retry_notifications:
                    # Aguardar delay de retry
                    time.sleep(self.config['retry_delay'])
                    
                    # Tentar novamente
                    result = self._process_notification(notification)
                    self.results[notification.id] = result
                    
                    # Se ainda falhou e pode tentar novamente
                    if (result.status == NotificationStatus.FAILED and 
                        notification.retry_count < notification.max_retries):
                        notification.retry_count += 1
                        self.processing_queue.append(notification)
                
                time.sleep(60)  # Verificar a cada minuto
                
            except Exception as e:
                print(f"❌ Erro no worker de retry: {e}")
                time.sleep(60)
    
    def _cleanup_worker(self):
        """Worker de limpeza"""
        while True:
            try:
                # Limpar notificações antigas
                cutoff_date = datetime.now() - timedelta(days=self.config['cleanup_days'])
                
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    DELETE FROM notifications 
                    WHERE created_at < ? AND status IN ('sent', 'delivered', 'failed')
                ''', (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                conn.close()
                
                if deleted_count > 0:
                    print(f"🗑️  Limpeza: {deleted_count} notificações antigas removidas")
                
                # Limpar cache de resultados
                old_results = [
                    nid for nid, result in self.results.items()
                    if result.sent_at and (datetime.now() - result.sent_at).days > 1
                ]
                
                for nid in old_results:
                    del self.results[nid]
                
                time.sleep(3600)  # Limpeza a cada hora
                
            except Exception as e:
                print(f"❌ Erro na limpeza: {e}")
                time.sleep(3600)
    
    def get_notification_status(self, notification_id: str) -> Optional[NotificationResult]:
        """Obtém status de uma notificação"""
        return self.results.get(notification_id)
    
    def get_user_notifications(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Obtém notificações de um usuário"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM notifications 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
        ''', (user_id, limit))
        
        columns = [description[0] for description in cursor.description]
        notifications = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        conn.close()
        return notifications
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas do sistema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Estatísticas gerais
        cursor.execute('SELECT COUNT(*) FROM notifications')
        total_notifications = cursor.fetchone()[0]
        
        cursor.execute('SELECT status, COUNT(*) FROM notifications GROUP BY status')
        status_counts = dict(cursor.fetchall())
        
        cursor.execute('''
            SELECT channel, COUNT(*) FROM notifications 
            WHERE created_at > datetime('now', '-24 hours')
            GROUP BY channel
        ''')
        channel_stats_24h = dict(cursor.fetchall())
        
        conn.close()
        
        return {
            'total_notifications': total_notifications,
            'queue_size': len(self.notification_queue),
            'processing_queue_size': len(self.processing_queue),
            'results_cache_size': len(self.results),
            'status_counts': status_counts,
            'channel_stats_24h': channel_stats_24h,
            'configured_providers': list(self.providers.keys()),
            'available_templates': list(self.templates.keys())
        }

# Instância global do sistema de notificações
push_system = PushNotificationSystem()
