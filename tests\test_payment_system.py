"""
Testes Unitários - Sistema de Pagamentos CryptoSignals
Validação do TetherPaymentProcessor e TANOSIntegration
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock
import json

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from saas_payments import TetherPaymentProcessor, TANOSIntegration
from saas_auth import PlanType

class TestTetherPaymentProcessor(unittest.TestCase):
    """Testes para o processador de pagamentos USDT"""
    
    def setUp(self):
        """Configuração inicial para cada teste"""
        self.test_wallet = "******************************************"
        self.processor = TetherPaymentProcessor(self.test_wallet)
        
        # Dados de teste
        self.test_payment_data = {
            'user_id': 'test_user_123',
            'plan': PlanType.STARTER,
            'amount': 29.0,
            'annual': False
        }
    
    def test_wallet_initialization(self):
        """Testa inicialização com wallet address"""
        self.assertEqual(self.processor.wallet_address, self.test_wallet)
        self.assertIsNotNone(self.processor.web3)
    
    def test_generate_payment_request(self):
        """Testa geração de solicitação de pagamento"""
        payment_request = self.processor.generate_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            annual=self.test_payment_data['annual']
        )
        
        self.assertIsNotNone(payment_request)
        self.assertIn('payment_id', payment_request)
        self.assertIn('wallet_address', payment_request)
        self.assertIn('amount', payment_request)
        self.assertIn('qr_code', payment_request)
        self.assertEqual(payment_request['amount'], self.test_payment_data['amount'])
    
    def test_payment_request_validation(self):
        """Testa validação de dados de pagamento"""
        # Teste com dados válidos
        valid_request = self.processor.generate_payment_request(
            user_id='valid_user',
            plan=PlanType.PROFESSIONAL,
            amount=79.0,
            annual=False
        )
        self.assertIsNotNone(valid_request)
        
        # Teste com amount inválido
        with self.assertRaises(ValueError):
            self.processor.generate_payment_request(
                user_id='test_user',
                plan=PlanType.FREE,
                amount=-10.0,  # Valor negativo
                annual=False
            )
    
    @patch('saas_payments.requests.get')
    def test_verify_payment_mock(self, mock_get):
        """Testa verificação de pagamento com mock"""
        # Mock da resposta da API
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'status': 'success',
            'result': [
                {
                    'hash': '0x123456789',
                    'value': '29000000',  # 29 USDT em wei
                    'to': self.test_wallet.lower(),
                    'confirmations': 12
                }
            ]
        }
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Gerar payment request
        payment_request = self.processor.generate_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            annual=self.test_payment_data['annual']
        )
        
        # Verificar pagamento
        verification_result = self.processor.verify_payment(
            payment_request['payment_id'],
            expected_amount=self.test_payment_data['amount']
        )
        
        self.assertTrue(verification_result['verified'])
        self.assertIn('tx_hash', verification_result)
    
    def test_qr_code_generation(self):
        """Testa geração de QR code para pagamento"""
        payment_request = self.processor.generate_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            annual=self.test_payment_data['annual']
        )
        
        qr_code = payment_request['qr_code']
        self.assertIsNotNone(qr_code)
        self.assertTrue(qr_code.startswith('data:image/png;base64,'))
    
    def test_payment_timeout_handling(self):
        """Testa tratamento de timeout de pagamento"""
        payment_request = self.processor.generate_payment_request(
            user_id=self.test_payment_data['user_id'],
            plan=self.test_payment_data['plan'],
            amount=self.test_payment_data['amount'],
            annual=self.test_payment_data['annual']
        )
        
        # Simular timeout (pagamento expirado)
        is_expired = self.processor.is_payment_expired(payment_request['payment_id'])
        
        # Como acabou de ser criado, não deve estar expirado
        self.assertFalse(is_expired)
    
    def test_payment_history(self):
        """Testa histórico de pagamentos"""
        # Gerar alguns pagamentos de teste
        payment1 = self.processor.generate_payment_request(
            user_id='user1',
            plan=PlanType.STARTER,
            amount=29.0,
            annual=False
        )
        
        payment2 = self.processor.generate_payment_request(
            user_id='user1',
            plan=PlanType.PROFESSIONAL,
            amount=79.0,
            annual=False
        )
        
        # Verificar histórico
        history = self.processor.get_payment_history('user1')
        self.assertIsInstance(history, list)
        self.assertGreaterEqual(len(history), 2)

class TestTANOSIntegration(unittest.TestCase):
    """Testes para integração TANOS"""
    
    def setUp(self):
        """Configuração inicial para cada teste"""
        self.tanos = TANOSIntegration()
    
    def test_tanos_initialization(self):
        """Testa inicialização do TANOS"""
        self.assertIsNotNone(self.tanos.api_key)
        self.assertIsNotNone(self.tanos.base_url)
    
    @patch('saas_payments.requests.post')
    def test_security_check_mock(self, mock_post):
        """Testa verificação de segurança com mock"""
        # Mock da resposta TANOS
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'status': 'safe',
            'risk_score': 0.1,
            'flags': []
        }
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # Executar verificação
        result = self.tanos.check_transaction_security('0x123456789')
        
        self.assertTrue(result['is_safe'])
        self.assertEqual(result['risk_score'], 0.1)
    
    @patch('saas_payments.requests.post')
    def test_security_check_risky_transaction(self, mock_post):
        """Testa verificação de transação arriscada"""
        # Mock de transação arriscada
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'status': 'risky',
            'risk_score': 0.8,
            'flags': ['suspicious_pattern', 'high_value']
        }
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # Executar verificação
        result = self.tanos.check_transaction_security('0x987654321')
        
        self.assertFalse(result['is_safe'])
        self.assertEqual(result['risk_score'], 0.8)
        self.assertIn('suspicious_pattern', result['flags'])
    
    def test_wallet_validation(self):
        """Testa validação de endereços de wallet"""
        # Endereço válido
        valid_wallet = "******************************************"
        self.assertTrue(self.tanos.validate_wallet_address(valid_wallet))
        
        # Endereço inválido
        invalid_wallet = "invalid_wallet_address"
        self.assertFalse(self.tanos.validate_wallet_address(invalid_wallet))
        
        # Endereço vazio
        self.assertFalse(self.tanos.validate_wallet_address(""))

class TestPaymentSystemIntegration(unittest.TestCase):
    """Testes de integração para o sistema de pagamentos"""
    
    def setUp(self):
        """Configuração inicial para testes de integração"""
        self.test_wallet = "******************************************"
        self.processor = TetherPaymentProcessor(self.test_wallet)
        self.tanos = TANOSIntegration()
    
    def test_complete_payment_workflow(self):
        """Testa fluxo completo de pagamento"""
        # 1. Gerar solicitação de pagamento
        payment_request = self.processor.generate_payment_request(
            user_id='integration_test_user',
            plan=PlanType.STARTER,
            amount=29.0,
            annual=False
        )
        
        self.assertIsNotNone(payment_request)
        self.assertIn('payment_id', payment_request)
        
        # 2. Validar wallet address
        is_valid_wallet = self.tanos.validate_wallet_address(self.test_wallet)
        self.assertTrue(is_valid_wallet)
        
        # 3. Verificar se pagamento não está expirado
        is_expired = self.processor.is_payment_expired(payment_request['payment_id'])
        self.assertFalse(is_expired)
        
        # 4. Verificar histórico
        history = self.processor.get_payment_history('integration_test_user')
        self.assertIsInstance(history, list)
    
    def test_payment_security_integration(self):
        """Testa integração de segurança nos pagamentos"""
        # Gerar pagamento
        payment_request = self.processor.generate_payment_request(
            user_id='security_test_user',
            plan=PlanType.PROFESSIONAL,
            amount=79.0,
            annual=False
        )
        
        # Validar dados de segurança
        self.assertIsNotNone(payment_request['wallet_address'])
        self.assertTrue(self.tanos.validate_wallet_address(payment_request['wallet_address']))
    
    def test_plan_pricing_consistency(self):
        """Testa consistência de preços entre planos"""
        # Testar todos os planos
        plans_to_test = [
            (PlanType.STARTER, 29.0),
            (PlanType.PROFESSIONAL, 79.0)
        ]
        
        for plan, expected_amount in plans_to_test:
            payment_request = self.processor.generate_payment_request(
                user_id=f'test_user_{plan.value}',
                plan=plan,
                amount=expected_amount,
                annual=False
            )
            
            self.assertEqual(payment_request['amount'], expected_amount)
            self.assertEqual(payment_request['plan'], plan.value)

if __name__ == '__main__':
    # Configurar suite de testes
    unittest.main(verbosity=2)
