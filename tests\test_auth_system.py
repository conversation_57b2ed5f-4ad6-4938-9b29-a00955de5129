"""
Testes Unitários - Sistema de Autenticação CryptoSignals
Validação completa do SaaSAuthManager e funcionalidades relacionadas
"""

import unittest
import sys
import os
import tempfile
import sqlite3
from unittest.mock import patch, MagicMock

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from saas_auth import SaaSAuthManager, PlanType
import hashlib
import secrets
from datetime import datetime, timedelta

class TestSaaSAuthManager(unittest.TestCase):
    """Testes para o sistema de autenticação SaaS"""

    def setUp(self):
        """Configuração inicial para cada teste"""
        # Criar banco de dados temporário
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()

        # Inicializar auth manager com banco temporário
        self.auth_manager = SaaSAuthManager(db_path=self.test_db.name)

        # Dados de teste
        self.test_user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'plan': PlanType.FREE
        }

    def tearDown(self):
        """Limpeza após cada teste"""
        # Remover banco temporário
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)

    def test_database_initialization(self):
        """Testa se o banco de dados é inicializado corretamente"""
        # Verificar se as tabelas foram criadas
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()

        # Verificar tabela users
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        self.assertIsNotNone(cursor.fetchone())

        # Verificar tabela api_usage
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_usage'")
        self.assertIsNotNone(cursor.fetchone())

        conn.close()

    def test_user_registration_success(self):
        """Testa registro de usuário bem-sucedido"""
        user = self.auth_manager.create_user(
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        self.assertIsNotNone(user)
        self.assertEqual(user.email, self.test_user_data['email'])
        self.assertEqual(user.plan, self.test_user_data['plan'])

    def test_user_registration_duplicate_email(self):
        """Testa registro com email duplicado"""
        # Registrar usuário primeiro
        user1 = self.auth_manager.create_user(
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        # Tentar registrar novamente com mesmo email
        user2 = self.auth_manager.create_user(
            email=self.test_user_data['email'],
            password='DifferentPassword123!',
            plan=PlanType.FREE
        )

        self.assertIsNotNone(user1)
        self.assertIsNone(user2)  # Deve falhar por email duplicado

    def test_user_login_success(self):
        """Testa login bem-sucedido"""
        # Registrar usuário primeiro
        user = self.auth_manager.create_user(
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        # Fazer login
        authenticated_user = self.auth_manager.authenticate_user(
            email=self.test_user_data['email'],
            password=self.test_user_data['password']
        )

        self.assertIsNotNone(authenticated_user)
        self.assertEqual(authenticated_user.email, self.test_user_data['email'])
        self.assertEqual(authenticated_user.id, user.id)

    def test_user_login_invalid_credentials(self):
        """Testa login com credenciais inválidas"""
        # Registrar usuário primeiro
        user = self.auth_manager.create_user(
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        # Tentar login com senha errada
        authenticated_user = self.auth_manager.authenticate_user(
            email=self.test_user_data['email'],
            password='WrongPassword123!'
        )

        self.assertIsNotNone(user)
        self.assertIsNone(authenticated_user)  # Login deve falhar

    def test_user_login_nonexistent_user(self):
        """Testa login com usuário inexistente"""
        authenticated_user = self.auth_manager.authenticate_user(
            email='<EMAIL>',
            password='AnyPassword123!'
        )

        self.assertIsNone(authenticated_user)

    def test_password_hashing(self):
        """Testa se as senhas são hasheadas corretamente"""
        password = 'TestPassword123!'
        salt = secrets.token_hex(32)

        # Simular o processo de hash usado no auth manager
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()

        # Verificar se o hash é diferente da senha original
        self.assertNotEqual(password, password_hash)
        self.assertEqual(len(password_hash), 64)  # SHA-256 produz 64 caracteres hex

    def test_get_user_by_id(self):
        """Testa recuperação de usuário por ID"""
        # Registrar usuário
        created_user = self.auth_manager.create_user(
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        # Recuperar usuário
        retrieved_user = self.auth_manager.get_user_by_id(created_user.id)

        self.assertIsNotNone(retrieved_user)
        self.assertEqual(retrieved_user.email, self.test_user_data['email'])
        self.assertEqual(retrieved_user.plan, self.test_user_data['plan'])
        self.assertEqual(retrieved_user.id, created_user.id)

    def test_get_user_by_username(self):
        """Testa recuperação de usuário por username"""
        # Registrar usuário
        self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        # Recuperar usuário
        user = self.auth_manager.get_user_by_username(self.test_user_data['username'])

        self.assertIsNotNone(user)
        self.assertEqual(user['username'], self.test_user_data['username'])
        self.assertEqual(user['email'], self.test_user_data['email'])

    def test_plan_type_enum(self):
        """Testa se os tipos de plano estão corretos"""
        self.assertEqual(PlanType.FREE.value, 'free')
        self.assertEqual(PlanType.STARTER.value, 'starter')
        self.assertEqual(PlanType.PROFESSIONAL.value, 'professional')
        self.assertEqual(PlanType.ENTERPRISE.value, 'enterprise')

    def test_user_plan_upgrade(self):
        """Testa upgrade de plano do usuário"""
        # Registrar usuário com plano FREE
        result = self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=PlanType.FREE
        )

        user_id = result['user_id']

        # Fazer upgrade para PROFESSIONAL
        upgrade_result = self.auth_manager.upgrade_user_plan(user_id, PlanType.PROFESSIONAL)

        self.assertTrue(upgrade_result['success'])

        # Verificar se o plano foi atualizado
        user = self.auth_manager.get_user_by_id(user_id)
        self.assertEqual(user['plan'], PlanType.PROFESSIONAL.value)

    def test_api_usage_tracking(self):
        """Testa rastreamento de uso da API"""
        # Registrar usuário
        result = self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=PlanType.FREE
        )

        user_id = result['user_id']

        # Simular uso da API
        self.auth_manager.log_api_usage(user_id, 'get_price_data')
        self.auth_manager.log_api_usage(user_id, 'get_technical_analysis')

        # Verificar contagem de uso
        usage = self.auth_manager.get_api_usage(user_id)
        self.assertGreaterEqual(usage['total_requests'], 2)

    def test_rate_limiting_free_plan(self):
        """Testa limitação de taxa para plano FREE"""
        # Registrar usuário FREE
        result = self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=PlanType.FREE
        )

        user_id = result['user_id']

        # Verificar limites do plano FREE
        limits = self.auth_manager.get_plan_limits(PlanType.FREE)
        self.assertEqual(limits['requests_per_hour'], 10)
        self.assertEqual(limits['requests_per_month'], 1000)

    def test_rate_limiting_professional_plan(self):
        """Testa limitação de taxa para plano PROFESSIONAL"""
        limits = self.auth_manager.get_plan_limits(PlanType.PROFESSIONAL)
        self.assertEqual(limits['requests_per_hour'], 1000)
        self.assertEqual(limits['requests_per_month'], 100000)

    def test_token_generation(self):
        """Testa geração de tokens JWT"""
        # Registrar usuário
        result = self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        user_id = result['user_id']

        # Gerar token
        token = self.auth_manager.generate_jwt_token(user_id)

        self.assertIsNotNone(token)
        self.assertIsInstance(token, str)
        self.assertGreater(len(token), 50)  # JWT tokens são longos

    def test_token_validation(self):
        """Testa validação de tokens JWT"""
        # Registrar usuário
        result = self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        user_id = result['user_id']

        # Gerar e validar token
        token = self.auth_manager.generate_jwt_token(user_id)
        validation_result = self.auth_manager.validate_jwt_token(token)

        self.assertTrue(validation_result['valid'])
        self.assertEqual(validation_result['user_id'], user_id)

    def test_invalid_token_validation(self):
        """Testa validação de token inválido"""
        invalid_token = "invalid.jwt.token"
        validation_result = self.auth_manager.validate_jwt_token(invalid_token)

        self.assertFalse(validation_result['valid'])

    def test_user_session_management(self):
        """Testa gerenciamento de sessões de usuário"""
        # Registrar usuário
        result = self.auth_manager.register_user(
            username=self.test_user_data['username'],
            email=self.test_user_data['email'],
            password=self.test_user_data['password'],
            plan=self.test_user_data['plan']
        )

        user_id = result['user_id']

        # Criar sessão
        session_id = self.auth_manager.create_user_session(user_id)
        self.assertIsNotNone(session_id)

        # Validar sessão
        session_valid = self.auth_manager.validate_user_session(session_id, user_id)
        self.assertTrue(session_valid)

        # Invalidar sessão
        self.auth_manager.invalidate_user_session(session_id)
        session_valid_after = self.auth_manager.validate_user_session(session_id, user_id)
        self.assertFalse(session_valid_after)

class TestAuthSystemIntegration(unittest.TestCase):
    """Testes de integração para o sistema de autenticação"""

    def setUp(self):
        """Configuração inicial para testes de integração"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.auth_manager = SaaSAuthManager(db_path=self.test_db.name)

    def tearDown(self):
        """Limpeza após testes de integração"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)

    def test_complete_user_workflow(self):
        """Testa fluxo completo do usuário: registro → login → uso → upgrade"""
        # 1. Registro
        register_result = self.auth_manager.register_user(
            username='integrationuser',
            email='<EMAIL>',
            password='IntegrationTest123!',
            plan=PlanType.FREE
        )

        self.assertTrue(register_result['success'])
        user_id = register_result['user_id']

        # 2. Login
        login_result = self.auth_manager.login_user(
            username='integrationuser',
            password='IntegrationTest123!'
        )

        self.assertTrue(login_result['success'])
        self.assertEqual(login_result['user_id'], user_id)

        # 3. Uso da API
        self.auth_manager.log_api_usage(user_id, 'get_price_data')
        usage = self.auth_manager.get_api_usage(user_id)
        self.assertGreater(usage['total_requests'], 0)

        # 4. Upgrade de plano
        upgrade_result = self.auth_manager.upgrade_user_plan(user_id, PlanType.PROFESSIONAL)
        self.assertTrue(upgrade_result['success'])

        # 5. Verificar estado final
        final_user = self.auth_manager.get_user_by_id(user_id)
        self.assertEqual(final_user['plan'], PlanType.PROFESSIONAL.value)

if __name__ == '__main__':
    # Configurar suite de testes
    unittest.main(verbosity=2)
