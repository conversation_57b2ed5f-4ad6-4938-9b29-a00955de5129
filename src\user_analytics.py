"""
Sistema de Analytics Avançados para Usuários - CryptoSignals
Analytics detalhados de comportamento, performance e insights
"""

import sqlite3
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, Counter
import threading
import time

class EventType(Enum):
    """Tipos de eventos para tracking"""
    LOGIN = "login"
    LOGOUT = "logout"
    ANALYSIS_REQUEST = "analysis_request"
    PREDICTION_VIEW = "prediction_view"
    ALERT_CREATED = "alert_created"
    ALERT_TRIGGERED = "alert_triggered"
    PAYMENT_INITIATED = "payment_initiated"
    PAYMENT_COMPLETED = "payment_completed"
    DASHBOARD_VIEW = "dashboard_view"
    SYMBOL_SEARCH = "symbol_search"
    EXPORT_DATA = "export_data"
    API_CALL = "api_call"
    ERROR_OCCURRED = "error_occurred"

class MetricType(Enum):
    """Tipos de métricas"""
    ENGAGEMENT = "engagement"
    PERFORMANCE = "performance"
    FINANCIAL = "financial"
    BEHAVIORAL = "behavioral"
    TECHNICAL = "technical"

@dataclass
class UserEvent:
    """Evento de usuário"""
    id: str
    user_id: str
    event_type: EventType
    timestamp: datetime
    session_id: str
    ip_address: str
    user_agent: str
    data: Dict[str, Any]
    duration_ms: Optional[int] = None
    success: bool = True

@dataclass
class UserSession:
    """Sessão de usuário"""
    session_id: str
    user_id: str
    start_time: datetime
    end_time: Optional[datetime]
    ip_address: str
    user_agent: str
    events_count: int
    duration_seconds: Optional[int] = None
    pages_visited: List[str] = None
    
    def __post_init__(self):
        if self.pages_visited is None:
            self.pages_visited = []

@dataclass
class AnalyticsMetric:
    """Métrica de analytics"""
    name: str
    value: float
    metric_type: MetricType
    period: str
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class UserInsight:
    """Insight sobre usuário"""
    user_id: str
    insight_type: str
    title: str
    description: str
    confidence: float
    data: Dict[str, Any]
    generated_at: datetime
    expires_at: Optional[datetime] = None

class UserAnalyticsEngine:
    """Engine principal de analytics de usuários"""
    
    def __init__(self, db_path: str = "user_analytics.db"):
        self.db_path = db_path
        self.active_sessions: Dict[str, UserSession] = {}
        self.event_buffer: List[UserEvent] = []
        self.metrics_cache: Dict[str, Any] = {}
        self.insights_cache: Dict[str, List[UserInsight]] = defaultdict(list)
        
        # Configurações
        self.config = {
            'buffer_size': 1000,
            'flush_interval': 60,  # segundos
            'session_timeout': 1800,  # 30 minutos
            'metrics_retention_days': 90,
            'events_retention_days': 365
        }
        
        # Inicializar banco
        self._init_database()
        
        # Iniciar workers
        self._start_workers()
    
    def _init_database(self):
        """Inicializa banco de dados"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabela de eventos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_events (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                session_id TEXT NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                data TEXT,
                duration_ms INTEGER,
                success BOOLEAN DEFAULT TRUE
            )
        ''')
        
        # Tabela de sessões
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                session_id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                ip_address TEXT,
                user_agent TEXT,
                events_count INTEGER DEFAULT 0,
                duration_seconds INTEGER,
                pages_visited TEXT
            )
        ''')
        
        # Tabela de métricas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analytics_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                value REAL NOT NULL,
                metric_type TEXT NOT NULL,
                period TEXT NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                metadata TEXT
            )
        ''')
        
        # Tabela de insights
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_insights (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                insight_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                confidence REAL NOT NULL,
                data TEXT NOT NULL,
                generated_at TIMESTAMP NOT NULL,
                expires_at TIMESTAMP
            )
        ''')
        
        # Índices
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_user_id ON user_events(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_timestamp ON user_events(timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_type ON user_events(event_type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_name ON analytics_metrics(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_insights_user_id ON user_insights(user_id)')
        
        conn.commit()
        conn.close()
    
    def track_event(self, user_id: str, event_type: EventType, session_id: str,
                   ip_address: str = "unknown", user_agent: str = "unknown",
                   data: Dict[str, Any] = None, duration_ms: Optional[int] = None,
                   success: bool = True) -> str:
        """Registra evento de usuário"""
        import uuid
        
        event = UserEvent(
            id=str(uuid.uuid4()),
            user_id=user_id,
            event_type=event_type,
            timestamp=datetime.now(),
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            data=data or {},
            duration_ms=duration_ms,
            success=success
        )
        
        # Adicionar ao buffer
        self.event_buffer.append(event)
        
        # Atualizar sessão ativa
        self._update_session(session_id, user_id, ip_address, user_agent)
        
        # Flush se buffer estiver cheio
        if len(self.event_buffer) >= self.config['buffer_size']:
            self._flush_events()
        
        return event.id
    
    def start_session(self, user_id: str, session_id: str, 
                     ip_address: str = "unknown", user_agent: str = "unknown") -> UserSession:
        """Inicia nova sessão"""
        session = UserSession(
            session_id=session_id,
            user_id=user_id,
            start_time=datetime.now(),
            end_time=None,
            ip_address=ip_address,
            user_agent=user_agent,
            events_count=0
        )
        
        self.active_sessions[session_id] = session
        return session
    
    def end_session(self, session_id: str):
        """Finaliza sessão"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.end_time = datetime.now()
            session.duration_seconds = int((session.end_time - session.start_time).total_seconds())
            
            # Salvar sessão no banco
            self._save_session(session)
            
            # Remover da memória
            del self.active_sessions[session_id]
    
    def _update_session(self, session_id: str, user_id: str, ip_address: str, user_agent: str):
        """Atualiza sessão ativa"""
        if session_id not in self.active_sessions:
            self.start_session(user_id, session_id, ip_address, user_agent)
        
        session = self.active_sessions[session_id]
        session.events_count += 1
    
    def _flush_events(self):
        """Salva eventos no banco"""
        if not self.event_buffer:
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        events_data = []
        for event in self.event_buffer:
            events_data.append((
                event.id,
                event.user_id,
                event.event_type.value,
                event.timestamp,
                event.session_id,
                event.ip_address,
                event.user_agent,
                json.dumps(event.data),
                event.duration_ms,
                event.success
            ))
        
        cursor.executemany('''
            INSERT INTO user_events 
            (id, user_id, event_type, timestamp, session_id, ip_address, 
             user_agent, data, duration_ms, success)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', events_data)
        
        conn.commit()
        conn.close()
        
        print(f"📊 {len(self.event_buffer)} eventos salvos no banco")
        self.event_buffer.clear()
    
    def _save_session(self, session: UserSession):
        """Salva sessão no banco"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO user_sessions 
            (session_id, user_id, start_time, end_time, ip_address, 
             user_agent, events_count, duration_seconds, pages_visited)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session.session_id,
            session.user_id,
            session.start_time,
            session.end_time,
            session.ip_address,
            session.user_agent,
            session.events_count,
            session.duration_seconds,
            json.dumps(session.pages_visited)
        ))
        
        conn.commit()
        conn.close()
    
    def get_user_metrics(self, user_id: str, period_days: int = 30) -> Dict[str, Any]:
        """Obtém métricas de um usuário"""
        start_date = datetime.now() - timedelta(days=period_days)
        
        conn = sqlite3.connect(self.db_path)
        
        # Eventos por tipo
        events_df = pd.read_sql_query('''
            SELECT event_type, COUNT(*) as count, 
                   AVG(duration_ms) as avg_duration,
                   SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count
            FROM user_events 
            WHERE user_id = ? AND timestamp >= ?
            GROUP BY event_type
        ''', conn, params=(user_id, start_date))
        
        # Sessões
        sessions_df = pd.read_sql_query('''
            SELECT COUNT(*) as total_sessions,
                   AVG(duration_seconds) as avg_session_duration,
                   AVG(events_count) as avg_events_per_session,
                   MAX(duration_seconds) as max_session_duration
            FROM user_sessions 
            WHERE user_id = ? AND start_time >= ?
        ''', conn, params=(user_id, start_date))
        
        # Atividade por dia
        daily_activity = pd.read_sql_query('''
            SELECT DATE(timestamp) as date, COUNT(*) as events
            FROM user_events 
            WHERE user_id = ? AND timestamp >= ?
            GROUP BY DATE(timestamp)
            ORDER BY date
        ''', conn, params=(user_id, start_date))
        
        # Atividade por hora
        hourly_activity = pd.read_sql_query('''
            SELECT strftime('%H', timestamp) as hour, COUNT(*) as events
            FROM user_events 
            WHERE user_id = ? AND timestamp >= ?
            GROUP BY strftime('%H', timestamp)
            ORDER BY hour
        ''', conn, params=(user_id, start_date))
        
        conn.close()
        
        # Calcular métricas
        total_events = events_df['count'].sum() if not events_df.empty else 0
        success_rate = (events_df['success_count'].sum() / total_events * 100) if total_events > 0 else 0
        
        # Símbolos mais analisados
        most_analyzed_symbols = self._get_user_symbol_stats(user_id, period_days)
        
        # Padrões de uso
        usage_patterns = self._analyze_usage_patterns(user_id, period_days)
        
        return {
            'period_days': period_days,
            'total_events': int(total_events),
            'success_rate': round(success_rate, 2),
            'events_by_type': events_df.to_dict('records') if not events_df.empty else [],
            'session_stats': sessions_df.to_dict('records')[0] if not sessions_df.empty else {},
            'daily_activity': daily_activity.to_dict('records') if not daily_activity.empty else [],
            'hourly_activity': hourly_activity.to_dict('records') if not hourly_activity.empty else [],
            'most_analyzed_symbols': most_analyzed_symbols,
            'usage_patterns': usage_patterns
        }
    
    def _get_user_symbol_stats(self, user_id: str, period_days: int) -> List[Dict[str, Any]]:
        """Obtém estatísticas de símbolos do usuário"""
        start_date = datetime.now() - timedelta(days=period_days)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT data FROM user_events 
            WHERE user_id = ? AND timestamp >= ? 
            AND event_type IN ('analysis_request', 'prediction_view', 'symbol_search')
        ''', (user_id, start_date))
        
        symbol_counts = Counter()
        for (data_json,) in cursor.fetchall():
            try:
                data = json.loads(data_json)
                symbol = data.get('symbol')
                if symbol:
                    symbol_counts[symbol] += 1
            except:
                continue
        
        conn.close()
        
        return [
            {'symbol': symbol, 'count': count}
            for symbol, count in symbol_counts.most_common(10)
        ]
    
    def _analyze_usage_patterns(self, user_id: str, period_days: int) -> Dict[str, Any]:
        """Analisa padrões de uso do usuário"""
        start_date = datetime.now() - timedelta(days=period_days)
        
        conn = sqlite3.connect(self.db_path)
        
        # Padrão temporal
        temporal_df = pd.read_sql_query('''
            SELECT strftime('%w', timestamp) as day_of_week,
                   strftime('%H', timestamp) as hour,
                   COUNT(*) as events
            FROM user_events 
            WHERE user_id = ? AND timestamp >= ?
            GROUP BY strftime('%w', timestamp), strftime('%H', timestamp)
        ''', conn, params=(user_id, start_date))
        
        # Sequências de eventos
        sequences_df = pd.read_sql_query('''
            SELECT event_type, 
                   LAG(event_type) OVER (ORDER BY timestamp) as prev_event
            FROM user_events 
            WHERE user_id = ? AND timestamp >= ?
            ORDER BY timestamp
        ''', conn, params=(user_id, start_date))
        
        conn.close()
        
        # Análise de padrões
        patterns = {}
        
        # Horário mais ativo
        if not temporal_df.empty:
            most_active_hour = temporal_df.groupby('hour')['events'].sum().idxmax()
            most_active_day = temporal_df.groupby('day_of_week')['events'].sum().idxmax()
            
            patterns['most_active_hour'] = int(most_active_hour)
            patterns['most_active_day'] = int(most_active_day)
        
        # Sequências comuns
        if not sequences_df.empty:
            sequences_df = sequences_df.dropna()
            if not sequences_df.empty:
                common_sequences = sequences_df.groupby(['prev_event', 'event_type']).size().nlargest(5)
                patterns['common_sequences'] = [
                    {'from': prev_event, 'to': event, 'count': count}
                    for (prev_event, event), count in common_sequences.items()
                ]
        
        return patterns
    
    def generate_user_insights(self, user_id: str) -> List[UserInsight]:
        """Gera insights para um usuário"""
        insights = []
        
        # Obter métricas do usuário
        metrics = self.get_user_metrics(user_id, 30)
        
        # Insight: Usuário muito ativo
        if metrics['total_events'] > 100:
            insights.append(UserInsight(
                user_id=user_id,
                insight_type="high_activity",
                title="Usuário Muito Ativo",
                description=f"Realizou {metrics['total_events']} ações nos últimos 30 dias",
                confidence=0.9,
                data={'total_events': metrics['total_events']},
                generated_at=datetime.now()
            ))
        
        # Insight: Símbolos favoritos
        if metrics['most_analyzed_symbols']:
            top_symbol = metrics['most_analyzed_symbols'][0]
            insights.append(UserInsight(
                user_id=user_id,
                insight_type="favorite_symbol",
                title="Símbolo Favorito",
                description=f"Analisou {top_symbol['symbol']} {top_symbol['count']} vezes",
                confidence=0.8,
                data=top_symbol,
                generated_at=datetime.now()
            ))
        
        # Insight: Padrão de horário
        if 'usage_patterns' in metrics and 'most_active_hour' in metrics['usage_patterns']:
            hour = metrics['usage_patterns']['most_active_hour']
            insights.append(UserInsight(
                user_id=user_id,
                insight_type="usage_pattern",
                title="Horário Preferido",
                description=f"Mais ativo às {hour}:00h",
                confidence=0.7,
                data={'hour': hour},
                generated_at=datetime.now()
            ))
        
        # Salvar insights
        for insight in insights:
            self._save_insight(insight)
        
        return insights
    
    def _save_insight(self, insight: UserInsight):
        """Salva insight no banco"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO user_insights 
            (user_id, insight_type, title, description, confidence, data, generated_at, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            insight.user_id,
            insight.insight_type,
            insight.title,
            insight.description,
            insight.confidence,
            json.dumps(insight.data),
            insight.generated_at,
            insight.expires_at
        ))
        
        conn.commit()
        conn.close()
    
    def get_platform_metrics(self, period_days: int = 7) -> Dict[str, Any]:
        """Obtém métricas da plataforma"""
        start_date = datetime.now() - timedelta(days=period_days)
        
        conn = sqlite3.connect(self.db_path)
        
        # Usuários ativos
        active_users = pd.read_sql_query('''
            SELECT COUNT(DISTINCT user_id) as count
            FROM user_events 
            WHERE timestamp >= ?
        ''', conn, params=(start_date,))
        
        # Eventos por dia
        daily_events = pd.read_sql_query('''
            SELECT DATE(timestamp) as date, COUNT(*) as events,
                   COUNT(DISTINCT user_id) as unique_users
            FROM user_events 
            WHERE timestamp >= ?
            GROUP BY DATE(timestamp)
            ORDER BY date
        ''', conn, params=(start_date,))
        
        # Top eventos
        top_events = pd.read_sql_query('''
            SELECT event_type, COUNT(*) as count
            FROM user_events 
            WHERE timestamp >= ?
            GROUP BY event_type
            ORDER BY count DESC
            LIMIT 10
        ''', conn, params=(start_date,))
        
        # Retenção de usuários
        retention_data = pd.read_sql_query('''
            SELECT user_id, MIN(DATE(timestamp)) as first_seen,
                   MAX(DATE(timestamp)) as last_seen,
                   COUNT(DISTINCT DATE(timestamp)) as active_days
            FROM user_events 
            WHERE timestamp >= ?
            GROUP BY user_id
        ''', conn, params=(start_date,))
        
        conn.close()
        
        # Calcular métricas
        total_active_users = active_users.iloc[0]['count'] if not active_users.empty else 0
        avg_daily_events = daily_events['events'].mean() if not daily_events.empty else 0
        avg_daily_users = daily_events['unique_users'].mean() if not daily_events.empty else 0
        
        # Taxa de retenção
        retention_rate = 0
        if not retention_data.empty:
            retained_users = len(retention_data[retention_data['active_days'] > 1])
            retention_rate = (retained_users / len(retention_data) * 100) if len(retention_data) > 0 else 0
        
        return {
            'period_days': period_days,
            'total_active_users': int(total_active_users),
            'avg_daily_events': round(avg_daily_events, 2),
            'avg_daily_users': round(avg_daily_users, 2),
            'retention_rate': round(retention_rate, 2),
            'daily_activity': daily_events.to_dict('records') if not daily_events.empty else [],
            'top_events': top_events.to_dict('records') if not top_events.empty else [],
            'active_sessions': len(self.active_sessions)
        }
    
    def _start_workers(self):
        """Inicia workers de background"""
        # Worker de flush automático
        flush_worker = threading.Thread(target=self._flush_worker, daemon=True)
        flush_worker.start()
        
        # Worker de limpeza de sessões
        session_cleanup_worker = threading.Thread(target=self._session_cleanup_worker, daemon=True)
        session_cleanup_worker.start()
        
        # Worker de geração de insights
        insights_worker = threading.Thread(target=self._insights_worker, daemon=True)
        insights_worker.start()
    
    def _flush_worker(self):
        """Worker para flush automático de eventos"""
        while True:
            try:
                time.sleep(self.config['flush_interval'])
                if self.event_buffer:
                    self._flush_events()
            except Exception as e:
                print(f"❌ Erro no flush worker: {e}")
                time.sleep(60)
    
    def _session_cleanup_worker(self):
        """Worker para limpeza de sessões inativas"""
        while True:
            try:
                now = datetime.now()
                timeout = timedelta(seconds=self.config['session_timeout'])
                
                inactive_sessions = []
                for session_id, session in self.active_sessions.items():
                    if now - session.start_time > timeout:
                        inactive_sessions.append(session_id)
                
                for session_id in inactive_sessions:
                    self.end_session(session_id)
                
                time.sleep(300)  # Verificar a cada 5 minutos
                
            except Exception as e:
                print(f"❌ Erro na limpeza de sessões: {e}")
                time.sleep(300)
    
    def _insights_worker(self):
        """Worker para geração automática de insights"""
        while True:
            try:
                # Gerar insights para usuários ativos
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Usuários ativos nas últimas 24 horas
                cursor.execute('''
                    SELECT DISTINCT user_id FROM user_events 
                    WHERE timestamp >= datetime('now', '-24 hours')
                ''')
                
                active_users = [row[0] for row in cursor.fetchall()]
                conn.close()
                
                # Gerar insights para cada usuário ativo
                for user_id in active_users[:10]:  # Limitar a 10 usuários por vez
                    try:
                        self.generate_user_insights(user_id)
                    except Exception as e:
                        print(f"❌ Erro ao gerar insights para {user_id}: {e}")
                
                time.sleep(3600)  # Gerar insights a cada hora
                
            except Exception as e:
                print(f"❌ Erro no worker de insights: {e}")
                time.sleep(3600)

# Instância global do analytics
user_analytics = UserAnalyticsEngine()
