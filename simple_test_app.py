#!/usr/bin/env python3
"""
CryptoSignals - Versão de Teste Simplificada
Para verificar se o sistema básico funciona
"""

import sys
import os
from flask import Flask, render_template_string, jsonify
import threading
import webbrowser
from datetime import datetime

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Inicializar <PERSON>
app = Flask(__name__)
app.secret_key = "cryptosignals_test_2024"

# Template HTML simples para teste
LANDING_TEMPLATE = """
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoSignals - Teste</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            padding: 40px 20px;
        }
        .logo {
            font-size: 3em;
            margin-bottom: 20px;
        }
        .status {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #45a049;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            display: inline-block;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀 CryptoSignals</div>
        <h1>Sistema de Teste Inicializado!</h1>
        
        <div class="status">
            <h2>✅ Status do Sistema</h2>
            <p><strong>Data/Hora:</strong> {{ timestamp }}</p>
            <p><strong>Status:</strong> Funcionando</p>
            <p><strong>Versão:</strong> Teste v1.0</p>
        </div>
        
        <div class="features">
            <h2>🎯 Funcionalidades Disponíveis</h2>
            <div class="feature">
                <h3>📊 API Status</h3>
                <a href="/api/status" class="btn">Testar API</a>
            </div>
            <div class="feature">
                <h3>💰 Crypto Data</h3>
                <a href="/api/crypto/BTC" class="btn">Dados BTC</a>
            </div>
            <div class="feature">
                <h3>🔍 Sistema Info</h3>
                <a href="/api/info" class="btn">Info Sistema</a>
            </div>
        </div>
        
        <div class="status">
            <h2>📋 Próximos Passos</h2>
            <p>1. ✅ Sistema básico funcionando</p>
            <p>2. 🔄 Testar APIs</p>
            <p>3. 🔄 Carregar módulos completos</p>
            <p>4. 🔄 Inicializar dashboard completo</p>
        </div>
    </div>
</body>
</html>
"""

@app.route('/')
def landing_page():
    """Landing Page de Teste"""
    return render_template_string(LANDING_TEMPLATE, timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

@app.route('/api/status')
def api_status():
    """API de Status"""
    return jsonify({
        'status': 'ok',
        'message': 'CryptoSignals API funcionando!',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0-test',
        'features': {
            'flask': True,
            'pandas': True,
            'numpy': True,
            'plotly': True,
            'requests': True
        }
    })

@app.route('/api/crypto/<symbol>')
def crypto_data(symbol):
    """API de Dados de Crypto (simulado)"""
    # Dados simulados para teste
    fake_data = {
        'symbol': symbol.upper(),
        'price': 43250.00 if symbol.upper() == 'BTC' else 2850.00,
        'change_24h': 2.5,
        'volume': 28500000000,
        'market_cap': 850000000000,
        'timestamp': datetime.now().isoformat(),
        'status': 'simulated_data'
    }
    
    return jsonify({
        'success': True,
        'data': fake_data,
        'message': f'Dados simulados para {symbol.upper()}'
    })

@app.route('/api/info')
def system_info():
    """Informações do Sistema"""
    try:
        import pandas
        import numpy
        import plotly
        import requests
        
        return jsonify({
            'system': 'CryptoSignals Test',
            'status': 'running',
            'python_version': sys.version,
            'dependencies': {
                'pandas': pandas.__version__,
                'numpy': numpy.__version__,
                'plotly': plotly.__version__,
                'requests': requests.__version__
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'status': 'error'
        }), 500

if __name__ == "__main__":
    print("🚀 Iniciando CryptoSignals - Versão de Teste...")
    print("🌐 Acesse: http://localhost:5000")
    print("🎯 Testando sistema básico")
    print("⏹️  Pressione Ctrl+C para parar")
    print("-" * 50)
    
    # Abrir navegador automaticamente após 2 segundos
    threading.Timer(2.0, lambda: webbrowser.open('http://localhost:5000')).start()
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 Sistema de teste encerrado!")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
