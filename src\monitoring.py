"""
Sistema de Monitoramento CryptoSignals
Monitoramento em tempo real de performance, saúde e métricas
"""

import time
import threading
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics

@dataclass
class PerformanceMetric:
    """Métrica de performance"""
    timestamp: datetime
    operation: str
    duration_ms: float
    success: bool
    user_id: Optional[str] = None
    error_message: Optional[str] = None

@dataclass
class SystemHealth:
    """Estado de saúde do sistema"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    active_connections: int
    response_time_avg: float
    error_rate: float
    status: str  # 'healthy', 'warning', 'critical'

class PerformanceMonitor:
    """Monitor de performance em tempo real"""
    
    def __init__(self, db_path: str = "monitoring.db"):
        self.db_path = db_path
        self.metrics_buffer = deque(maxlen=1000)  # Buffer circular para métricas
        self.active_operations = defaultdict(int)
        self.error_counts = defaultdict(int)
        self.response_times = defaultdict(list)
        self.lock = threading.Lock()
        
        # Configurações de alertas
        self.alert_thresholds = {
            'response_time_ms': 1000,
            'error_rate_percent': 5.0,
            'memory_usage_percent': 80.0,
            'cpu_usage_percent': 85.0
        }
        
        self._init_database()
        self._start_background_tasks()
    
    def _init_database(self):
        """Inicializa banco de dados de monitoramento"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabela de métricas de performance
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                operation TEXT,
                duration_ms REAL,
                success BOOLEAN,
                user_id TEXT,
                error_message TEXT
            )
        """)
        
        # Tabela de saúde do sistema
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                cpu_usage REAL,
                memory_usage REAL,
                active_connections INTEGER,
                response_time_avg REAL,
                error_rate REAL,
                status TEXT
            )
        """)
        
        # Tabela de alertas
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                alert_type TEXT,
                severity TEXT,
                message TEXT,
                resolved BOOLEAN DEFAULT FALSE
            )
        """)
        
        conn.commit()
        conn.close()
    
    def _start_background_tasks(self):
        """Inicia tarefas em background"""
        # Thread para coleta de métricas do sistema
        health_thread = threading.Thread(target=self._collect_system_health, daemon=True)
        health_thread.start()
        
        # Thread para limpeza de dados antigos
        cleanup_thread = threading.Thread(target=self._cleanup_old_data, daemon=True)
        cleanup_thread.start()
    
    def record_operation(self, operation: str, duration_ms: float, success: bool = True, 
                        user_id: Optional[str] = None, error_message: Optional[str] = None):
        """Registra uma operação"""
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            operation=operation,
            duration_ms=duration_ms,
            success=success,
            user_id=user_id,
            error_message=error_message
        )
        
        with self.lock:
            self.metrics_buffer.append(metric)
            self.active_operations[operation] += 1
            
            if not success:
                self.error_counts[operation] += 1
            
            # Manter histórico de tempos de resposta (últimos 100)
            if len(self.response_times[operation]) >= 100:
                self.response_times[operation].pop(0)
            self.response_times[operation].append(duration_ms)
        
        # Salvar no banco de dados
        self._save_metric(metric)
        
        # Verificar alertas
        self._check_alerts(metric)
    
    def _save_metric(self, metric: PerformanceMetric):
        """Salva métrica no banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO performance_metrics 
                (timestamp, operation, duration_ms, success, user_id, error_message)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                metric.timestamp.isoformat(),
                metric.operation,
                metric.duration_ms,
                metric.success,
                metric.user_id,
                metric.error_message
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Erro ao salvar métrica: {e}")
    
    def _collect_system_health(self):
        """Coleta métricas de saúde do sistema"""
        while True:
            try:
                import psutil
                
                # Coletar métricas do sistema
                cpu_usage = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                memory_usage = memory.percent
                
                # Calcular métricas da aplicação
                with self.lock:
                    active_connections = sum(self.active_operations.values())
                    
                    # Calcular tempo de resposta médio
                    all_times = []
                    for times in self.response_times.values():
                        all_times.extend(times)
                    
                    response_time_avg = statistics.mean(all_times) if all_times else 0
                    
                    # Calcular taxa de erro
                    total_ops = sum(self.active_operations.values())
                    total_errors = sum(self.error_counts.values())
                    error_rate = (total_errors / total_ops * 100) if total_ops > 0 else 0
                
                # Determinar status
                status = self._determine_health_status(cpu_usage, memory_usage, response_time_avg, error_rate)
                
                # Criar registro de saúde
                health = SystemHealth(
                    timestamp=datetime.now(),
                    cpu_usage=cpu_usage,
                    memory_usage=memory_usage,
                    active_connections=active_connections,
                    response_time_avg=response_time_avg,
                    error_rate=error_rate,
                    status=status
                )
                
                # Salvar no banco
                self._save_health(health)
                
                time.sleep(30)  # Coletar a cada 30 segundos
                
            except ImportError:
                # psutil não disponível, usar métricas básicas
                time.sleep(60)
            except Exception as e:
                print(f"Erro na coleta de saúde: {e}")
                time.sleep(60)
    
    def _determine_health_status(self, cpu: float, memory: float, response_time: float, error_rate: float) -> str:
        """Determina o status de saúde do sistema"""
        if (cpu > self.alert_thresholds['cpu_usage_percent'] or 
            memory > self.alert_thresholds['memory_usage_percent'] or
            response_time > self.alert_thresholds['response_time_ms'] or
            error_rate > self.alert_thresholds['error_rate_percent']):
            return 'critical'
        elif (cpu > 70 or memory > 70 or response_time > 500 or error_rate > 2):
            return 'warning'
        else:
            return 'healthy'
    
    def _save_health(self, health: SystemHealth):
        """Salva registro de saúde"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO system_health 
                (timestamp, cpu_usage, memory_usage, active_connections, 
                 response_time_avg, error_rate, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                health.timestamp.isoformat(),
                health.cpu_usage,
                health.memory_usage,
                health.active_connections,
                health.response_time_avg,
                health.error_rate,
                health.status
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Erro ao salvar saúde: {e}")
    
    def _check_alerts(self, metric: PerformanceMetric):
        """Verifica se deve gerar alertas"""
        # Alerta por tempo de resposta alto
        if metric.duration_ms > self.alert_thresholds['response_time_ms']:
            self._create_alert(
                'performance',
                'warning',
                f"Operação {metric.operation} demorou {metric.duration_ms:.2f}ms"
            )
        
        # Alerta por erro
        if not metric.success:
            self._create_alert(
                'error',
                'critical',
                f"Erro na operação {metric.operation}: {metric.error_message}"
            )
    
    def _create_alert(self, alert_type: str, severity: str, message: str):
        """Cria um alerta"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO alerts (timestamp, alert_type, severity, message)
                VALUES (?, ?, ?, ?)
            """, (datetime.now().isoformat(), alert_type, severity, message))
            
            conn.commit()
            conn.close()
            
            print(f"🚨 ALERTA [{severity.upper()}]: {message}")
            
        except Exception as e:
            print(f"Erro ao criar alerta: {e}")
    
    def _cleanup_old_data(self):
        """Remove dados antigos"""
        while True:
            try:
                cutoff_date = (datetime.now() - timedelta(days=7)).isoformat()
                
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Remover métricas antigas
                cursor.execute("DELETE FROM performance_metrics WHERE timestamp < ?", (cutoff_date,))
                cursor.execute("DELETE FROM system_health WHERE timestamp < ?", (cutoff_date,))
                cursor.execute("DELETE FROM alerts WHERE timestamp < ? AND resolved = TRUE", (cutoff_date,))
                
                conn.commit()
                conn.close()
                
                time.sleep(3600)  # Limpeza a cada hora
                
            except Exception as e:
                print(f"Erro na limpeza: {e}")
                time.sleep(3600)
    
    def get_current_metrics(self) -> Dict:
        """Retorna métricas atuais"""
        with self.lock:
            # Calcular estatísticas dos últimos 5 minutos
            recent_metrics = [m for m in self.metrics_buffer 
                            if (datetime.now() - m.timestamp).seconds < 300]
            
            if not recent_metrics:
                return {
                    'total_operations': 0,
                    'success_rate': 100.0,
                    'avg_response_time': 0,
                    'operations_per_minute': 0
                }
            
            successful = [m for m in recent_metrics if m.success]
            success_rate = len(successful) / len(recent_metrics) * 100
            
            response_times = [m.duration_ms for m in recent_metrics]
            avg_response_time = statistics.mean(response_times)
            
            operations_per_minute = len(recent_metrics) / 5  # últimos 5 minutos
            
            return {
                'total_operations': len(recent_metrics),
                'success_rate': success_rate,
                'avg_response_time': avg_response_time,
                'operations_per_minute': operations_per_minute,
                'active_operations': dict(self.active_operations),
                'error_counts': dict(self.error_counts)
            }
    
    def get_health_status(self) -> Dict:
        """Retorna status de saúde atual"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM system_health 
                ORDER BY timestamp DESC 
                LIMIT 1
            """)
            
            row = cursor.fetchone()
            conn.close()
            
            if row:
                return {
                    'timestamp': row[1],
                    'cpu_usage': row[2],
                    'memory_usage': row[3],
                    'active_connections': row[4],
                    'response_time_avg': row[5],
                    'error_rate': row[6],
                    'status': row[7]
                }
            else:
                return {'status': 'unknown'}
                
        except Exception as e:
            print(f"Erro ao obter saúde: {e}")
            return {'status': 'error', 'message': str(e)}

# Instância global do monitor
monitor = PerformanceMonitor()

def track_operation(operation_name: str):
    """Decorator para rastrear operações"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            error_message = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            finally:
                duration_ms = (time.time() - start_time) * 1000
                monitor.record_operation(operation_name, duration_ms, success, error_message=error_message)
        
        return wrapper
    return decorator
