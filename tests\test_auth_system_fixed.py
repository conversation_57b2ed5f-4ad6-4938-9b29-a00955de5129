"""
Testes Unitários Corrigidos - Sistema de Autenticação CryptoSignals
Validação dos métodos reais do SaaSAuthManager
"""

import unittest
import sys
import os
import tempfile
import sqlite3

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from saas_auth import SaaSAuthManager, PlanType
from datetime import datetime, timedelta

class TestSaaSAuthManagerFixed(unittest.TestCase):
    """Testes corrigidos para o sistema de autenticação SaaS"""
    
    def setUp(self):
        """Configuração inicial para cada teste"""
        # Criar banco de dados temporário
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        
        # Inicializar auth manager com banco temporário
        self.auth_manager = SaaSAuthManager(db_path=self.test_db.name)
        
        # Dados de teste
        self.test_email = '<EMAIL>'
        self.test_password = 'TestPassword123!'
        self.test_plan = PlanType.FREE
    
    def tearDown(self):
        """Limpeza após cada teste"""
        # Remover banco temporário
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)
    
    def test_database_initialization(self):
        """Testa se o banco de dados é inicializado corretamente"""
        # Verificar se as tabelas foram criadas
        conn = sqlite3.connect(self.test_db.name)
        cursor = conn.cursor()
        
        # Verificar tabela users
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        self.assertIsNotNone(cursor.fetchone())
        
        # Verificar tabela api_usage
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_usage'")
        self.assertIsNotNone(cursor.fetchone())
        
        # Verificar tabela payments
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='payments'")
        self.assertIsNotNone(cursor.fetchone())
        
        conn.close()
    
    def test_create_user_success(self):
        """Testa criação de usuário bem-sucedida"""
        user = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        self.assertIsNotNone(user)
        self.assertEqual(user.email, self.test_email)
        self.assertEqual(user.plan, self.test_plan)
        self.assertIsNotNone(user.id)
        self.assertIsNotNone(user.api_key)
    
    def test_create_user_duplicate_email(self):
        """Testa criação de usuário com email duplicado"""
        # Criar primeiro usuário
        user1 = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        # Tentar criar segundo usuário com mesmo email
        user2 = self.auth_manager.create_user(
            email=self.test_email,
            password='DifferentPassword123!',
            plan=PlanType.STARTER
        )
        
        self.assertIsNotNone(user1)
        self.assertIsNone(user2)  # Deve falhar por email duplicado
    
    def test_authenticate_user_success(self):
        """Testa autenticação bem-sucedida"""
        # Criar usuário
        created_user = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        # Autenticar usuário
        authenticated_user = self.auth_manager.authenticate_user(
            email=self.test_email,
            password=self.test_password
        )
        
        self.assertIsNotNone(authenticated_user)
        self.assertEqual(authenticated_user.email, self.test_email)
        self.assertEqual(authenticated_user.id, created_user.id)
    
    def test_authenticate_user_wrong_password(self):
        """Testa autenticação com senha incorreta"""
        # Criar usuário
        self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        # Tentar autenticar com senha errada
        authenticated_user = self.auth_manager.authenticate_user(
            email=self.test_email,
            password='WrongPassword123!'
        )
        
        self.assertIsNone(authenticated_user)
    
    def test_authenticate_user_nonexistent(self):
        """Testa autenticação de usuário inexistente"""
        authenticated_user = self.auth_manager.authenticate_user(
            email='<EMAIL>',
            password='AnyPassword123!'
        )
        
        self.assertIsNone(authenticated_user)
    
    def test_get_user_by_id(self):
        """Testa recuperação de usuário por ID"""
        # Criar usuário
        created_user = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        # Recuperar usuário por ID
        retrieved_user = self.auth_manager.get_user_by_id(created_user.id)
        
        self.assertIsNotNone(retrieved_user)
        self.assertEqual(retrieved_user.email, self.test_email)
        self.assertEqual(retrieved_user.id, created_user.id)
        self.assertEqual(retrieved_user.plan, self.test_plan)
    
    def test_get_user_by_id_nonexistent(self):
        """Testa recuperação de usuário inexistente por ID"""
        retrieved_user = self.auth_manager.get_user_by_id('nonexistent_id')
        self.assertIsNone(retrieved_user)
    
    def test_password_hashing(self):
        """Testa sistema de hash de senhas"""
        password = 'TestPassword123!'
        
        # Testar hash
        password_hash = self.auth_manager.hash_password(password)
        self.assertIsNotNone(password_hash)
        self.assertIn(':', password_hash)  # Formato salt:hash
        
        # Testar verificação
        is_valid = self.auth_manager.verify_password(password, password_hash)
        self.assertTrue(is_valid)
        
        # Testar senha incorreta
        is_invalid = self.auth_manager.verify_password('WrongPassword', password_hash)
        self.assertFalse(is_invalid)
    
    def test_generate_api_key(self):
        """Testa geração de chave API"""
        api_key = self.auth_manager.generate_api_key()
        
        self.assertIsNotNone(api_key)
        self.assertTrue(api_key.startswith('ca_'))
        self.assertEqual(len(api_key), 35)  # 'ca_' + 32 caracteres
    
    def test_jwt_token_generation_and_verification(self):
        """Testa geração e verificação de tokens JWT"""
        # Criar usuário
        user = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        # Gerar token
        token = self.auth_manager.generate_jwt_token(user)
        self.assertIsNotNone(token)
        self.assertIsInstance(token, str)
        
        # Verificar token
        payload = self.auth_manager.verify_jwt_token(token)
        self.assertIsNotNone(payload)
        self.assertEqual(payload['user_id'], user.id)
        self.assertEqual(payload['email'], user.email)
    
    def test_jwt_token_invalid(self):
        """Testa verificação de token JWT inválido"""
        invalid_token = 'invalid.jwt.token'
        payload = self.auth_manager.verify_jwt_token(invalid_token)
        self.assertIsNone(payload)
    
    def test_plan_types(self):
        """Testa tipos de planos disponíveis"""
        self.assertEqual(PlanType.FREE.value, 'free')
        self.assertEqual(PlanType.STARTER.value, 'starter')
        self.assertEqual(PlanType.PROFESSIONAL.value, 'professional')
        self.assertEqual(PlanType.ENTERPRISE.value, 'enterprise')
    
    def test_plan_limits_access(self):
        """Testa acesso aos limites dos planos"""
        # Verificar se os planos estão definidos
        self.assertIn(PlanType.FREE, self.auth_manager.plans)
        self.assertIn(PlanType.STARTER, self.auth_manager.plans)
        self.assertIn(PlanType.PROFESSIONAL, self.auth_manager.plans)
        self.assertIn(PlanType.ENTERPRISE, self.auth_manager.plans)
        
        # Verificar estrutura dos planos
        free_plan = self.auth_manager.plans[PlanType.FREE]
        self.assertEqual(free_plan.name, 'Free')
        self.assertEqual(free_plan.price_monthly, 0.0)
        self.assertIn('api_calls_per_hour', free_plan.limits)
    
    def test_api_usage_logging(self):
        """Testa log de uso da API"""
        # Criar usuário
        user = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=self.test_plan
        )
        
        # Log de uso da API
        self.auth_manager.log_api_usage(user.id, 'test_endpoint', '127.0.0.1')
        
        # Verificar se foi registrado
        usage_count = self.auth_manager.get_hourly_api_usage(user.id)
        self.assertGreaterEqual(usage_count, 1)
    
    def test_check_user_limits(self):
        """Testa verificação de limites do usuário"""
        # Criar usuário
        user = self.auth_manager.create_user(
            email=self.test_email,
            password=self.test_password,
            plan=PlanType.FREE
        )
        
        # Verificar se pode fazer chamada API
        can_call = self.auth_manager.check_user_limits(user, 'api_call')
        self.assertTrue(can_call)  # Usuário novo deve poder fazer chamadas
    
    def test_payment_info(self):
        """Testa informações de pagamento"""
        # Testar plano mensal
        payment_info_monthly = self.auth_manager.get_payment_info(PlanType.STARTER, annual=False)
        self.assertEqual(payment_info_monthly['plan'], 'starter')
        self.assertEqual(payment_info_monthly['amount'], 29.0)
        self.assertEqual(payment_info_monthly['currency'], 'USDT')
        
        # Testar plano anual
        payment_info_annual = self.auth_manager.get_payment_info(PlanType.STARTER, annual=True)
        self.assertEqual(payment_info_annual['amount'], 290.0)
        self.assertTrue(payment_info_annual['annual'])

class TestAuthSystemIntegrationFixed(unittest.TestCase):
    """Testes de integração corrigidos para o sistema de autenticação"""
    
    def setUp(self):
        """Configuração inicial para testes de integração"""
        self.test_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.test_db.close()
        self.auth_manager = SaaSAuthManager(db_path=self.test_db.name)
    
    def tearDown(self):
        """Limpeza após testes de integração"""
        if os.path.exists(self.test_db.name):
            os.unlink(self.test_db.name)
    
    def test_complete_user_workflow(self):
        """Testa fluxo completo do usuário: criação → autenticação → uso"""
        # 1. Criar usuário
        user = self.auth_manager.create_user(
            email='<EMAIL>',
            password='IntegrationTest123!',
            plan=PlanType.FREE
        )
        
        self.assertIsNotNone(user)
        
        # 2. Autenticar usuário
        authenticated_user = self.auth_manager.authenticate_user(
            email='<EMAIL>',
            password='IntegrationTest123!'
        )
        
        self.assertIsNotNone(authenticated_user)
        self.assertEqual(authenticated_user.id, user.id)
        
        # 3. Gerar token JWT
        token = self.auth_manager.generate_jwt_token(authenticated_user)
        self.assertIsNotNone(token)
        
        # 4. Verificar token
        payload = self.auth_manager.verify_jwt_token(token)
        self.assertIsNotNone(payload)
        self.assertEqual(payload['user_id'], user.id)
        
        # 5. Log de uso da API
        self.auth_manager.log_api_usage(user.id, 'test_endpoint', '127.0.0.1')
        usage = self.auth_manager.get_hourly_api_usage(user.id)
        self.assertGreater(usage, 0)
        
        # 6. Verificar limites
        can_use = self.auth_manager.check_user_limits(user, 'api_call')
        self.assertTrue(can_use)

if __name__ == '__main__':
    # Configurar suite de testes
    unittest.main(verbosity=2)
