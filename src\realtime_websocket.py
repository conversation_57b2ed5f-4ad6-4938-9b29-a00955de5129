"""
Sistema de WebSockets para Dados em Tempo Real - CryptoSignals
Streaming de dados de criptomoedas e notificações em tempo real
"""

import asyncio
import websockets
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
from collections import defaultdict, deque

try:
    from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
    SOCKETIO_AVAILABLE = True
except ImportError:
    SOCKETIO_AVAILABLE = False
    print("⚠️  Flask-SocketIO não disponível, usando WebSockets básicos")

class MessageType(Enum):
    """Tipos de mensagens WebSocket"""
    PRICE_UPDATE = "price_update"
    PREDICTION_UPDATE = "prediction_update"
    ALERT = "alert"
    ANALYSIS_COMPLETE = "analysis_complete"
    SYSTEM_STATUS = "system_status"
    USER_NOTIFICATION = "user_notification"
    MARKET_NEWS = "market_news"
    PORTFOLIO_UPDATE = "portfolio_update"

class SubscriptionType(Enum):
    """Tipos de subscrições"""
    PRICE_FEED = "price_feed"
    PREDICTIONS = "predictions"
    ALERTS = "alerts"
    ANALYSIS = "analysis"
    PORTFOLIO = "portfolio"
    NEWS = "news"
    SYSTEM = "system"

@dataclass
class WebSocketMessage:
    """Estrutura de mensagem WebSocket"""
    type: MessageType
    data: Dict[str, Any]
    timestamp: datetime
    user_id: Optional[str] = None
    symbol: Optional[str] = None
    room: Optional[str] = None

@dataclass
class ClientConnection:
    """Informações de conexão do cliente"""
    client_id: str
    user_id: Optional[str]
    subscriptions: Set[SubscriptionType]
    symbols: Set[str]
    connected_at: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str

class RealtimeDataManager:
    """Gerenciador de dados em tempo real"""
    
    def __init__(self):
        self.clients: Dict[str, ClientConnection] = {}
        self.price_feeds: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.prediction_cache: Dict[str, Any] = {}
        self.alert_queue: deque = deque(maxlen=10000)
        self.news_feed: deque = deque(maxlen=100)
        self.system_status: Dict[str, Any] = {}
        
        # Callbacks para diferentes tipos de dados
        self.data_callbacks: Dict[MessageType, List[Callable]] = defaultdict(list)
        
        # Estatísticas
        self.stats = {
            'messages_sent': 0,
            'connections_total': 0,
            'active_connections': 0,
            'data_points_processed': 0
        }
        
        # Iniciar threads de processamento
        self._start_background_tasks()
    
    def register_client(self, client_id: str, user_id: Optional[str] = None, 
                       ip_address: str = "unknown", user_agent: str = "unknown") -> ClientConnection:
        """Registra novo cliente"""
        connection = ClientConnection(
            client_id=client_id,
            user_id=user_id,
            subscriptions=set(),
            symbols=set(),
            connected_at=datetime.now(),
            last_activity=datetime.now(),
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.clients[client_id] = connection
        self.stats['connections_total'] += 1
        self.stats['active_connections'] = len(self.clients)
        
        print(f"🔗 Cliente conectado: {client_id} (User: {user_id})")
        return connection
    
    def unregister_client(self, client_id: str):
        """Remove cliente"""
        if client_id in self.clients:
            del self.clients[client_id]
            self.stats['active_connections'] = len(self.clients)
            print(f"🔌 Cliente desconectado: {client_id}")
    
    def subscribe_client(self, client_id: str, subscription_type: SubscriptionType, 
                        symbols: List[str] = None):
        """Subscreve cliente a um tipo de dados"""
        if client_id not in self.clients:
            return False
        
        client = self.clients[client_id]
        client.subscriptions.add(subscription_type)
        client.last_activity = datetime.now()
        
        if symbols:
            client.symbols.update(symbols)
        
        print(f"📡 Cliente {client_id} subscrito a {subscription_type.value}")
        return True
    
    def unsubscribe_client(self, client_id: str, subscription_type: SubscriptionType):
        """Remove subscrição do cliente"""
        if client_id not in self.clients:
            return False
        
        client = self.clients[client_id]
        client.subscriptions.discard(subscription_type)
        client.last_activity = datetime.now()
        
        return True
    
    def add_price_data(self, symbol: str, price_data: Dict[str, Any]):
        """Adiciona dados de preço"""
        self.price_feeds[symbol].append({
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            **price_data
        })
        
        self.stats['data_points_processed'] += 1
        
        # Broadcast para clientes interessados
        message = WebSocketMessage(
            type=MessageType.PRICE_UPDATE,
            data={
                'symbol': symbol,
                'price': price_data.get('price', 0),
                'change': price_data.get('change', 0),
                'volume': price_data.get('volume', 0),
                'timestamp': datetime.now().isoformat()
            },
            timestamp=datetime.now(),
            symbol=symbol
        )
        
        self._broadcast_message(message, SubscriptionType.PRICE_FEED)
    
    def add_prediction(self, symbol: str, prediction_data: Dict[str, Any]):
        """Adiciona predição"""
        self.prediction_cache[symbol] = {
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            **prediction_data
        }
        
        # Broadcast predição
        message = WebSocketMessage(
            type=MessageType.PREDICTION_UPDATE,
            data=self.prediction_cache[symbol],
            timestamp=datetime.now(),
            symbol=symbol
        )
        
        self._broadcast_message(message, SubscriptionType.PREDICTIONS)
    
    def add_alert(self, user_id: str, alert_data: Dict[str, Any]):
        """Adiciona alerta para usuário"""
        alert = {
            'id': str(uuid.uuid4()),
            'user_id': user_id,
            'timestamp': datetime.now().isoformat(),
            **alert_data
        }
        
        self.alert_queue.append(alert)
        
        # Enviar alerta para usuário específico
        message = WebSocketMessage(
            type=MessageType.ALERT,
            data=alert,
            timestamp=datetime.now(),
            user_id=user_id
        )
        
        self._send_to_user(user_id, message)
    
    def add_analysis_result(self, symbol: str, analysis_data: Dict[str, Any]):
        """Adiciona resultado de análise"""
        message = WebSocketMessage(
            type=MessageType.ANALYSIS_COMPLETE,
            data={
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                **analysis_data
            },
            timestamp=datetime.now(),
            symbol=symbol
        )
        
        self._broadcast_message(message, SubscriptionType.ANALYSIS)
    
    def add_news(self, news_data: Dict[str, Any]):
        """Adiciona notícia"""
        news_item = {
            'id': str(uuid.uuid4()),
            'timestamp': datetime.now().isoformat(),
            **news_data
        }
        
        self.news_feed.append(news_item)
        
        # Broadcast notícia
        message = WebSocketMessage(
            type=MessageType.MARKET_NEWS,
            data=news_item,
            timestamp=datetime.now()
        )
        
        self._broadcast_message(message, SubscriptionType.NEWS)
    
    def update_system_status(self, status_data: Dict[str, Any]):
        """Atualiza status do sistema"""
        self.system_status.update(status_data)
        self.system_status['last_update'] = datetime.now().isoformat()
        
        # Broadcast status
        message = WebSocketMessage(
            type=MessageType.SYSTEM_STATUS,
            data=self.system_status,
            timestamp=datetime.now()
        )
        
        self._broadcast_message(message, SubscriptionType.SYSTEM)
    
    def _broadcast_message(self, message: WebSocketMessage, subscription_type: SubscriptionType):
        """Envia mensagem para todos os clientes subscritos"""
        for client_id, client in self.clients.items():
            if subscription_type in client.subscriptions:
                # Verificar se cliente está interessado no símbolo
                if message.symbol and client.symbols and message.symbol not in client.symbols:
                    continue
                
                self._send_to_client(client_id, message)
    
    def _send_to_user(self, user_id: str, message: WebSocketMessage):
        """Envia mensagem para usuário específico"""
        for client_id, client in self.clients.items():
            if client.user_id == user_id:
                self._send_to_client(client_id, message)
    
    def _send_to_client(self, client_id: str, message: WebSocketMessage):
        """Envia mensagem para cliente específico"""
        try:
            # Executar callbacks registrados
            for callback in self.data_callbacks[message.type]:
                callback(client_id, message)
            
            self.stats['messages_sent'] += 1
            
        except Exception as e:
            print(f"❌ Erro ao enviar mensagem para {client_id}: {e}")
    
    def register_callback(self, message_type: MessageType, callback: Callable):
        """Registra callback para tipo de mensagem"""
        self.data_callbacks[message_type].append(callback)
    
    def get_client_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas dos clientes"""
        subscription_stats = defaultdict(int)
        symbol_stats = defaultdict(int)
        
        for client in self.clients.values():
            for sub in client.subscriptions:
                subscription_stats[sub.value] += 1
            for symbol in client.symbols:
                symbol_stats[symbol] += 1
        
        return {
            'total_connections': self.stats['connections_total'],
            'active_connections': self.stats['active_connections'],
            'messages_sent': self.stats['messages_sent'],
            'data_points_processed': self.stats['data_points_processed'],
            'subscription_stats': dict(subscription_stats),
            'popular_symbols': dict(sorted(symbol_stats.items(), key=lambda x: x[1], reverse=True)[:10])
        }
    
    def _start_background_tasks(self):
        """Inicia tarefas em background"""
        # Thread para limpeza de conexões inativas
        cleanup_thread = threading.Thread(target=self._cleanup_inactive_clients, daemon=True)
        cleanup_thread.start()
        
        # Thread para heartbeat
        heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        heartbeat_thread.start()
    
    def _cleanup_inactive_clients(self):
        """Remove clientes inativos"""
        while True:
            try:
                now = datetime.now()
                inactive_clients = []
                
                for client_id, client in self.clients.items():
                    if (now - client.last_activity).seconds > 3600:  # 1 hora inativo
                        inactive_clients.append(client_id)
                
                for client_id in inactive_clients:
                    self.unregister_client(client_id)
                
                time.sleep(300)  # Verificar a cada 5 minutos
                
            except Exception as e:
                print(f"❌ Erro na limpeza de clientes: {e}")
                time.sleep(300)
    
    def _heartbeat_loop(self):
        """Loop de heartbeat para manter conexões ativas"""
        while True:
            try:
                # Enviar heartbeat para todos os clientes
                heartbeat_message = WebSocketMessage(
                    type=MessageType.SYSTEM_STATUS,
                    data={
                        'type': 'heartbeat',
                        'timestamp': datetime.now().isoformat(),
                        'server_time': time.time()
                    },
                    timestamp=datetime.now()
                )
                
                for client_id in list(self.clients.keys()):
                    self._send_to_client(client_id, heartbeat_message)
                
                time.sleep(30)  # Heartbeat a cada 30 segundos
                
            except Exception as e:
                print(f"❌ Erro no heartbeat: {e}")
                time.sleep(30)

class FlaskSocketIOHandler:
    """Handler para Flask-SocketIO"""
    
    def __init__(self, socketio: SocketIO, data_manager: RealtimeDataManager):
        self.socketio = socketio
        self.data_manager = data_manager
        self._setup_handlers()
    
    def _setup_handlers(self):
        """Configura handlers do SocketIO"""
        
        @self.socketio.on('connect')
        def handle_connect():
            client_id = str(uuid.uuid4())
            # Registrar cliente no data manager
            self.data_manager.register_client(client_id)
            emit('connected', {'client_id': client_id})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            # Remover cliente (precisaria do client_id)
            pass
        
        @self.socketio.on('subscribe')
        def handle_subscribe(data):
            client_id = data.get('client_id')
            subscription_type = SubscriptionType(data.get('type'))
            symbols = data.get('symbols', [])
            
            success = self.data_manager.subscribe_client(client_id, subscription_type, symbols)
            emit('subscription_result', {'success': success, 'type': subscription_type.value})
        
        @self.socketio.on('unsubscribe')
        def handle_unsubscribe(data):
            client_id = data.get('client_id')
            subscription_type = SubscriptionType(data.get('type'))
            
            success = self.data_manager.unsubscribe_client(client_id, subscription_type)
            emit('unsubscription_result', {'success': success, 'type': subscription_type.value})
        
        # Registrar callback para enviar mensagens via SocketIO
        def socketio_callback(client_id: str, message: WebSocketMessage):
            self.socketio.emit(message.type.value, asdict(message))
        
        for message_type in MessageType:
            self.data_manager.register_callback(message_type, socketio_callback)

# Instância global do gerenciador de dados em tempo real
realtime_manager = RealtimeDataManager()

# Função para integração com Flask-SocketIO
def setup_socketio(app, socketio):
    """Configura SocketIO com a aplicação Flask"""
    if SOCKETIO_AVAILABLE:
        handler = FlaskSocketIOHandler(socketio, realtime_manager)
        return handler
    else:
        print("⚠️  Flask-SocketIO não disponível")
        return None
