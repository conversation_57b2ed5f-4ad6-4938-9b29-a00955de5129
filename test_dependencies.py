#!/usr/bin/env python3
"""
Script para testar dependências básicas do CryptoSignals
"""

import sys
import os

def test_basic_imports():
    """Testa importações básicas"""
    try:
        print("🔍 Testando importações básicas...")
        
        # Importações básicas
        import flask
        print("✅ Flask:", flask.__version__)
        
        import pandas
        print("✅ Pandas:", pandas.__version__)
        
        import numpy
        print("✅ NumPy:", numpy.__version__)
        
        import plotly
        print("✅ Plotly:", plotly.__version__)
        
        import requests
        print("✅ Requests:", requests.__version__)
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        return False

def test_optional_imports():
    """Testa importações opcionais"""
    optional_modules = [
        'textblob',
        'feedparser', 
        'scikit-learn',
        'yfinance',
        'ta',
        'seaborn'
    ]
    
    print("\n🔍 Testando importações opcionais...")
    missing = []
    
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - não instalado")
            missing.append(module)
    
    return missing

def install_missing_packages(missing):
    """Instala pacotes em falta"""
    if not missing:
        return True
        
    print(f"\n📦 Instalando pacotes em falta: {', '.join(missing)}")
    
    import subprocess
    try:
        cmd = [sys.executable, '-m', 'pip', 'install'] + missing
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Pacotes instalados com sucesso!")
            return True
        else:
            print(f"❌ Erro na instalação: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao instalar pacotes: {e}")
        return False

def test_flask_app():
    """Testa se consegue criar uma aplicação Flask básica"""
    try:
        print("\n🌐 Testando Flask básico...")
        from flask import Flask
        
        app = Flask(__name__)
        
        @app.route('/')
        def hello():
            return "CryptoSignals Test OK!"
        
        print("✅ Flask app criado com sucesso!")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar Flask app: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 CryptoSignals - Teste de Dependências")
    print("=" * 50)
    
    # Testar importações básicas
    if not test_basic_imports():
        print("\n❌ Falha nas importações básicas. Abortando.")
        return False
    
    # Testar importações opcionais
    missing = test_optional_imports()
    
    # Instalar pacotes em falta
    if missing:
        if not install_missing_packages(missing):
            print("\n⚠️  Alguns pacotes não puderam ser instalados.")
            print("Você pode tentar instalar manualmente:")
            print(f"pip install {' '.join(missing)}")
    
    # Testar Flask
    if not test_flask_app():
        return False
    
    print("\n✅ Todos os testes passaram!")
    print("🎯 O sistema está pronto para ser executado.")
    print("\nPara iniciar o CryptoSignals:")
    print("python cryptosignals_app.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
