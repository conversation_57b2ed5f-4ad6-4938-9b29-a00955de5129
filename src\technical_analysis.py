"""
Módulo para análise técnica do Bitcoin.
Implementa indicadores técnicos e funções de análise.
"""

import pandas as pd
import numpy as np
import ta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class TechnicalAnalyzer:
    """Classe para análise técnica de dados do Bitcoin."""
    
    def __init__(self, df: pd.DataFrame):
        """
        Inicializa o analisador técnico.
        
        Args:
            df: DataFrame com dados OHLCV
        """
        self.df = df.copy()
        self.indicators = {}
        
    def calculate_moving_averages(self) -> Dict[str, pd.Series]:
        """
        Calcula médias móveis simples e exponenciais.
        
        Returns:
            Dicionário com médias móveis
        """
        mas = {}
        
        # Médias móveis simples
        for period in [7, 14, 21, 50, 100, 200]:
            mas[f'SMA_{period}'] = self.df['Close'].rolling(window=period).mean()
        
        # Médias móveis exponenciais
        for period in [12, 26, 50]:
            mas[f'EMA_{period}'] = self.df['Close'].ewm(span=period).mean()
        
        self.indicators.update(mas)
        return mas
    
    def calculate_rsi(self, period: int = 14) -> pd.Series:
        """
        Calcula o Relative Strength Index (RSI).
        
        Args:
            period: Período para cálculo do RSI
            
        Returns:
            Série com valores do RSI
        """
        rsi = ta.momentum.RSIIndicator(
            close=self.df['Close'],
            window=period
        ).rsi()
        
        self.indicators[f'RSI_{period}'] = rsi
        return rsi
    
    def calculate_macd(self) -> Dict[str, pd.Series]:
        """
        Calcula o MACD (Moving Average Convergence Divergence).
        
        Returns:
            Dicionário com MACD, Signal e Histogram
        """
        macd_indicator = ta.trend.MACD(close=self.df['Close'])
        
        macd_data = {
            'MACD': macd_indicator.macd(),
            'MACD_Signal': macd_indicator.macd_signal(),
            'MACD_Histogram': macd_indicator.macd_diff()
        }
        
        self.indicators.update(macd_data)
        return macd_data
    
    def calculate_bollinger_bands(self, period: int = 20, std_dev: int = 2) -> Dict[str, pd.Series]:
        """
        Calcula as Bandas de Bollinger.
        
        Args:
            period: Período para média móvel
            std_dev: Número de desvios padrão
            
        Returns:
            Dicionário com bandas superior, inferior e média
        """
        bb_indicator = ta.volatility.BollingerBands(
            close=self.df['Close'],
            window=period,
            window_dev=std_dev
        )
        
        bb_data = {
            'BB_Upper': bb_indicator.bollinger_hband(),
            'BB_Middle': bb_indicator.bollinger_mavg(),
            'BB_Lower': bb_indicator.bollinger_lband(),
            'BB_Width': bb_indicator.bollinger_wband(),
            'BB_Percent': bb_indicator.bollinger_pband()
        }
        
        self.indicators.update(bb_data)
        return bb_data
    
    def calculate_stochastic(self, k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """
        Calcula o Oscilador Estocástico.
        
        Args:
            k_period: Período para %K
            d_period: Período para %D
            
        Returns:
            Dicionário com %K e %D
        """
        stoch_indicator = ta.momentum.StochasticOscillator(
            high=self.df['High'],
            low=self.df['Low'],
            close=self.df['Close'],
            window=k_period,
            smooth_window=d_period
        )
        
        stoch_data = {
            'Stoch_K': stoch_indicator.stoch(),
            'Stoch_D': stoch_indicator.stoch_signal()
        }
        
        self.indicators.update(stoch_data)
        return stoch_data
    
    def calculate_atr(self, period: int = 14) -> pd.Series:
        """
        Calcula o Average True Range (ATR).
        
        Args:
            period: Período para cálculo
            
        Returns:
            Série com valores do ATR
        """
        atr = ta.volatility.AverageTrueRange(
            high=self.df['High'],
            low=self.df['Low'],
            close=self.df['Close'],
            window=period
        ).average_true_range()
        
        self.indicators[f'ATR_{period}'] = atr
        return atr
    
    def calculate_volume_indicators(self) -> Dict[str, pd.Series]:
        """
        Calcula indicadores baseados em volume.
        
        Returns:
            Dicionário com indicadores de volume
        """
        volume_indicators = {}
        
        # On-Balance Volume
        obv = ta.volume.OnBalanceVolumeIndicator(
            close=self.df['Close'],
            volume=self.df['Volume']
        ).on_balance_volume()
        volume_indicators['OBV'] = obv
        
        # Volume-Price Trend
        vpt = ta.volume.VolumePriceTrendIndicator(
            close=self.df['Close'],
            volume=self.df['Volume']
        ).volume_price_trend()
        volume_indicators['VPT'] = vpt
        
        # Accumulation/Distribution Line
        ad = ta.volume.AccDistIndexIndicator(
            high=self.df['High'],
            low=self.df['Low'],
            close=self.df['Close'],
            volume=self.df['Volume']
        ).acc_dist_index()
        volume_indicators['AD'] = ad
        
        self.indicators.update(volume_indicators)
        return volume_indicators
    
    def calculate_support_resistance(self, window: int = 20) -> Dict[str, List[float]]:
        """
        Identifica níveis de suporte e resistência.
        
        Args:
            window: Janela para identificação de picos e vales
            
        Returns:
            Dicionário com níveis de suporte e resistência
        """
        highs = self.df['High'].rolling(window=window, center=True).max()
        lows = self.df['Low'].rolling(window=window, center=True).min()
        
        # Identificar picos (resistência)
        resistance_levels = []
        for i in range(window, len(self.df) - window):
            if self.df['High'].iloc[i] == highs.iloc[i]:
                resistance_levels.append(self.df['High'].iloc[i])
        
        # Identificar vales (suporte)
        support_levels = []
        for i in range(window, len(self.df) - window):
            if self.df['Low'].iloc[i] == lows.iloc[i]:
                support_levels.append(self.df['Low'].iloc[i])
        
        # Remover duplicatas e ordenar
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)[:5]
        support_levels = sorted(list(set(support_levels)))[:5]
        
        return {
            'resistance': resistance_levels,
            'support': support_levels
        }
    
    def calculate_all_indicators(self) -> pd.DataFrame:
        """
        Calcula todos os indicadores técnicos.
        
        Returns:
            DataFrame com todos os indicadores
        """
        # Calcular todos os indicadores
        self.calculate_moving_averages()
        self.calculate_rsi()
        self.calculate_macd()
        self.calculate_bollinger_bands()
        self.calculate_stochastic()
        self.calculate_atr()
        self.calculate_volume_indicators()
        
        # Criar DataFrame com todos os indicadores
        result_df = self.df.copy()
        
        for name, indicator in self.indicators.items():
            result_df[name] = indicator
        
        return result_df
    
    def get_current_signals(self) -> Dict[str, str]:
        """
        Gera sinais de trading baseados nos indicadores atuais.
        
        Returns:
            Dicionário com sinais de compra/venda
        """
        if not self.indicators:
            self.calculate_all_indicators()
        
        signals = {}
        current_price = self.df['Close'].iloc[-1]
        
        # Sinal RSI
        if 'RSI_14' in self.indicators:
            rsi_current = self.indicators['RSI_14'].iloc[-1]
            if rsi_current < 30:
                signals['RSI'] = 'COMPRA'
            elif rsi_current > 70:
                signals['RSI'] = 'VENDA'
            else:
                signals['RSI'] = 'NEUTRO'
        
        # Sinal MACD
        if 'MACD' in self.indicators and 'MACD_Signal' in self.indicators:
            macd_current = self.indicators['MACD'].iloc[-1]
            macd_signal_current = self.indicators['MACD_Signal'].iloc[-1]
            
            if macd_current > macd_signal_current:
                signals['MACD'] = 'COMPRA'
            else:
                signals['MACD'] = 'VENDA'
        
        # Sinal Bandas de Bollinger
        if all(key in self.indicators for key in ['BB_Upper', 'BB_Lower']):
            bb_upper = self.indicators['BB_Upper'].iloc[-1]
            bb_lower = self.indicators['BB_Lower'].iloc[-1]
            
            if current_price <= bb_lower:
                signals['Bollinger'] = 'COMPRA'
            elif current_price >= bb_upper:
                signals['Bollinger'] = 'VENDA'
            else:
                signals['Bollinger'] = 'NEUTRO'
        
        # Sinal Médias Móveis
        if 'SMA_50' in self.indicators and 'SMA_200' in self.indicators:
            sma_50 = self.indicators['SMA_50'].iloc[-1]
            sma_200 = self.indicators['SMA_200'].iloc[-1]
            
            if sma_50 > sma_200 and current_price > sma_50:
                signals['MA_Trend'] = 'COMPRA'
            elif sma_50 < sma_200 and current_price < sma_50:
                signals['MA_Trend'] = 'VENDA'
            else:
                signals['MA_Trend'] = 'NEUTRO'
        
        return signals
    
    def get_market_summary(self) -> Dict[str, any]:
        """
        Retorna um resumo do mercado baseado nos indicadores.
        
        Returns:
            Dicionário com resumo do mercado
        """
        if not self.indicators:
            self.calculate_all_indicators()
        
        current_price = self.df['Close'].iloc[-1]
        signals = self.get_current_signals()
        
        # Contar sinais
        buy_signals = sum(1 for signal in signals.values() if signal == 'COMPRA')
        sell_signals = sum(1 for signal in signals.values() if signal == 'VENDA')
        neutral_signals = sum(1 for signal in signals.values() if signal == 'NEUTRO')
        
        # Determinar tendência geral
        if buy_signals > sell_signals:
            overall_trend = 'ALTA'
        elif sell_signals > buy_signals:
            overall_trend = 'BAIXA'
        else:
            overall_trend = 'LATERAL'
        
        return {
            'current_price': current_price,
            'overall_trend': overall_trend,
            'signals': signals,
            'signal_count': {
                'buy': buy_signals,
                'sell': sell_signals,
                'neutral': neutral_signals
            },
            'volatility': self.indicators.get('ATR_14', pd.Series()).iloc[-1] if 'ATR_14' in self.indicators else None,
            'rsi': self.indicators.get('RSI_14', pd.Series()).iloc[-1] if 'RSI_14' in self.indicators else None
        }


def analyze_bitcoin_data(df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
    """
    Função utilitária para análise completa dos dados do Bitcoin.
    
    Args:
        df: DataFrame com dados OHLCV
        
    Returns:
        Tupla com DataFrame com indicadores e resumo do mercado
    """
    analyzer = TechnicalAnalyzer(df)
    indicators_df = analyzer.calculate_all_indicators()
    market_summary = analyzer.get_market_summary()
    
    return indicators_df, market_summary


if __name__ == "__main__":
    # Teste do módulo
    from data_processing import load_bitcoin_data
    
    # Carregar dados
    data = load_bitcoin_data()
    
    # Análise técnica
    analyzer = TechnicalAnalyzer(data)
    indicators = analyzer.calculate_all_indicators()
    summary = analyzer.get_market_summary()
    
    print("Resumo do Mercado:")
    print(f"Preço atual: ${summary['current_price']:.2f}")
    print(f"Tendência geral: {summary['overall_trend']}")
    print(f"RSI: {summary['rsi']:.2f}")
    print("\nSinais:")
    for indicator, signal in summary['signals'].items():
        print(f"{indicator}: {signal}")
