"""
Aplicação principal do dashboard BitcoinAnalytics.
Interface web interativa para análise de múltiplas criptomoedas.
"""

import dash
from dash import dcc, html, Input, Output, callback, State
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import sys
import os
from datetime import datetime

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer
from visualization import BitcoinVisualizer

# Inicializar aplicação Dash
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.BOOTSTRAP, dbc.icons.FONT_AWESOME],
    title="BitcoinAnalytics",
    update_title="Carregando...",
    suppress_callback_exceptions=True
)

# Configurações globais
app.config.suppress_callback_exceptions = True

# Inicializar gerenciador de dados
print("Inicializando CryptoAnalytics...")
data_manager = CryptoDataManager()

# Cache global para dados
current_data = {}
current_analysis = {}

# Layout principal
def create_navbar():
    """Cria barra de navegação."""
    return dbc.NavbarSimple(
        children=[
            dbc.NavItem(dbc.NavLink("Dashboard", href="/", active="exact")),
            dbc.NavItem(dbc.NavLink("Análise Técnica", href="/technical", active="exact")),
            dbc.NavItem(dbc.NavLink("Previsões", href="/predictions", active="exact")),
            dbc.NavItem(dbc.NavLink("Sobre", href="/about", active="exact")),
        ],
        brand="🪙 BitcoinAnalytics",
        brand_href="/",
        color="warning",
        dark=True,
        fluid=True,
    )

def create_summary_cards():
    """Cria cards de resumo do mercado."""
    current_price = market_summary['current_price']
    trend = market_summary['overall_trend']
    rsi = market_summary.get('rsi', 0)

    # Determinar cor da tendência
    trend_color = "success" if trend == "ALTA" else "danger" if trend == "BAIXA" else "warning"

    # Determinar cor do RSI
    rsi_color = "danger" if rsi > 70 else "success" if rsi < 30 else "info"

    cards = dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(f"${current_price:,.2f}", className="card-title text-primary"),
                    html.P("Preço Atual", className="card-text"),
                    html.Small("BTCUSDT", className="text-muted")
                ])
            ], className="h-100")
        ], width=3),

        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(trend, className=f"card-title text-{trend_color}"),
                    html.P("Tendência", className="card-text"),
                    html.Small("Análise Geral", className="text-muted")
                ])
            ], className="h-100")
        ], width=3),

        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(f"{rsi:.1f}", className=f"card-title text-{rsi_color}"),
                    html.P("RSI (14)", className="card-text"),
                    html.Small("Índice de Força", className="text-muted")
                ])
            ], className="h-100")
        ], width=3),

        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4(f"{len(df):,}", className="card-title text-info"),
                    html.P("Registros", className="card-text"),
                    html.Small("Dados Históricos", className="text-muted")
                ])
            ], className="h-100")
        ], width=3),
    ], className="mb-4")

    return cards

def create_dashboard_layout():
    """Cria layout do dashboard principal."""
    return html.Div([
        # Cards de resumo
        create_summary_cards(),

        # Gráficos principais
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("📈 Gráfico de Preços"),
                    dbc.CardBody([
                        dcc.Graph(
                            id="candlestick-chart",
                            figure=visualizer.create_candlestick_chart(analyzer.indicators),
                            config={'displayModeBar': True}
                        )
                    ])
                ])
            ], width=12)
        ], className="mb-4"),

        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("📊 Volume de Negociação"),
                    dbc.CardBody([
                        dcc.Graph(
                            id="volume-chart",
                            figure=visualizer.create_volume_chart(),
                            config={'displayModeBar': True}
                        )
                    ])
                ])
            ], width=6),

            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("📈 Distribuição de Preços"),
                    dbc.CardBody([
                        dcc.Graph(
                            id="distribution-chart",
                            figure=visualizer.create_price_distribution(),
                            config={'displayModeBar': True}
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-4"),

        # Sinais de trading
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("🎯 Sinais de Trading"),
                    dbc.CardBody([
                        create_signals_table()
                    ])
                ])
            ], width=12)
        ])
    ])

def create_signals_table():
    """Cria tabela de sinais de trading."""
    signals = market_summary['signals']

    table_rows = []
    for indicator, signal in signals.items():
        # Determinar cor do sinal
        if signal == "COMPRA":
            color = "success"
            icon = "fas fa-arrow-up"
        elif signal == "VENDA":
            color = "danger"
            icon = "fas fa-arrow-down"
        else:
            color = "warning"
            icon = "fas fa-minus"

        table_rows.append(
            html.Tr([
                html.Td(indicator),
                html.Td([
                    html.I(className=f"{icon} me-2"),
                    html.Span(signal, className=f"text-{color} fw-bold")
                ])
            ])
        )

    return dbc.Table([
        html.Thead([
            html.Tr([
                html.Th("Indicador"),
                html.Th("Sinal")
            ])
        ]),
        html.Tbody(table_rows)
    ], striped=True, bordered=True, hover=True)

def create_technical_layout():
    """Cria layout da análise técnica."""
    return html.Div([
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("📊 Indicadores Técnicos"),
                    dbc.CardBody([
                        dcc.Graph(
                            id="indicators-chart",
                            figure=visualizer.create_indicators_subplot(analyzer.indicators),
                            config={'displayModeBar': True}
                        )
                    ])
                ])
            ], width=12)
        ], className="mb-4"),

        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("🔥 Mapa de Correlação"),
                    dbc.CardBody([
                        dcc.Graph(
                            id="correlation-chart",
                            figure=visualizer.create_correlation_heatmap(),
                            config={'displayModeBar': True}
                        )
                    ])
                ])
            ], width=6),

            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("📈 Análise de Retornos"),
                    dbc.CardBody([
                        dcc.Graph(
                            id="returns-chart",
                            figure=visualizer.create_returns_analysis(),
                            config={'displayModeBar': True}
                        )
                    ])
                ])
            ], width=6)
        ])
    ])

def create_predictions_layout():
    """Cria layout das previsões."""
    return html.Div([
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("🔮 Modelos de Previsão"),
                    dbc.CardBody([
                        html.P("Funcionalidade em desenvolvimento..."),
                        dbc.Button("Treinar Modelos", color="primary", disabled=True)
                    ])
                ])
            ], width=12)
        ])
    ])

def create_about_layout():
    """Cria layout da página sobre."""
    return html.Div([
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("ℹ️ Sobre o BitcoinAnalytics"),
                    dbc.CardBody([
                        html.H5("Plataforma de Análise de Bitcoin"),
                        html.P([
                            "O BitcoinAnalytics é uma plataforma completa para análise de dados do Bitcoin, "
                            "oferecendo ferramentas avançadas de análise técnica, visualização de dados e "
                            "modelos de previsão de preços."
                        ]),
                        html.H6("Funcionalidades:"),
                        html.Ul([
                            html.Li("Análise exploratória de dados em tempo real"),
                            html.Li("Indicadores técnicos avançados"),
                            html.Li("Visualizações interativas"),
                            html.Li("Sinais de trading automatizados"),
                            html.Li("Modelos de previsão de preços")
                        ]),
                        html.Hr(),
                        html.P([
                            html.Strong("Aviso: "),
                            "Esta plataforma é apenas para fins educacionais e de pesquisa. "
                            "Não constitui aconselhamento financeiro."
                        ], className="text-muted")
                    ])
                ])
            ], width=8)
        ], justify="center")
    ])

# Layout principal da aplicação
app.layout = html.Div([
    dcc.Location(id="url", refresh=False),
    create_navbar(),
    dbc.Container([
        html.Div(id="page-content", className="mt-4")
    ], fluid=True)
])

# Callback para navegação entre páginas
@app.callback(
    Output("page-content", "children"),
    Input("url", "pathname")
)
def display_page(pathname):
    """Controla a navegação entre páginas."""
    if pathname == "/technical":
        return create_technical_layout()
    elif pathname == "/predictions":
        return create_predictions_layout()
    elif pathname == "/about":
        return create_about_layout()
    else:
        return create_dashboard_layout()

# Callback para atualização automática (opcional)
@app.callback(
    Output("candlestick-chart", "figure"),
    Input("url", "pathname")
)
def update_candlestick(pathname):
    """Atualiza gráfico de candlestick."""
    if pathname == "/":
        return visualizer.create_candlestick_chart(analyzer.indicators)
    return dash.no_update

if __name__ == "__main__":
    print("Iniciando BitcoinAnalytics Dashboard...")
    print("Acesse: http://localhost:8050")
    app.run_server(debug=True, host="0.0.0.0", port=8050)
