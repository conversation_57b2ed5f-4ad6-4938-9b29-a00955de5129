"""
Gerenciador de dados inteligente para múltiplas criptomoedas.
Sistema de cache com SQLite para evitar arquivos pesados.
"""

import sqlite3
import pandas as pd
import numpy as np
import yfinance as yf
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import os
import json

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CryptoDataManager:
    """Gerenciador inteligente de dados de criptomoedas."""
    
    def __init__(self, db_path: str = "crypto_data.db"):
        """
        Inicializa o gerenciador de dados.
        
        Args:
            db_path: Caminho para o banco de dados SQLite
        """
        self.db_path = db_path
        self.init_database()
        
        # Lista de exchanges/fontes suportadas
        self.supported_sources = {
            'yahoo': self._fetch_yahoo_data,
            'binance': self._fetch_binance_data,
            'coingecko': self._fetch_coingecko_data
        }
        
        # Cache de símbolos populares
        self.popular_symbols = {
            'BTC': ['BTC-USD', 'BTCUSDT'],
            'ETH': ['ETH-USD', 'ETHUSDT'],
            'BNB': ['BNB-USD', 'BNBUSDT'],
            'ADA': ['ADA-USD', 'ADAUSDT'],
            'SOL': ['SOL-USD', 'SOLUSDT'],
            'DOT': ['DOT-USD', 'DOTUSDT'],
            'MATIC': ['MATIC-USD', 'MATICUSDT'],
            'LINK': ['LINK-USD', 'LINKUSDT'],
            'AVAX': ['AVAX-USD', 'AVAXUSDT'],
            'UNI': ['UNI-USD', 'UNIUSDT']
        }
    
    def init_database(self):
        """Inicializa o banco de dados SQLite."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Tabela para dados OHLCV
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS crypto_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                open REAL NOT NULL,
                high REAL NOT NULL,
                low REAL NOT NULL,
                close REAL NOT NULL,
                volume REAL NOT NULL,
                source TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(symbol, timestamp, source)
            )
        ''')
        
        # Tabela para metadados dos símbolos
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS symbol_metadata (
                symbol TEXT PRIMARY KEY,
                name TEXT,
                description TEXT,
                last_updated DATETIME,
                data_count INTEGER DEFAULT 0,
                source TEXT
            )
        ''')
        
        # Tabela para análises salvas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS saved_analyses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_type TEXT NOT NULL,
                analysis_data TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Índices para performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol_timestamp ON crypto_data(symbol, timestamp)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_symbol_analysis ON saved_analyses(symbol, analysis_type)')
        
        conn.commit()
        conn.close()
        logger.info(f"Banco de dados inicializado: {self.db_path}")
    
    def get_available_symbols(self) -> List[str]:
        """Retorna lista de símbolos disponíveis no banco."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT DISTINCT symbol FROM crypto_data ORDER BY symbol')
        symbols = [row[0] for row in cursor.fetchall()]
        
        conn.close()
        return symbols
    
    def search_symbols(self, query: str) -> List[Dict]:
        """
        Busca símbolos disponíveis baseado em uma query.
        
        Args:
            query: Termo de busca (ex: 'BTC', 'Bitcoin')
            
        Returns:
            Lista de símbolos encontrados
        """
        results = []
        query = query.upper()
        
        # Buscar nos símbolos populares
        for symbol, variants in self.popular_symbols.items():
            if query in symbol or any(query in v for v in variants):
                results.append({
                    'symbol': symbol,
                    'variants': variants,
                    'source': 'popular'
                })
        
        # Buscar no banco de dados
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT symbol, name, source, data_count 
            FROM symbol_metadata 
            WHERE symbol LIKE ? OR name LIKE ?
            ORDER BY data_count DESC
        ''', (f'%{query}%', f'%{query}%'))
        
        for row in cursor.fetchall():
            results.append({
                'symbol': row[0],
                'name': row[1],
                'source': row[2],
                'data_count': row[3]
            })
        
        conn.close()
        return results[:10]  # Limitar a 10 resultados
    
    def fetch_crypto_data(self, symbol: str, period: str = '1y', 
                         source: str = 'auto') -> pd.DataFrame:
        """
        Busca dados de uma criptomoeda com cache inteligente.
        
        Args:
            symbol: Símbolo da criptomoeda (ex: 'BTC', 'ETH')
            period: Período dos dados ('1d', '1w', '1m', '3m', '6m', '1y', '2y')
            source: Fonte dos dados ('auto', 'yahoo', 'binance', 'coingecko')
            
        Returns:
            DataFrame com dados OHLCV
        """
        # Normalizar símbolo
        symbol = symbol.upper()
        
        # Verificar cache primeiro
        cached_data = self._get_cached_data(symbol, period)
        if cached_data is not None and len(cached_data) > 0:
            logger.info(f"Dados encontrados no cache para {symbol}")
            return cached_data
        
        # Buscar dados online
        logger.info(f"Buscando dados online para {symbol}...")
        
        if source == 'auto':
            # Tentar diferentes fontes automaticamente
            for src_name, fetch_func in self.supported_sources.items():
                try:
                    data = fetch_func(symbol, period)
                    if data is not None and len(data) > 0:
                        self._cache_data(symbol, data, src_name)
                        return data
                except Exception as e:
                    logger.warning(f"Erro ao buscar de {src_name}: {e}")
                    continue
        else:
            # Usar fonte específica
            if source in self.supported_sources:
                try:
                    data = self.supported_sources[source](symbol, period)
                    if data is not None and len(data) > 0:
                        self._cache_data(symbol, data, source)
                        return data
                except Exception as e:
                    logger.error(f"Erro ao buscar de {source}: {e}")
        
        # Se nada funcionou, retornar dados de exemplo
        logger.warning(f"Não foi possível obter dados para {symbol}. Gerando dados de exemplo.")
        return self._generate_sample_data(symbol)
    
    def _get_cached_data(self, symbol: str, period: str) -> Optional[pd.DataFrame]:
        """Recupera dados do cache se disponíveis e atualizados."""
        conn = sqlite3.connect(self.db_path)
        
        # Calcular data limite baseada no período
        days_map = {'1d': 1, '1w': 7, '1m': 30, '3m': 90, '6m': 180, '1y': 365, '2y': 730}
        days = days_map.get(period, 365)
        start_date = datetime.now() - timedelta(days=days)
        
        # Verificar se temos dados suficientes e recentes
        query = '''
            SELECT timestamp, open, high, low, close, volume
            FROM crypto_data 
            WHERE symbol = ? AND timestamp >= ?
            ORDER BY timestamp
        '''
        
        df = pd.read_sql_query(query, conn, params=(symbol, start_date))
        conn.close()
        
        if len(df) < 100:  # Mínimo de 100 pontos
            return None
        
        # Converter timestamp para datetime e definir como índice
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        # Renomear colunas para padrão
        df.columns = ['Open', 'High', 'Low', 'Close', 'Volume']
        
        return df
    
    def _cache_data(self, symbol: str, data: pd.DataFrame, source: str):
        """Armazena dados no cache."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Preparar dados para inserção
        data_to_insert = []
        for timestamp, row in data.iterrows():
            data_to_insert.append((
                symbol,
                timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                float(row['Open']),
                float(row['High']),
                float(row['Low']),
                float(row['Close']),
                float(row['Volume']),
                source
            ))
        
        # Inserir dados (ignorar duplicatas)
        cursor.executemany('''
            INSERT OR IGNORE INTO crypto_data 
            (symbol, timestamp, open, high, low, close, volume, source)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', data_to_insert)
        
        # Atualizar metadados
        cursor.execute('''
            INSERT OR REPLACE INTO symbol_metadata 
            (symbol, last_updated, data_count, source)
            VALUES (?, ?, ?, ?)
        ''', (symbol, datetime.now(), len(data), source))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Dados de {symbol} armazenados no cache ({len(data)} registros)")
    
    def _fetch_yahoo_data(self, symbol: str, period: str) -> pd.DataFrame:
        """Busca dados do Yahoo Finance."""
        # Tentar diferentes variações do símbolo
        variants = self.popular_symbols.get(symbol, [f"{symbol}-USD"])
        
        for variant in variants:
            try:
                ticker = yf.Ticker(variant)
                data = ticker.history(period=period, interval='1h')
                
                if len(data) > 0:
                    # Padronizar colunas
                    data = data[['Open', 'High', 'Low', 'Close', 'Volume']]
                    return data
                    
            except Exception as e:
                logger.debug(f"Erro ao buscar {variant}: {e}")
                continue
        
        return None
    
    def _fetch_binance_data(self, symbol: str, period: str) -> pd.DataFrame:
        """Busca dados da API da Binance."""
        try:
            # Converter símbolo para formato Binance
            binance_symbol = f"{symbol}USDT"
            
            # Mapear período para Binance
            interval_map = {'1d': '1h', '1w': '4h', '1m': '1d', '3m': '1d', 
                          '6m': '1d', '1y': '1d', '2y': '1d'}
            interval = interval_map.get(period, '1d')
            
            # Calcular limite de dados
            limit_map = {'1d': 24, '1w': 42, '1m': 30, '3m': 90, 
                        '6m': 180, '1y': 365, '2y': 730}
            limit = min(limit_map.get(period, 365), 1000)  # Binance limit
            
            url = "https://api.binance.com/api/v3/klines"
            params = {
                'symbol': binance_symbol,
                'interval': interval,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return None
            
            # Converter para DataFrame
            df = pd.DataFrame(data, columns=[
                'timestamp', 'Open', 'High', 'Low', 'Close', 'Volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # Processar dados
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Converter para float
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df[['Open', 'High', 'Low', 'Close', 'Volume']]
            
        except Exception as e:
            logger.debug(f"Erro ao buscar dados da Binance: {e}")
            return None
    
    def _fetch_coingecko_data(self, symbol: str, period: str) -> pd.DataFrame:
        """Busca dados da API do CoinGecko."""
        try:
            # Mapear período para dias
            days_map = {'1d': 1, '1w': 7, '1m': 30, '3m': 90, '6m': 180, '1y': 365, '2y': 730}
            days = days_map.get(period, 365)
            
            # CoinGecko usa IDs diferentes
            symbol_map = {
                'BTC': 'bitcoin', 'ETH': 'ethereum', 'BNB': 'binancecoin',
                'ADA': 'cardano', 'SOL': 'solana', 'DOT': 'polkadot',
                'MATIC': 'polygon', 'LINK': 'chainlink', 'AVAX': 'avalanche-2',
                'UNI': 'uniswap'
            }
            
            coin_id = symbol_map.get(symbol, symbol.lower())
            
            url = f"https://api.coingecko.com/api/v3/coins/{coin_id}/ohlc"
            params = {'vs_currency': 'usd', 'days': days}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if not data:
                return None
            
            # Converter para DataFrame
            df = pd.DataFrame(data, columns=['timestamp', 'Open', 'High', 'Low', 'Close'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Volume não disponível no CoinGecko OHLC, usar valor padrão
            df['Volume'] = 1000000  # Volume fictício
            
            return df
            
        except Exception as e:
            logger.debug(f"Erro ao buscar dados do CoinGecko: {e}")
            return None
    
    def _generate_sample_data(self, symbol: str) -> pd.DataFrame:
        """Gera dados de exemplo para demonstração."""
        logger.info(f"Gerando dados de exemplo para {symbol}")
        
        # Gerar 1000 pontos de dados
        dates = pd.date_range(
            start=datetime.now() - timedelta(days=365),
            end=datetime.now(),
            freq='1H'
        )[:1000]
        
        # Simular preços com random walk
        np.random.seed(hash(symbol) % 2**32)  # Seed baseado no símbolo
        
        # Preços base diferentes para cada símbolo
        base_prices = {
            'BTC': 45000, 'ETH': 3000, 'BNB': 300, 'ADA': 1.5,
            'SOL': 100, 'DOT': 25, 'MATIC': 1.2, 'LINK': 15,
            'AVAX': 80, 'UNI': 8
        }
        
        initial_price = base_prices.get(symbol, 100)
        returns = np.random.normal(0, 0.02, len(dates))
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Criar OHLC
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            high = close * (1 + abs(np.random.normal(0, 0.01)))
            low = close * (1 - abs(np.random.normal(0, 0.01)))
            open_price = prices[i-1] if i > 0 else close
            volume = np.random.uniform(100000, 1000000)
            
            data.append({
                'Open': open_price,
                'High': high,
                'Low': low,
                'Close': close,
                'Volume': volume
            })
        
        df = pd.DataFrame(data, index=dates)
        return df
    
    def save_analysis(self, symbol: str, analysis_type: str, analysis_data: Dict):
        """Salva uma análise no banco de dados."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO saved_analyses (symbol, analysis_type, analysis_data)
            VALUES (?, ?, ?)
        ''', (symbol, analysis_type, json.dumps(analysis_data)))
        
        conn.commit()
        conn.close()
        
        logger.info(f"Análise {analysis_type} salva para {symbol}")
    
    def get_saved_analyses(self, symbol: str = None) -> List[Dict]:
        """Recupera análises salvas."""
        conn = sqlite3.connect(self.db_path)
        
        if symbol:
            query = '''
                SELECT symbol, analysis_type, analysis_data, created_at
                FROM saved_analyses 
                WHERE symbol = ?
                ORDER BY created_at DESC
            '''
            params = (symbol,)
        else:
            query = '''
                SELECT symbol, analysis_type, analysis_data, created_at
                FROM saved_analyses 
                ORDER BY created_at DESC
                LIMIT 50
            '''
            params = ()
        
        cursor = conn.cursor()
        cursor.execute(query, params)
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'symbol': row[0],
                'analysis_type': row[1],
                'analysis_data': json.loads(row[2]),
                'created_at': row[3]
            })
        
        conn.close()
        return results
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """Remove dados antigos para economizar espaço."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # Remover dados antigos
        cursor.execute('DELETE FROM crypto_data WHERE timestamp < ?', (cutoff_date,))
        cursor.execute('DELETE FROM saved_analyses WHERE created_at < ?', (cutoff_date,))
        
        # Vacuum para recuperar espaço
        cursor.execute('VACUUM')
        
        conn.commit()
        conn.close()
        
        logger.info(f"Dados anteriores a {cutoff_date.date()} removidos")
    
    def get_database_stats(self) -> Dict:
        """Retorna estatísticas do banco de dados."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Contar registros por tabela
        cursor.execute('SELECT COUNT(*) FROM crypto_data')
        data_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM symbol_metadata')
        symbols_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM saved_analyses')
        analyses_count = cursor.fetchone()[0]
        
        # Tamanho do arquivo
        file_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
        
        conn.close()
        
        return {
            'data_records': data_count,
            'symbols': symbols_count,
            'analyses': analyses_count,
            'file_size_mb': file_size / (1024 * 1024),
            'file_path': self.db_path
        }


if __name__ == "__main__":
    # Teste do sistema
    manager = CryptoDataManager()
    
    print("🔍 Testando busca de símbolos:")
    results = manager.search_symbols('BTC')
    for result in results:
        print(f"  {result}")
    
    print("\n📊 Buscando dados do Bitcoin:")
    btc_data = manager.fetch_crypto_data('BTC', '1m')
    print(f"  Dados obtidos: {len(btc_data)} registros")
    print(f"  Período: {btc_data.index.min()} até {btc_data.index.max()}")
    
    print("\n📈 Estatísticas do banco:")
    stats = manager.get_database_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
