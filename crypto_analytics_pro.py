"""
CryptoSignals - Dashboard SaaS Profissional
Plataforma premium de análise de criptomoedas com gráficos interativos.
Design moderno inspirado em produtos SaaS de alta qualidade.
"""

import sys
import os
import json
import base64
import io
from flask import Flask, render_template_string, request, jsonify
import threading
import webbrowser
from datetime import datetime, timedelta
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.utils
import pandas as pd
import numpy as np

# Adicionar diretório src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_manager import CryptoDataManager
from technical_analysis import TechnicalAnalyzer

# Inicializar Flask
app = Flask(__name__)
data_manager = CryptoDataManager()

# Cache global
cache = {
    'current_symbol': 'BTC',
    'current_period': '3m',
    'current_data': None,
    'current_analysis': None,
    'last_update': None
}

# Configurações do tema
THEME_CONFIG = {
    'primary_color': '#6366f1',      # Indigo moderno
    'secondary_color': '#8b5cf6',    # Purple
    'success_color': '#10b981',      # Emerald
    'danger_color': '#ef4444',       # Red
    'warning_color': '#f59e0b',      # Amber
    'dark_bg': '#0f172a',           # Slate 900
    'card_bg': '#1e293b',           # Slate 800
    'border_color': '#334155',       # Slate 700
    'text_primary': '#f8fafc',       # Slate 50
    'text_secondary': '#cbd5e1',     # Slate 300
    'bitcoin_color': '#f7931a'       # Bitcoin Orange
}

def create_candlestick_chart(data, indicators, symbol):
    """Cria gráfico de candlestick profissional."""
    # Usar apenas os últimos 100 pontos para performance
    df = data.tail(100).copy()
    
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        row_heights=[0.6, 0.2, 0.2],
        subplot_titles=(f'{symbol}/USDT - Análise Técnica', 'Volume', 'RSI')
    )
    
    # Candlestick principal
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['Open'],
            high=df['High'],
            low=df['Low'],
            close=df['Close'],
            name=f'{symbol}/USDT',
            increasing_line_color=THEME_CONFIG['success_color'],
            decreasing_line_color=THEME_CONFIG['danger_color'],
            increasing_fillcolor=THEME_CONFIG['success_color'],
            decreasing_fillcolor=THEME_CONFIG['danger_color']
        ),
        row=1, col=1
    )
    
    # Médias móveis
    if 'SMA_20' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['SMA_20'],
                mode='lines',
                name='SMA 20',
                line=dict(color=THEME_CONFIG['primary_color'], width=2),
                opacity=0.8
            ),
            row=1, col=1
        )
    
    if 'SMA_50' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['SMA_50'],
                mode='lines',
                name='SMA 50',
                line=dict(color=THEME_CONFIG['secondary_color'], width=2),
                opacity=0.8
            ),
            row=1, col=1
        )
    
    # Bollinger Bands
    if all(col in df.columns for col in ['BB_Upper', 'BB_Lower', 'BB_Middle']):
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['BB_Upper'],
                mode='lines',
                name='BB Superior',
                line=dict(color='rgba(128,128,128,0.3)', width=1),
                showlegend=False
            ),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['BB_Lower'],
                mode='lines',
                name='BB Inferior',
                line=dict(color='rgba(128,128,128,0.3)', width=1),
                fill='tonexty',
                fillcolor='rgba(128,128,128,0.1)',
                showlegend=False
            ),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['BB_Middle'],
                mode='lines',
                name='BB Média',
                line=dict(color='rgba(128,128,128,0.5)', width=1, dash='dash'),
                showlegend=False
            ),
            row=1, col=1
        )
    
    # Volume
    colors = ['rgba(16, 185, 129, 0.7)' if close >= open else 'rgba(239, 68, 68, 0.7)' 
              for close, open in zip(df['Close'], df['Open'])]
    
    fig.add_trace(
        go.Bar(
            x=df.index,
            y=df['Volume'],
            name='Volume',
            marker_color=colors,
            showlegend=False
        ),
        row=2, col=1
    )
    
    # RSI
    if 'RSI_14' in df.columns:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['RSI_14'],
                mode='lines',
                name='RSI(14)',
                line=dict(color=THEME_CONFIG['warning_color'], width=2),
                showlegend=False
            ),
            row=3, col=1
        )
        
        # Linhas de referência RSI
        fig.add_hline(y=70, line_dash="dash", line_color=THEME_CONFIG['danger_color'], 
                     opacity=0.7, row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color=THEME_CONFIG['success_color'], 
                     opacity=0.7, row=3, col=1)
        fig.add_hrect(y0=30, y1=70, fillcolor="rgba(128,128,128,0.1)", 
                     opacity=0.3, row=3, col=1)
    
    # Layout
    fig.update_layout(
        template='plotly_dark',
        paper_bgcolor=THEME_CONFIG['dark_bg'],
        plot_bgcolor=THEME_CONFIG['card_bg'],
        font=dict(color=THEME_CONFIG['text_primary'], size=12),
        height=800,
        margin=dict(l=0, r=0, t=40, b=0),
        xaxis_rangeslider_visible=False,
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    # Atualizar eixos
    fig.update_xaxes(
        gridcolor=THEME_CONFIG['border_color'],
        gridwidth=1,
        showgrid=True
    )
    fig.update_yaxes(
        gridcolor=THEME_CONFIG['border_color'],
        gridwidth=1,
        showgrid=True
    )
    
    # Configurar eixo Y do RSI
    fig.update_yaxes(range=[0, 100], row=3, col=1)
    
    return fig

def create_indicators_chart(data, indicators):
    """Cria gráfico de indicadores técnicos."""
    df = data.tail(100).copy()
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('MACD', 'Estocástico', 'Volume Profile', 'Momentum'),
        vertical_spacing=0.1,
        horizontal_spacing=0.1
    )
    
    # MACD
    if all(col in df.columns for col in ['MACD', 'MACD_Signal', 'MACD_Histogram']):
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['MACD'],
                mode='lines',
                name='MACD',
                line=dict(color=THEME_CONFIG['primary_color'], width=2)
            ),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['MACD_Signal'],
                mode='lines',
                name='Signal',
                line=dict(color=THEME_CONFIG['warning_color'], width=2)
            ),
            row=1, col=1
        )
        
        # Histograma MACD
        colors = [THEME_CONFIG['success_color'] if x >= 0 else THEME_CONFIG['danger_color'] 
                 for x in df['MACD_Histogram']]
        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df['MACD_Histogram'],
                name='Histogram',
                marker_color=colors,
                opacity=0.6
            ),
            row=1, col=1
        )
    
    # Estocástico
    if all(col in df.columns for col in ['Stoch_K', 'Stoch_D']):
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['Stoch_K'],
                mode='lines',
                name='%K',
                line=dict(color=THEME_CONFIG['primary_color'], width=2)
            ),
            row=1, col=2
        )
        
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['Stoch_D'],
                mode='lines',
                name='%D',
                line=dict(color=THEME_CONFIG['secondary_color'], width=2)
            ),
            row=1, col=2
        )
        
        # Linhas de referência
        fig.add_hline(y=80, line_dash="dash", line_color=THEME_CONFIG['danger_color'], 
                     opacity=0.5, row=1, col=2)
        fig.add_hline(y=20, line_dash="dash", line_color=THEME_CONFIG['success_color'], 
                     opacity=0.5, row=1, col=2)
    
    # Volume Profile (simplificado)
    if 'Volume' in df.columns:
        volume_ma = df['Volume'].rolling(window=20).mean()
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['Volume'],
                mode='lines',
                name='Volume',
                line=dict(color=THEME_CONFIG['warning_color'], width=1),
                opacity=0.7
            ),
            row=2, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=volume_ma,
                mode='lines',
                name='Volume MA',
                line=dict(color=THEME_CONFIG['primary_color'], width=2)
            ),
            row=2, col=1
        )
    
    # Momentum (Rate of Change)
    if 'Close' in df.columns:
        roc = df['Close'].pct_change(periods=10) * 100
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=roc,
                mode='lines',
                name='ROC(10)',
                line=dict(color=THEME_CONFIG['secondary_color'], width=2)
            ),
            row=2, col=2
        )
        
        fig.add_hline(y=0, line_dash="dash", line_color=THEME_CONFIG['text_secondary'], 
                     opacity=0.5, row=2, col=2)
    
    # Layout
    fig.update_layout(
        template='plotly_dark',
        paper_bgcolor=THEME_CONFIG['dark_bg'],
        plot_bgcolor=THEME_CONFIG['card_bg'],
        font=dict(color=THEME_CONFIG['text_primary'], size=10),
        height=600,
        margin=dict(l=0, r=0, t=40, b=0),
        showlegend=False
    )
    
    # Atualizar eixos
    fig.update_xaxes(
        gridcolor=THEME_CONFIG['border_color'],
        gridwidth=1,
        showgrid=True
    )
    fig.update_yaxes(
        gridcolor=THEME_CONFIG['border_color'],
        gridwidth=1,
        showgrid=True
    )
    
    return fig

def create_market_overview_chart(symbols_data):
    """Cria gráfico de overview do mercado."""
    if not symbols_data:
        return go.Figure()
    
    # Dados de exemplo para demonstração
    symbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT']
    prices = [108021.70, 2527.99, 0.75, 176.10, 4.54]
    changes = [-2.5, -1.8, -3.2, 0.8, -4.1]
    
    # Gráfico de barras horizontal
    colors = [THEME_CONFIG['success_color'] if change >= 0 else THEME_CONFIG['danger_color'] 
              for change in changes]
    
    fig = go.Figure(data=[
        go.Bar(
            y=symbols,
            x=changes,
            orientation='h',
            marker_color=colors,
            text=[f'{change:+.1f}%' for change in changes],
            textposition='auto',
        )
    ])
    
    fig.update_layout(
        title='Performance 24h - Top Cryptos',
        template='plotly_dark',
        paper_bgcolor=THEME_CONFIG['dark_bg'],
        plot_bgcolor=THEME_CONFIG['card_bg'],
        font=dict(color=THEME_CONFIG['text_primary'], size=12),
        height=300,
        margin=dict(l=0, r=0, t=40, b=0),
        xaxis_title='Variação (%)',
        yaxis_title='Criptomoedas'
    )
    
    fig.update_xaxes(
        gridcolor=THEME_CONFIG['border_color'],
        gridwidth=1,
        showgrid=True,
        zeroline=True,
        zerolinecolor=THEME_CONFIG['text_secondary']
    )
    
    return fig
