"""
Sistema de Análise de Sentimento
Análise de notícias, redes sociais e sentiment do mercado
"""

import requests
import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import sqlite3
import logging
from textblob import TextBlob
import feedparser
import time

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SentimentScore(Enum):
    VERY_NEGATIVE = -2
    NEGATIVE = -1
    NEUTRAL = 0
    POSITIVE = 1
    VERY_POSITIVE = 2

@dataclass
class NewsArticle:
    title: str
    content: str
    url: str
    source: str
    published_at: datetime
    sentiment_score: float
    sentiment_label: SentimentScore
    keywords: List[str]
    relevance_score: float

@dataclass
class SentimentAnalysis:
    symbol: str
    overall_sentiment: SentimentScore
    sentiment_score: float
    confidence: float
    news_count: int
    positive_news: int
    negative_news: int
    neutral_news: int
    trending_keywords: List[str]
    analysis_timestamp: datetime
    market_mood: str
    recommendation: str

class SentimentAnalyzer:
    def __init__(self, db_path: str = "sentiment_data.db"):
        self.db_path = db_path
        self.init_database()
        
        # APIs de notícias (configurar em produção)
        self.news_apis = {
            'newsapi': {
                'url': 'https://newsapi.org/v2/everything',
                'key': '',  # Configurar API key
                'enabled': False
            },
            'cryptocompare': {
                'url': 'https://min-api.cryptocompare.com/data/v2/news/',
                'key': '',  # Configurar API key
                'enabled': True
            }
        }
        
        # RSS feeds de notícias crypto
        self.rss_feeds = [
            'https://cointelegraph.com/rss',
            'https://coindesk.com/arc/outboundfeeds/rss/',
            'https://cryptonews.com/news/feed/',
            'https://decrypt.co/feed',
            'https://www.coinbase.com/blog/rss.xml'
        ]
        
        # Palavras-chave por símbolo
        self.symbol_keywords = {
            'BTC': ['bitcoin', 'btc', 'satoshi', 'lightning network', 'halving'],
            'ETH': ['ethereum', 'eth', 'vitalik', 'smart contract', 'defi', 'nft'],
            'ADA': ['cardano', 'ada', 'charles hoskinson', 'ouroboros', 'plutus'],
            'DOT': ['polkadot', 'dot', 'gavin wood', 'parachain', 'substrate'],
            'LINK': ['chainlink', 'link', 'oracle', 'sergey nazarov'],
            'SOL': ['solana', 'sol', 'anatoly yakovenko', 'proof of history'],
            'MATIC': ['polygon', 'matic', 'layer 2', 'scaling'],
            'AVAX': ['avalanche', 'avax', 'emin gun sirer', 'subnet']
        }
        
    def init_database(self):
        """Inicializa o banco de dados de sentiment"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabela de artigos de notícias
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS news_articles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    content TEXT,
                    url TEXT UNIQUE NOT NULL,
                    source TEXT NOT NULL,
                    published_at TIMESTAMP NOT NULL,
                    sentiment_score REAL NOT NULL,
                    sentiment_label TEXT NOT NULL,
                    keywords TEXT NOT NULL,
                    relevance_score REAL NOT NULL,
                    symbol TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de análises de sentiment
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sentiment_analyses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol TEXT NOT NULL,
                    overall_sentiment TEXT NOT NULL,
                    sentiment_score REAL NOT NULL,
                    confidence REAL NOT NULL,
                    news_count INTEGER NOT NULL,
                    positive_news INTEGER NOT NULL,
                    negative_news INTEGER NOT NULL,
                    neutral_news INTEGER NOT NULL,
                    trending_keywords TEXT NOT NULL,
                    market_mood TEXT NOT NULL,
                    recommendation TEXT NOT NULL,
                    analysis_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de cache de sentiment
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sentiment_cache (
                    symbol TEXT PRIMARY KEY,
                    sentiment_data TEXT NOT NULL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Banco de dados de sentiment inicializado")
            
        except Exception as e:
            logger.error(f"Erro ao inicializar banco de sentiment: {e}")
    
    def analyze_text_sentiment(self, text: str) -> Tuple[float, SentimentScore]:
        """Analisa o sentiment de um texto usando TextBlob"""
        try:
            # Limpar texto
            clean_text = self.clean_text(text)
            
            # Análise com TextBlob
            blob = TextBlob(clean_text)
            polarity = blob.sentiment.polarity  # -1 a 1
            
            # Converter para nossa escala
            if polarity >= 0.5:
                sentiment_label = SentimentScore.VERY_POSITIVE
            elif polarity >= 0.1:
                sentiment_label = SentimentScore.POSITIVE
            elif polarity <= -0.5:
                sentiment_label = SentimentScore.VERY_NEGATIVE
            elif polarity <= -0.1:
                sentiment_label = SentimentScore.NEGATIVE
            else:
                sentiment_label = SentimentScore.NEUTRAL
            
            return polarity, sentiment_label
            
        except Exception as e:
            logger.error(f"Erro na análise de sentiment: {e}")
            return 0.0, SentimentScore.NEUTRAL
    
    def clean_text(self, text: str) -> str:
        """Limpa e normaliza texto para análise"""
        # Remover URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remover caracteres especiais
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Remover espaços extras
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text.lower()
    
    def extract_keywords(self, text: str, symbol: str) -> List[str]:
        """Extrai palavras-chave relevantes do texto"""
        keywords = []
        text_lower = text.lower()
        
        # Palavras-chave específicas do símbolo
        if symbol in self.symbol_keywords:
            for keyword in self.symbol_keywords[symbol]:
                if keyword in text_lower:
                    keywords.append(keyword)
        
        # Palavras-chave gerais de crypto
        crypto_keywords = [
            'bull', 'bear', 'pump', 'dump', 'moon', 'crash', 'rally', 'dip',
            'hodl', 'fomo', 'fud', 'whale', 'adoption', 'regulation', 'etf',
            'institutional', 'retail', 'volume', 'breakout', 'support', 'resistance'
        ]
        
        for keyword in crypto_keywords:
            if keyword in text_lower:
                keywords.append(keyword)
        
        return list(set(keywords))  # Remover duplicatas
    
    def calculate_relevance_score(self, article: str, symbol: str) -> float:
        """Calcula score de relevância do artigo para o símbolo"""
        score = 0.0
        text_lower = article.lower()
        
        # Score base por menção do símbolo
        symbol_mentions = text_lower.count(symbol.lower())
        score += symbol_mentions * 0.3
        
        # Score por palavras-chave específicas
        if symbol in self.symbol_keywords:
            for keyword in self.symbol_keywords[symbol]:
                if keyword in text_lower:
                    score += 0.2
        
        # Score por palavras-chave gerais
        crypto_keywords = ['crypto', 'blockchain', 'bitcoin', 'ethereum', 'altcoin']
        for keyword in crypto_keywords:
            if keyword in text_lower:
                score += 0.1
        
        # Normalizar score (0-1)
        return min(score, 1.0)
    
    def fetch_news_from_rss(self, symbol: str, hours_back: int = 24) -> List[NewsArticle]:
        """Busca notícias dos feeds RSS"""
        articles = []
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        for feed_url in self.rss_feeds:
            try:
                feed = feedparser.parse(feed_url)
                
                for entry in feed.entries:
                    # Verificar data
                    try:
                        published = datetime(*entry.published_parsed[:6])
                        if published < cutoff_time:
                            continue
                    except:
                        published = datetime.now()
                    
                    # Extrair conteúdo
                    title = entry.get('title', '')
                    content = entry.get('summary', '') or entry.get('description', '')
                    url = entry.get('link', '')
                    source = feed.feed.get('title', 'Unknown')
                    
                    # Verificar relevância
                    full_text = f"{title} {content}"
                    relevance = self.calculate_relevance_score(full_text, symbol)
                    
                    if relevance > 0.1:  # Filtro de relevância mínima
                        # Análise de sentiment
                        sentiment_score, sentiment_label = self.analyze_text_sentiment(full_text)
                        
                        # Extrair keywords
                        keywords = self.extract_keywords(full_text, symbol)
                        
                        article = NewsArticle(
                            title=title,
                            content=content,
                            url=url,
                            source=source,
                            published_at=published,
                            sentiment_score=sentiment_score,
                            sentiment_label=sentiment_label,
                            keywords=keywords,
                            relevance_score=relevance
                        )
                        
                        articles.append(article)
                
                # Rate limiting
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Erro ao buscar RSS {feed_url}: {e}")
                continue
        
        return articles
    
    def fetch_news_from_api(self, symbol: str, hours_back: int = 24) -> List[NewsArticle]:
        """Busca notícias das APIs"""
        articles = []
        
        # CryptoCompare API
        if self.news_apis['cryptocompare']['enabled']:
            try:
                url = self.news_apis['cryptocompare']['url']
                params = {
                    'lang': 'EN',
                    'sortOrder': 'latest',
                    'categories': f'{symbol},Blockchain,Mining,Trading'
                }
                
                response = requests.get(url, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    for item in data.get('Data', []):
                        # Verificar data
                        published = datetime.fromtimestamp(item.get('published_on', 0))
                        cutoff_time = datetime.now() - timedelta(hours=hours_back)
                        
                        if published < cutoff_time:
                            continue
                        
                        title = item.get('title', '')
                        content = item.get('body', '')
                        url = item.get('url', '')
                        source = item.get('source_info', {}).get('name', 'CryptoCompare')
                        
                        # Verificar relevância
                        full_text = f"{title} {content}"
                        relevance = self.calculate_relevance_score(full_text, symbol)
                        
                        if relevance > 0.1:
                            # Análise de sentiment
                            sentiment_score, sentiment_label = self.analyze_text_sentiment(full_text)
                            
                            # Extrair keywords
                            keywords = self.extract_keywords(full_text, symbol)
                            
                            article = NewsArticle(
                                title=title,
                                content=content,
                                url=url,
                                source=source,
                                published_at=published,
                                sentiment_score=sentiment_score,
                                sentiment_label=sentiment_label,
                                keywords=keywords,
                                relevance_score=relevance
                            )
                            
                            articles.append(article)
                
            except Exception as e:
                logger.error(f"Erro ao buscar API CryptoCompare: {e}")
        
        return articles
    
    def save_articles(self, articles: List[NewsArticle], symbol: str):
        """Salva artigos no banco de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for article in articles:
                cursor.execute('''
                    INSERT OR IGNORE INTO news_articles 
                    (title, content, url, source, published_at, sentiment_score, 
                     sentiment_label, keywords, relevance_score, symbol)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    article.title,
                    article.content,
                    article.url,
                    article.source,
                    article.published_at,
                    article.sentiment_score,
                    article.sentiment_label.name,
                    json.dumps(article.keywords),
                    article.relevance_score,
                    symbol
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Salvos {len(articles)} artigos para {symbol}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar artigos: {e}")
    
    def analyze_symbol_sentiment(self, symbol: str, hours_back: int = 24) -> SentimentAnalysis:
        """Analisa o sentiment geral de um símbolo"""
        try:
            # Buscar notícias
            rss_articles = self.fetch_news_from_rss(symbol, hours_back)
            api_articles = self.fetch_news_from_api(symbol, hours_back)
            
            all_articles = rss_articles + api_articles
            
            # Salvar artigos
            if all_articles:
                self.save_articles(all_articles, symbol)
            
            # Buscar artigos do banco (incluindo histórico)
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            cursor.execute('''
                SELECT sentiment_score, sentiment_label, keywords, relevance_score
                FROM news_articles 
                WHERE symbol = ? AND published_at >= ?
                ORDER BY published_at DESC
            ''', (symbol, cutoff_time))
            
            db_articles = cursor.fetchall()
            conn.close()
            
            if not db_articles:
                # Retornar análise neutra se não há dados
                return SentimentAnalysis(
                    symbol=symbol,
                    overall_sentiment=SentimentScore.NEUTRAL,
                    sentiment_score=0.0,
                    confidence=0.0,
                    news_count=0,
                    positive_news=0,
                    negative_news=0,
                    neutral_news=0,
                    trending_keywords=[],
                    analysis_timestamp=datetime.now(),
                    market_mood="Sem dados suficientes",
                    recommendation="Aguardar mais informações"
                )
            
            # Calcular métricas
            total_articles = len(db_articles)
            weighted_sentiment = 0.0
            total_weight = 0.0
            
            positive_count = 0
            negative_count = 0
            neutral_count = 0
            
            all_keywords = []
            
            for article in db_articles:
                sentiment_score = article[0]
                sentiment_label = article[1]
                keywords = json.loads(article[2])
                relevance = article[3]
                
                # Peso baseado na relevância
                weight = relevance
                weighted_sentiment += sentiment_score * weight
                total_weight += weight
                
                # Contar por categoria
                if sentiment_score > 0.1:
                    positive_count += 1
                elif sentiment_score < -0.1:
                    negative_count += 1
                else:
                    neutral_count += 1
                
                all_keywords.extend(keywords)
            
            # Sentiment médio ponderado
            avg_sentiment = weighted_sentiment / total_weight if total_weight > 0 else 0.0
            
            # Determinar sentiment geral
            if avg_sentiment >= 0.3:
                overall_sentiment = SentimentScore.VERY_POSITIVE
            elif avg_sentiment >= 0.1:
                overall_sentiment = SentimentScore.POSITIVE
            elif avg_sentiment <= -0.3:
                overall_sentiment = SentimentScore.VERY_NEGATIVE
            elif avg_sentiment <= -0.1:
                overall_sentiment = SentimentScore.NEGATIVE
            else:
                overall_sentiment = SentimentScore.NEUTRAL
            
            # Calcular confiança baseada no número de artigos
            confidence = min(total_articles / 10.0, 1.0)  # Max confiança com 10+ artigos
            
            # Keywords mais frequentes
            keyword_counts = {}
            for keyword in all_keywords:
                keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
            
            trending_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            trending_keywords = [kw[0] for kw in trending_keywords]
            
            # Determinar mood do mercado
            market_mood = self.determine_market_mood(overall_sentiment, positive_count, negative_count, trending_keywords)
            
            # Gerar recomendação
            recommendation = self.generate_recommendation(overall_sentiment, avg_sentiment, confidence)
            
            analysis = SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                sentiment_score=avg_sentiment,
                confidence=confidence,
                news_count=total_articles,
                positive_news=positive_count,
                negative_news=negative_count,
                neutral_news=neutral_count,
                trending_keywords=trending_keywords,
                analysis_timestamp=datetime.now(),
                market_mood=market_mood,
                recommendation=recommendation
            )
            
            # Salvar análise
            self.save_sentiment_analysis(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Erro na análise de sentiment: {e}")
            return SentimentAnalysis(
                symbol=symbol,
                overall_sentiment=SentimentScore.NEUTRAL,
                sentiment_score=0.0,
                confidence=0.0,
                news_count=0,
                positive_news=0,
                negative_news=0,
                neutral_news=0,
                trending_keywords=[],
                analysis_timestamp=datetime.now(),
                market_mood="Erro na análise",
                recommendation="Verificar dados manualmente"
            )
    
    def determine_market_mood(self, sentiment: SentimentScore, positive: int, negative: int, keywords: List[str]) -> str:
        """Determina o mood geral do mercado"""
        if sentiment == SentimentScore.VERY_POSITIVE:
            if 'bull' in keywords or 'moon' in keywords:
                return "Extremamente Otimista - Bull Market"
            return "Muito Positivo - Tendência de Alta"
        elif sentiment == SentimentScore.POSITIVE:
            return "Positivo - Sentimento Favorável"
        elif sentiment == SentimentScore.VERY_NEGATIVE:
            if 'bear' in keywords or 'crash' in keywords:
                return "Extremamente Pessimista - Bear Market"
            return "Muito Negativo - Tendência de Baixa"
        elif sentiment == SentimentScore.NEGATIVE:
            return "Negativo - Cautela Recomendada"
        else:
            return "Neutro - Mercado Indeciso"
    
    def generate_recommendation(self, sentiment: SentimentScore, score: float, confidence: float) -> str:
        """Gera recomendação baseada no sentiment"""
        if confidence < 0.3:
            return "Dados insuficientes - Aguardar mais informações"
        
        if sentiment == SentimentScore.VERY_POSITIVE and confidence > 0.7:
            return "COMPRA FORTE - Sentiment muito positivo com alta confiança"
        elif sentiment == SentimentScore.POSITIVE:
            return "COMPRA - Sentiment positivo, considerar entrada"
        elif sentiment == SentimentScore.VERY_NEGATIVE and confidence > 0.7:
            return "VENDA FORTE - Sentiment muito negativo com alta confiança"
        elif sentiment == SentimentScore.NEGATIVE:
            return "VENDA - Sentiment negativo, considerar saída"
        else:
            return "NEUTRO - Aguardar definição de tendência"
    
    def save_sentiment_analysis(self, analysis: SentimentAnalysis):
        """Salva análise de sentiment no banco"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO sentiment_analyses 
                (symbol, overall_sentiment, sentiment_score, confidence, news_count,
                 positive_news, negative_news, neutral_news, trending_keywords,
                 market_mood, recommendation)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                analysis.symbol,
                analysis.overall_sentiment.name,
                analysis.sentiment_score,
                analysis.confidence,
                analysis.news_count,
                analysis.positive_news,
                analysis.negative_news,
                analysis.neutral_news,
                json.dumps(analysis.trending_keywords),
                analysis.market_mood,
                analysis.recommendation
            ))
            
            # Atualizar cache
            cursor.execute('''
                INSERT OR REPLACE INTO sentiment_cache (symbol, sentiment_data)
                VALUES (?, ?)
            ''', (analysis.symbol, json.dumps({
                'overall_sentiment': analysis.overall_sentiment.name,
                'sentiment_score': analysis.sentiment_score,
                'confidence': analysis.confidence,
                'market_mood': analysis.market_mood,
                'recommendation': analysis.recommendation,
                'last_updated': analysis.analysis_timestamp.isoformat()
            })))
            
            conn.commit()
            conn.close()
            
            logger.info(f"Análise de sentiment salva para {analysis.symbol}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar análise: {e}")
    
    def get_cached_sentiment(self, symbol: str, max_age_hours: int = 1) -> Optional[Dict]:
        """Busca sentiment do cache se ainda válido"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT sentiment_data, last_updated 
                FROM sentiment_cache 
                WHERE symbol = ?
            ''', (symbol,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                sentiment_data = json.loads(result[0])
                last_updated = datetime.fromisoformat(result[1])
                
                # Verificar se ainda é válido
                if datetime.now() - last_updated < timedelta(hours=max_age_hours):
                    return sentiment_data
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao buscar cache: {e}")
            return None

# Instância global
sentiment_analyzer = SentimentAnalyzer()
