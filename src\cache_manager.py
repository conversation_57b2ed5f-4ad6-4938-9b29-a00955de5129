"""
Sistema de Cache Avançado - CryptoSignals
Cache Redis com fallback para memória local
"""

import json
import time
import hashlib
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
from dataclasses import dataclass, asdict
import pickle
import threading
from collections import defaultdict

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("⚠️  Redis não disponível, usando cache em memória")

@dataclass
class CacheEntry:
    """Entrada do cache"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None

class CacheManager:
    """Gerenciador de cache inteligente"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0", 
                 default_ttl: int = 3600, max_memory_entries: int = 1000):
        self.default_ttl = default_ttl
        self.max_memory_entries = max_memory_entries
        self.redis_client = None
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0
        }
        self.lock = threading.Lock()
        
        # Tentar conectar ao Redis
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                self.redis_client.ping()
                print("✅ Cache Redis conectado com sucesso")
            except Exception as e:
                print(f"⚠️  Erro ao conectar Redis: {e}")
                self.redis_client = None
        
        # Iniciar limpeza automática
        self._start_cleanup_thread()
    
    def _generate_key(self, namespace: str, key: str, **kwargs) -> str:
        """Gera chave única para o cache"""
        key_data = f"{namespace}:{key}"
        if kwargs:
            # Adicionar parâmetros à chave
            params = "&".join([f"{k}={v}" for k, v in sorted(kwargs.items())])
            key_data += f"?{params}"
        
        # Hash para chaves muito longas
        if len(key_data) > 200:
            key_hash = hashlib.md5(key_data.encode()).hexdigest()
            return f"{namespace}:{key_hash}"
        
        return key_data
    
    def get(self, namespace: str, key: str, **kwargs) -> Optional[Any]:
        """Recupera valor do cache"""
        cache_key = self._generate_key(namespace, key, **kwargs)
        
        # Tentar Redis primeiro
        if self.redis_client:
            try:
                value = self.redis_client.get(cache_key)
                if value is not None:
                    self.cache_stats['hits'] += 1
                    # Incrementar contador de acesso
                    self.redis_client.incr(f"{cache_key}:access_count")
                    return json.loads(value)
            except Exception as e:
                print(f"Erro no Redis get: {e}")
        
        # Fallback para cache em memória
        with self.lock:
            if cache_key in self.memory_cache:
                entry = self.memory_cache[cache_key]
                
                # Verificar expiração
                if entry.expires_at and datetime.now() > entry.expires_at:
                    del self.memory_cache[cache_key]
                    self.cache_stats['misses'] += 1
                    return None
                
                # Atualizar estatísticas de acesso
                entry.access_count += 1
                entry.last_accessed = datetime.now()
                self.cache_stats['hits'] += 1
                return entry.value
        
        self.cache_stats['misses'] += 1
        return None
    
    def set(self, namespace: str, key: str, value: Any, ttl: Optional[int] = None, **kwargs):
        """Armazena valor no cache"""
        cache_key = self._generate_key(namespace, key, **kwargs)
        ttl = ttl or self.default_ttl
        expires_at = datetime.now() + timedelta(seconds=ttl)
        
        # Tentar Redis primeiro
        if self.redis_client:
            try:
                serialized_value = json.dumps(value, default=str)
                self.redis_client.setex(cache_key, ttl, serialized_value)
                self.redis_client.setex(f"{cache_key}:access_count", ttl, 0)
                self.cache_stats['sets'] += 1
                return
            except Exception as e:
                print(f"Erro no Redis set: {e}")
        
        # Fallback para cache em memória
        with self.lock:
            # Verificar limite de memória
            if len(self.memory_cache) >= self.max_memory_entries:
                self._evict_lru()
            
            entry = CacheEntry(
                key=cache_key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at
            )
            
            self.memory_cache[cache_key] = entry
            self.cache_stats['sets'] += 1
    
    def delete(self, namespace: str, key: str, **kwargs):
        """Remove valor do cache"""
        cache_key = self._generate_key(namespace, key, **kwargs)
        
        # Remover do Redis
        if self.redis_client:
            try:
                self.redis_client.delete(cache_key)
                self.redis_client.delete(f"{cache_key}:access_count")
            except Exception as e:
                print(f"Erro no Redis delete: {e}")
        
        # Remover da memória
        with self.lock:
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]
                self.cache_stats['deletes'] += 1
    
    def clear_namespace(self, namespace: str):
        """Limpa todos os valores de um namespace"""
        pattern = f"{namespace}:*"
        
        # Limpar Redis
        if self.redis_client:
            try:
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
            except Exception as e:
                print(f"Erro ao limpar namespace no Redis: {e}")
        
        # Limpar memória
        with self.lock:
            keys_to_delete = [k for k in self.memory_cache.keys() if k.startswith(f"{namespace}:")]
            for key in keys_to_delete:
                del self.memory_cache[key]
    
    def _evict_lru(self):
        """Remove entrada menos recentemente usada"""
        if not self.memory_cache:
            return
        
        # Encontrar entrada menos acessada
        lru_key = min(
            self.memory_cache.keys(),
            key=lambda k: (
                self.memory_cache[k].last_accessed or self.memory_cache[k].created_at,
                self.memory_cache[k].access_count
            )
        )
        
        del self.memory_cache[lru_key]
        self.cache_stats['evictions'] += 1
    
    def _start_cleanup_thread(self):
        """Inicia thread de limpeza automática"""
        def cleanup():
            while True:
                try:
                    with self.lock:
                        now = datetime.now()
                        expired_keys = [
                            k for k, entry in self.memory_cache.items()
                            if entry.expires_at and now > entry.expires_at
                        ]
                        
                        for key in expired_keys:
                            del self.memory_cache[key]
                    
                    time.sleep(300)  # Limpeza a cada 5 minutos
                except Exception as e:
                    print(f"Erro na limpeza do cache: {e}")
                    time.sleep(300)
        
        cleanup_thread = threading.Thread(target=cleanup, daemon=True)
        cleanup_thread.start()
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do cache"""
        total_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hit_rate': hit_rate,
            'total_requests': total_requests,
            'memory_entries': len(self.memory_cache),
            'redis_available': self.redis_client is not None,
            **self.cache_stats
        }
    
    def get_cache_info(self, namespace: Optional[str] = None) -> Dict[str, Any]:
        """Retorna informações detalhadas do cache"""
        info = {
            'stats': self.get_stats(),
            'namespaces': defaultdict(int)
        }
        
        # Contar entradas por namespace
        for key in self.memory_cache.keys():
            ns = key.split(':')[0]
            info['namespaces'][ns] += 1
        
        if namespace:
            # Informações específicas do namespace
            namespace_keys = [k for k in self.memory_cache.keys() if k.startswith(f"{namespace}:")]
            info['namespace_detail'] = {
                'key_count': len(namespace_keys),
                'keys': namespace_keys[:10]  # Primeiras 10 chaves
            }
        
        return info

# Decorador para cache automático
def cached(namespace: str, ttl: int = 3600, key_func=None):
    """Decorator para cache automático de funções"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Gerar chave do cache
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Usar nome da função e argumentos
                args_str = "_".join([str(arg) for arg in args])
                kwargs_str = "_".join([f"{k}_{v}" for k, v in kwargs.items()])
                cache_key = f"{func.__name__}_{args_str}_{kwargs_str}"
            
            # Tentar obter do cache
            cached_result = cache_manager.get(namespace, cache_key)
            if cached_result is not None:
                return cached_result
            
            # Executar função e cachear resultado
            result = func(*args, **kwargs)
            cache_manager.set(namespace, cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator

# Instância global do cache
cache_manager = CacheManager()

# Funções de conveniência para diferentes tipos de dados
class CryptoCache:
    """Cache específico para dados de criptomoedas"""
    
    @staticmethod
    def get_price_data(symbol: str, period: str) -> Optional[Any]:
        """Recupera dados de preço do cache"""
        return cache_manager.get('price_data', f"{symbol}_{period}")
    
    @staticmethod
    def set_price_data(symbol: str, period: str, data: Any, ttl: int = 300):
        """Armazena dados de preço no cache"""
        cache_manager.set('price_data', f"{symbol}_{period}", data, ttl)
    
    @staticmethod
    def get_analysis(symbol: str, period: str) -> Optional[Any]:
        """Recupera análise técnica do cache"""
        return cache_manager.get('analysis', f"{symbol}_{period}")
    
    @staticmethod
    def set_analysis(symbol: str, period: str, analysis: Any, ttl: int = 600):
        """Armazena análise técnica no cache"""
        cache_manager.set('analysis', f"{symbol}_{period}", analysis, ttl)
    
    @staticmethod
    def get_sentiment(symbol: str) -> Optional[Any]:
        """Recupera análise de sentimento do cache"""
        return cache_manager.get('sentiment', symbol)
    
    @staticmethod
    def set_sentiment(symbol: str, sentiment: Any, ttl: int = 1800):
        """Armazena análise de sentimento no cache"""
        cache_manager.set('sentiment', symbol, sentiment, ttl)

class UserCache:
    """Cache específico para dados de usuários"""
    
    @staticmethod
    def get_user_data(user_id: str) -> Optional[Any]:
        """Recupera dados do usuário do cache"""
        return cache_manager.get('users', user_id)
    
    @staticmethod
    def set_user_data(user_id: str, user_data: Any, ttl: int = 1800):
        """Armazena dados do usuário no cache"""
        cache_manager.set('users', user_id, user_data, ttl)
    
    @staticmethod
    def invalidate_user(user_id: str):
        """Invalida cache do usuário"""
        cache_manager.delete('users', user_id)
    
    @staticmethod
    def get_user_limits(user_id: str) -> Optional[Any]:
        """Recupera limites do usuário do cache"""
        return cache_manager.get('user_limits', user_id)
    
    @staticmethod
    def set_user_limits(user_id: str, limits: Any, ttl: int = 3600):
        """Armazena limites do usuário no cache"""
        cache_manager.set('user_limits', user_id, limits, ttl)
